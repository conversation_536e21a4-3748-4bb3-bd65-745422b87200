#!/usr/bin/env python3
"""
Settings.toml配置值验证测试
直接测试配置文件中的具体配置值是否符合Feature 1的要求

测试覆盖Feature 1的所有配置修改：
- F1.T1.S1: TUSHARE配置启用
- F1.T1.S2: 个人开发者内存配置
- F1.T1.S3: 跨平台路径配置优化  
- F1.T1.S4: 多级积分预算控制配置
"""

import toml
import pytest
from pathlib import Path


class TestSettingsConfigValues:
    """Settings配置值验证测试类"""
    
    @classmethod
    def setup_class(cls):
        """测试类设置 - 加载实际配置文件"""
        config_path = Path("config/settings.toml")
        assert config_path.exists(), "配置文件config/settings.toml不存在"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            cls.config = toml.load(f)
    
    def test_tushare_config_enabled(self):
        """测试TUSHARE配置已启用 - F1.T1.S1"""
        # 验证TUSHARE配置段存在
        assert 'datasources' in self.config, "datasources配置段不存在"
        assert 'api' in self.config['datasources'], "api配置段不存在"
        assert 'tushare' in self.config['datasources']['api'], "tushare配置段不存在"
        
        tushare_config = self.config['datasources']['api']['tushare']
        
        # 验证TUSHARE已启用
        assert tushare_config.get('enabled') is True, \
            f"TUSHARE未启用，当前值: {tushare_config.get('enabled')}"
        
        # 验证基本配置正确
        assert tushare_config.get('name') == "Tushare Pro", \
            f"TUSHARE名称不正确: {tushare_config.get('name')}"
        
        assert tushare_config.get('base_url') == "https://tushare.pro", \
            f"TUSHARE URL不正确: {tushare_config.get('base_url')}"
        
        # 验证token使用环境变量
        assert tushare_config.get('token') == "${TUSHARE_TOKEN}", \
            f"Token配置不正确: {tushare_config.get('token')}"
        
        # 验证频率限制配置
        assert tushare_config.get('rate_limit') == 200, \
            f"频率限制不正确: {tushare_config.get('rate_limit')}"
        
        assert tushare_config.get('points_per_minute') == 2000, \
            f"每分钟积分不正确: {tushare_config.get('points_per_minute')}"
    
    def test_tushare_budget_config(self):
        """测试TUSHARE积分预算配置 - F1.T1.S4"""
        tushare_config = self.config['datasources']['api']['tushare']
        
        # 验证预算配置存在
        assert 'budget' in tushare_config, "积分预算配置不存在"
        budget_config = tushare_config['budget']
        
        # 验证基本预算配置
        assert budget_config.get('total_points') == 2100, \
            f"总积分配置不正确: {budget_config.get('total_points')}"
        
        assert budget_config.get('daily_limit') == 50, \
            f"每日限制不正确: {budget_config.get('daily_limit')}"
        
        assert budget_config.get('emergency_reserve') == 200, \
            f"紧急预留不正确: {budget_config.get('emergency_reserve')}"
        
        # 验证预算模式配置
        assert 'modes' in budget_config, "预算模式配置不存在"
        modes = budget_config['modes']
        
        # 验证保守模式
        assert 'conservative' in modes, "保守模式配置不存在"
        conservative = modes['conservative']
        assert conservative.get('daily_limit') == 30, \
            f"保守模式每日限制不正确: {conservative.get('daily_limit')}"
        assert conservative.get('priority') == "期货主力合约", \
            f"保守模式优先级不正确: {conservative.get('priority')}"
        
        # 验证正常模式
        assert 'normal' in modes, "正常模式配置不存在"
        normal = modes['normal']
        assert normal.get('daily_limit') == 50, \
            f"正常模式每日限制不正确: {normal.get('daily_limit')}"
        assert normal.get('priority') == "期货>股票>其他", \
            f"正常模式优先级不正确: {normal.get('priority')}"
        
        # 验证激进模式
        assert 'aggressive' in modes, "激进模式配置不存在"
        aggressive = modes['aggressive']
        assert aggressive.get('daily_limit') == 80, \
            f"激进模式每日限制不正确: {aggressive.get('daily_limit')}"
        assert aggressive.get('priority') == "全量数据", \
            f"激进模式优先级不正确: {aggressive.get('priority')}"
        
        # 验证监控配置
        assert 'monitoring' in tushare_config, "监控配置不存在"
        monitoring = tushare_config['monitoring']
        
        assert monitoring.get('enable_tracking') is True, \
            f"积分追踪未启用: {monitoring.get('enable_tracking')}"
        
        assert monitoring.get('efficiency_target') == 0.95, \
            f"效率目标不正确: {monitoring.get('efficiency_target')}"
        
        assert monitoring.get('warning_threshold') == 0.8, \
            f"警告阈值不正确: {monitoring.get('warning_threshold')}"
        
        assert monitoring.get('auto_downgrade') is True, \
            f"自动降级未启用: {monitoring.get('auto_downgrade')}"
    
    def test_personal_dev_memory_config(self):
        """测试个人开发者内存配置 - F1.T1.S2"""
        # 验证dev环境性能配置
        assert 'dev' in self.config, "dev环境配置不存在"
        assert 'performance' in self.config['dev'], "dev性能配置不存在"
        
        perf_config = self.config['dev']['performance']
        
        # 验证个人开发者友好的内存限制
        assert perf_config.get('memory_limit_mb') == 1536, \
            f"内存限制不正确，应为1536MB: {perf_config.get('memory_limit_mb')}"
        
        # 验证启动时间目标
        assert perf_config.get('startup_target_seconds') == 2, \
            f"启动时间目标不正确: {perf_config.get('startup_target_seconds')}"
        
        # 验证个人开发者模式启用
        assert perf_config.get('personal_dev_mode') is True, \
            f"个人开发者模式未启用: {perf_config.get('personal_dev_mode')}"
        
        # 验证内存管理配置
        assert perf_config.get('memory_warning_threshold_mb') == 1200, \
            f"内存警告阈值不正确: {perf_config.get('memory_warning_threshold_mb')}"
        
        assert perf_config.get('auto_gc_trigger_mb') == 50, \
            f"自动GC触发不正确: {perf_config.get('auto_gc_trigger_mb')}"
        
        assert perf_config.get('idle_memory_limit_mb') == 800, \
            f"空闲内存限制不正确: {perf_config.get('idle_memory_limit_mb')}"
    
    def test_cross_platform_config(self):
        """测试跨平台配置优化 - F1.T1.S3"""
        # 验证平台配置存在
        assert 'platform' in self.config, "platform配置段不存在"
        platform_config = self.config['platform']
        
        # 验证基本平台配置
        assert platform_config.get('auto_detect') is True, \
            f"平台自动检测未启用: {platform_config.get('auto_detect')}"
        
        assert platform_config.get('normalize_paths') is True, \
            f"路径标准化未启用: {platform_config.get('normalize_paths')}"
        
        assert platform_config.get('auto_create_dirs') is True, \
            f"自动创建目录未启用: {platform_config.get('auto_create_dirs')}"
        
        # 验证支持的平台列表
        supported_platforms = platform_config.get('supported_platforms', [])
        expected_platforms = ["windows", "darwin", "linux"]
        for platform in expected_platforms:
            assert platform in supported_platforms, \
                f"平台{platform}不在支持列表中: {supported_platforms}"
        
        # 验证个人开发者跨平台优化配置
        assert 'personal_dev' in platform_config, "个人开发者配置不存在"
        personal_dev = platform_config['personal_dev']
        
        # 验证环境变量配置
        expected_env_vars = ["AQUA_CONFIG_DIR", "AQUA_DATA_DIR", "TUSHARE_TOKEN"]
        env_vars = personal_dev.get('env_vars', [])
        for env_var in expected_env_vars:
            assert env_var in env_vars, \
                f"环境变量{env_var}不在配置列表中: {env_vars}"
        
        # 验证编码配置
        assert personal_dev.get('default_encoding') == "utf-8", \
            f"默认编码不正确: {personal_dev.get('default_encoding')}"
        
        assert personal_dev.get('utf8_path_support') is True, \
            f"UTF-8路径支持未启用: {personal_dev.get('utf8_path_support')}"
        
        assert personal_dev.get('auto_path_separator') is True, \
            f"自动路径分隔符未启用: {personal_dev.get('auto_path_separator')}"
        
        assert personal_dev.get('check_write_permissions') is True, \
            f"写权限检查未启用: {personal_dev.get('check_write_permissions')}"
    
    def test_config_structure_integrity(self):
        """测试配置文件结构完整性"""
        # 验证必要的顶级配置段存在
        required_sections = ['app', 'platform', 'datasources', 'dev']
        for section in required_sections:
            assert section in self.config, f"必要配置段{section}不存在"
        
        # 验证app基本信息
        app_config = self.config['app']
        assert app_config.get('name') == "AQUA", \
            f"应用名称不正确: {app_config.get('name')}"
        
        # 验证版本信息存在
        assert 'version' in app_config, "版本信息缺失"
        assert 'environments' in app_config, "环境列表缺失"
        
        # 验证环境列表包含所需环境
        environments = app_config.get('environments', [])
        required_envs = ['dev', 'test', 'prod']
        for env in required_envs:
            assert env in environments, f"环境{env}不在列表中: {environments}"
    
    def test_feature1_config_completeness(self):
        """测试Feature 1配置完整性"""
        # 验证所有Feature 1任务的配置都已完成
        
        # F1.T1.S1: TUSHARE启用
        tushare_enabled = self.config['datasources']['api']['tushare']['enabled']
        assert tushare_enabled is True, "F1.T1.S1: TUSHARE未启用"
        
        # F1.T1.S2: 个人开发者内存配置
        memory_limit = self.config['dev']['performance']['memory_limit_mb']
        assert memory_limit == 1536, f"F1.T1.S2: 内存限制不正确: {memory_limit}"
        
        # F1.T1.S3: 跨平台配置
        personal_dev_exists = 'personal_dev' in self.config['platform']
        assert personal_dev_exists, "F1.T1.S3: 跨平台个人开发者配置缺失"
        
        # F1.T1.S4: 积分预算配置
        budget_exists = 'budget' in self.config['datasources']['api']['tushare']
        assert budget_exists, "F1.T1.S4: 积分预算配置缺失"
        
        # 验证所有配置都符合个人开发者友好的目标
        print("✅ Feature 1所有配置修改验证通过")


def test_config_file_loading():
    """测试配置文件加载功能"""
    config_path = Path("config/settings.toml")
    assert config_path.exists(), "配置文件不存在"
    
    # 验证TOML格式正确
    with open(config_path, 'r', encoding='utf-8') as f:
        config = toml.load(f)
    
    assert isinstance(config, dict), "配置文件格式不正确"
    assert len(config) > 0, "配置文件为空"