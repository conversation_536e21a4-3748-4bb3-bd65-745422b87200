#!/usr/bin/env python3
"""
Feature 5 MVP功能验证测试

简化的AI组件功能验证，不依赖复杂的mock
"""

import pytest
import pandas as pd
import tempfile
from pathlib import Path
import sys
import os

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_ai_strategy_basic():
    """测试AI策略基类基础功能"""
    import os
    os.environ['AQUA_ENV'] = 'test'  # 设置测试环境
    
    from ai_agent.ai_strategy import NLPQueryStrategy, AnomalyDetectionStrategy
    
    # 测试NLP策略
    nlp_strategy = NLPQueryStrategy()
    assert nlp_strategy.name == "nlp_query"
    
    # 测试基本执行
    result = nlp_strategy.execute("测试查询", {})
    assert result["success"] is True
    assert result["strategy_type"] == "nlp_query"
    
    # 测试配置验证
    valid_config = {
        "max_query_length": 1000,
        "confidence_threshold": 0.8
    }
    assert nlp_strategy.validate_config(valid_config) is True
    
    # 测试异常检测策略
    anomaly_strategy = AnomalyDetectionStrategy()
    assert anomaly_strategy.name == "anomaly_detection"
    
    test_data = pd.DataFrame({"close": [10, 11, 12]})
    result = anomaly_strategy.execute(test_data, {})
    assert result["success"] is True
    assert result["strategy_type"] == "anomaly_detection"


def test_nlp_query_processor_basic():
    """测试NLP查询处理器基础功能"""
    from ai_agent.nlp_query_processor import NLPQueryProcessor
    
    # 创建临时配置
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            "database": {
                "environments": {"test": f"{temp_dir}/test.db"},
                "default_environment": "test"
            },
            "nlp_query": {
                "max_query_length": 500,
                "confidence_threshold": 0.7,
                "enable_cache": True
            },
            "logging": {
                "level": "INFO",
                "file_path": None  # 禁用文件日志
            }
        }
        
        # 简单功能测试，不涉及数据库连接
        processor = NLPQueryProcessor(config)
        assert processor.max_query_length == 500
        assert processor.confidence_threshold == 0.7
        
        # 测试查询解析
        result = processor.parse_natural_query("查询平安银行股价")
        assert result["success"] is True
        assert "entities" in result
        
        # 测试支持的查询
        queries = processor.get_supported_queries()
        assert len(queries) > 0
        assert "平安银行" in queries[0]


def test_anomaly_detection_engine_basic():
    """测试异常检测引擎基础功能"""
    from ai_agent.anomaly_detection_engine import AnomalyDetectionEngine
    
    config = {
        "anomaly_detection": {
            "confidence_threshold": 0.8,
            "z_score_threshold": 3.0
        },
        "notification": {
            "severity_threshold": "major"
        },
        "logging": {
            "level": "INFO", 
            "file_path": None
        }
    }
    
    engine = AnomalyDetectionEngine(config)
    assert engine.confidence_threshold == 0.8
    assert engine.z_score_threshold == 3.0
    
    # 测试统计异常检测
    test_data = pd.DataFrame({
        "close": [10, 11, 10.5, 50, 11.2],  # 50是异常值
        "vol": [1000, 1100, 1050, 1080, 1120],
        "pct_chg": [1.0, 0.5, -1.0, 25.0, 1.5]  # 25.0是异常值
    })
    
    result = engine.detect_anomalies(test_data, method="z_score")
    assert result["success"] is True
    assert result["detection_method"] == "z_score"
    
    # 测试百分比异常检测
    result = engine.detect_anomalies(test_data, method="percentage", threshold=0.2)
    assert result["success"] is True
    assert result["detection_method"] == "percentage"


def test_report_generator_basic():
    """测试报告生成器基础功能"""
    from ai_agent.report_generator import ReportGenerator
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            "database": {
                "environments": {"test": f"{temp_dir}/test.db"},
                "default_environment": "test"
            },
            "report_generation": {
                "output_path": f"{temp_dir}/reports",
                "template_path": f"{temp_dir}/templates"
            },
            "priority_queue": {
                "max_queue_size": 100,
                "worker_threads": 2
            },
            "logging": {
                "level": "INFO",
                "file_path": None
            }
        }
        
        generator = ReportGenerator(config)
        assert generator.output_path.exists()
        
        # 测试模板列表
        templates = generator.get_report_templates()
        assert "daily_summary" in templates
        assert "basic_report" in templates
        
        # 测试基础报告生成（无数据版本）
        test_data = pd.DataFrame({
            "trade_date": ["20240115"],
            "ts_code": ["000001.SZ"],
            "close": [10.5]
        })
        
        # 模拟数据获取
        generator._fetch_report_data = lambda config: test_data
        
        report_config = {
            "report_type": "basic_report",
            "title": "测试报告"
        }
        
        result = generator.generate_report(report_config)
        assert result["success"] is True
        assert result["report_type"] == "basic_report"
        assert Path(result["file_path"]).exists()


def test_ai_task_manager_basic():
    """测试AI任务管理器基础功能"""
    from ai_agent.ai_task_manager import AITaskManager
    
    config = {
        "ai_task_management": {
            "max_concurrent_training": 2,
            "max_concurrent_inference": 5
        },
        "priority_queue": {
            "max_queue_size": 100,
            "worker_threads": 2
        },
        "logging": {
            "level": "INFO",
            "file_path": None
        }
    }
    
    ai_manager = AITaskManager(config)
    assert ai_manager.max_concurrent_training == 2
    assert ai_manager.max_concurrent_inference == 5
    
    # 测试任务提交
    task_id = ai_manager.submit_nlp_query_task({
        "query_text": "测试查询"
    })
    assert task_id is not None
    
    # 测试任务状态
    status = ai_manager.get_task_status(task_id)
    assert status is not None
    assert status["task_type"] == "nlp_query"
    
    # 测试AI统计信息
    stats = ai_manager.get_ai_task_statistics()
    assert "ai_task_stats" in stats
    assert stats["ai_task_stats"]["nlp_queries"] >= 1


def test_integration_nlp_to_report():
    """测试NLP到报告生成的集成流程"""
    from ai_agent.nlp_query_processor import NLPQueryProcessor
    from ai_agent.report_generator import ReportGenerator
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            "database": {
                "environments": {"test": f"{temp_dir}/test.db"},
                "default_environment": "test"
            },
            "nlp_query": {
                "confidence_threshold": 0.7,
                "enable_cache": True
            },
            "report_generation": {
                "output_path": f"{temp_dir}/reports"
            },
            "priority_queue": {
                "max_queue_size": 100,
                "worker_threads": 2
            },
            "logging": {
                "level": "INFO",
                "file_path": None
            }
        }
        
        # 1. NLP解析查询
        nlp_processor = NLPQueryProcessor(config)
        parse_result = nlp_processor.parse_natural_query("查询平安银行股价数据")
        
        assert parse_result["success"] is True
        
        # 2. 生成基于解析结果的报告
        report_generator = ReportGenerator(config)
        
        # 模拟根据NLP结果生成报告配置
        report_config = {
            "report_type": "stock_analysis",
            "ts_code": parse_result["entities"].get("ts_code", "000001.SZ"),
            "title": f"基于查询'{parse_result['original_query']}'的分析报告"
        }
        
        # 模拟数据
        test_data = pd.DataFrame({
            "trade_date": ["20240115", "20240116"],
            "ts_code": ["000001.SZ", "000001.SZ"],
            "close": [10.5, 10.8],
            "vol": [1000000, 1100000]
        })
        
        report_generator._fetch_report_data = lambda config: test_data
        
        result = report_generator.generate_report(report_config)
        assert result["success"] is True
        assert Path(result["file_path"]).exists()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])