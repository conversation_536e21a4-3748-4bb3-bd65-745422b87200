#!/usr/bin/env python3
"""
测试数据库配置修复 - TDD原则
"""

import unittest
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, 'src')

class TestDatabaseConfigFix(unittest.TestCase):
    """测试数据库配置修复功能"""
    
    def setUp(self):
        """测试前准备"""
        from utils.config_loader_v2 import ConfigLoaderV2
        self.config_loader = ConfigLoaderV2()
    
    def test_placeholder_replacement_works(self):
        """测试占位符替换功能正常工作"""
        dev_config = self.config_loader.get_config('dev')
        
        # 验证数据库配置存在
        self.assertIn('database', dev_config)
        
        db_config = dev_config['database']
        db_path = db_config.get('path')
        
        # 验证路径不包含未替换的占位符
        self.assertNotIn('{datacenter_dir}', str(db_path))
        self.assertNotIn('{', str(db_path))
        
    def test_correct_database_path_format(self):
        """测试数据库路径格式正确"""
        dev_config = self.config_loader.get_config('dev')
        db_path = dev_config['database']['path']
        
        # 验证Windows平台的正确路径格式
        expected_pattern = "D:/Data/duckdb/AQUA/DataCenter/aqua_dev.duckdb"
        self.assertEqual(db_path, expected_pattern)
        
    def test_database_directory_structure(self):
        """测试数据库目录结构正确"""
        dev_config = self.config_loader.get_config('dev')
        db_path = Path(dev_config['database']['path'])
        
        # 验证目录结构
        self.assertEqual(db_path.name, "aqua_dev.duckdb")
        self.assertTrue("DataCenter" in str(db_path.parent))
        self.assertTrue("D:/Data/duckdb/AQUA" in str(db_path))
        
    def test_platform_detection(self):
        """测试平台检测正确"""
        platform_info = self.config_loader.get_platform_info()
        
        # 在Windows环境下验证
        self.assertEqual(platform_info['system'], 'Windows')
        self.assertTrue(platform_info.get('is_windows', False))
        
    def test_no_wrong_database_file(self):
        """测试不存在错误位置的数据库文件"""
        wrong_db_path = Path('data/aqua.duckdb')
        
        # 验证错误位置的数据库文件不存在（或应该被删除）
        if wrong_db_path.exists():
            self.fail(f"错误位置的数据库文件仍然存在: {wrong_db_path}")

if __name__ == '__main__':
    print("🧪 执行数据库配置修复测试")
    unittest.main(verbosity=2)
