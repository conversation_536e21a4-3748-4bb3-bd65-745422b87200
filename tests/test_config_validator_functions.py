#!/usr/bin/env python3
"""
配置验证器功能测试
针对Feature 1新增的验证功能进行专项测试

测试覆盖：
- validate_tushare_config功能
- validate_cross_platform_config功能
- 个人开发者内存配置验证功能
- 错误处理和警告机制
"""

import os
import tempfile
import platform
from unittest.mock import patch, MagicMock
import pytest
import toml

from src.utils.config_validator import ConfigValidator, ValidationLevel, ValidationResult


class TestConfigValidatorFunctions:
    """配置验证器功能测试类"""
    
    def test_validate_tushare_config_complete_flow(self):
        """测试TUSHARE配置验证完整流程"""
        # 创建完整的TUSHARE配置
        test_config = {
            "datasources": {
                "api": {
                    "tushare": {
                        "name": "Tushare Pro",
                        "enabled": True,
                        "base_url": "https://tushare.pro",
                        "token": "${TUSHARE_TOKEN}",
                        "rate_limit": 200,
                        "points_per_minute": 2000,
                        "budget": {
                            "total_points": 2100,
                            "daily_limit": 50,
                            "emergency_reserve": 200,
                            "modes": {
                                "conservative": {"daily_limit": 30, "priority": "期货主力合约"},
                                "normal": {"daily_limit": 50, "priority": "期货>股票>其他"},
                                "aggressive": {"daily_limit": 80, "priority": "全量数据"}
                            }
                        },
                        "monitoring": {
                            "enable_tracking": True,
                            "efficiency_target": 0.95,
                            "warning_threshold": 0.8,
                            "auto_downgrade": True
                        }
                    }
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(test_config, f)
            temp_config_path = f.name
        
        try:
            with patch.dict(os.environ, {'TUSHARE_TOKEN': 'test_token_123'}):
                validator = ConfigValidator(temp_config_path)
                validator.load_config()
                
                # 验证TUSHARE配置
                validator.validate_tushare_config(validator.config, 'dev')
                
                # 不应该有任何错误
                errors = [r for r in validator.validation_results 
                         if r.level == ValidationLevel.ERROR]
                assert len(errors) == 0, f"完整配置不应该有错误: {[e.message for e in errors]}"
                
                # 可能有一些信息性消息，但不应该有警告
                warnings = [r for r in validator.validation_results 
                           if r.level == ValidationLevel.WARNING]
                # 允许预算警告（因为50*30 > 2100可能触发）
                budget_warnings = [w for w in warnings if '积分' in w.message and '月' in w.message]
                other_warnings = [w for w in warnings if w not in budget_warnings]
                assert len(other_warnings) == 0, f"不应该有其他警告: {[w.message for w in other_warnings]}"
                
        finally:
            os.unlink(temp_config_path)
    
    def test_validate_tushare_config_missing_sections(self):
        """测试TUSHARE配置缺失各个部分的情况"""
        # 测试完全缺失TUSHARE配置
        empty_config = {"datasources": {"api": {}}}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(empty_config, f)
            temp_config_path = f.name
        
        try:
            validator = ConfigValidator(temp_config_path)
            validator.load_config()
            validator.validate_tushare_config(validator.config, 'dev')
            
            # 应该有TUSHARE配置缺失错误
            errors = [r for r in validator.validation_results 
                     if r.level == ValidationLevel.ERROR and 'TUSHARE API配置缺失' in r.message]
            assert len(errors) == 1, "应该检测到TUSHARE配置缺失"
            
        finally:
            os.unlink(temp_config_path)
    
    def test_validate_tushare_config_disabled(self):
        """测试TUSHARE配置未启用的情况"""
        disabled_config = {
            "datasources": {
                "api": {
                    "tushare": {
                        "name": "Tushare Pro",
                        "enabled": False,  # 未启用
                        "token": "${TUSHARE_TOKEN}"
                    }
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(disabled_config, f)
            temp_config_path = f.name
        
        try:
            validator = ConfigValidator(temp_config_path)
            validator.load_config()
            validator.validate_tushare_config(validator.config, 'dev')
            
            # 应该有TUSHARE未启用警告
            warnings = [r for r in validator.validation_results 
                       if r.level == ValidationLevel.WARNING and 'TUSHARE API未启用' in r.message]
            assert len(warnings) == 1, "应该检测到TUSHARE未启用"
            
        finally:
            os.unlink(temp_config_path)
    
    def test_validate_cross_platform_config_complete(self):
        """测试跨平台配置验证完整功能"""
        platform_config = {
            "platform": {
                "auto_detect": True,
                "normalize_paths": True,
                "auto_create_dirs": True,
                "path_validation": True,
                "supported_platforms": ["windows", "darwin", "linux"],
                "personal_dev": {
                    "env_vars": ["AQUA_CONFIG_DIR", "AQUA_DATA_DIR", "TUSHARE_TOKEN"],
                    "default_encoding": "utf-8",
                    "auto_path_separator": True,
                    "check_write_permissions": True,
                    "utf8_path_support": True
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(platform_config, f)
            temp_config_path = f.name
        
        try:
            with patch.dict(os.environ, {'TUSHARE_TOKEN': 'test_token'}):
                validator = ConfigValidator(temp_config_path)
                validator.load_config()
                validator.validate_cross_platform_config(validator.config, 'dev')
                
                # 不应该有任何错误（当前平台应该被支持）
                errors = [r for r in validator.validation_results 
                         if r.level == ValidationLevel.ERROR]
                platform_errors = [e for e in errors if '平台' in e.message]
                assert len(platform_errors) == 0, f"当前平台应该被支持: {[e.message for e in platform_errors]}"
                
        finally:
            os.unlink(temp_config_path)
    
    def test_validate_personal_dev_memory_limits(self):
        """测试个人开发者内存限制验证"""
        # 测试内存限制过高的情况
        high_memory_config = {
            "dev": {
                "performance": {
                    "personal_dev_mode": True,
                    "memory_limit_mb": 3000,  # 超过1536MB限制
                    "startup_target_seconds": 2
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(high_memory_config, f)
            temp_config_path = f.name
        
        try:
            validator = ConfigValidator(temp_config_path)
            validator.load_config()
            validator.validate_environment('dev')
            
            # 应该有内存限制警告
            warnings = [r for r in validator.validation_results 
                       if r.level == ValidationLevel.WARNING and '内存限制' in r.message and '1536MB' in r.message]
            assert len(warnings) == 1, f"应该检测到内存限制警告: {[r.message for r in validator.validation_results]}"
            
        finally:
            os.unlink(temp_config_path)
    
    def test_validate_personal_dev_memory_normal(self):
        """测试个人开发者内存限制正常情况"""
        # 测试符合要求的内存配置
        normal_memory_config = {
            "dev": {
                "performance": {
                    "personal_dev_mode": True,
                    "memory_limit_mb": 1536,  # 符合要求
                    "startup_target_seconds": 2,
                    "memory_warning_threshold_mb": 1200,
                    "auto_gc_trigger_mb": 50,
                    "idle_memory_limit_mb": 800
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(normal_memory_config, f)
            temp_config_path = f.name
        
        try:
            validator = ConfigValidator(temp_config_path)
            validator.load_config()
            validator.validate_environment('dev')
            
            # 不应该有内存限制警告
            memory_warnings = [r for r in validator.validation_results 
                             if r.level == ValidationLevel.WARNING and '内存限制' in r.message and '1536MB' in r.message]
            assert len(memory_warnings) == 0, f"符合要求的内存配置不应该有警告: {[r.message for r in memory_warnings]}"
            
        finally:
            os.unlink(temp_config_path)
    
    def test_budget_validation_edge_cases(self):
        """测试积分预算验证边界情况"""
        # 测试预算恰好足够的情况
        adequate_budget_config = {
            "datasources": {
                "api": {
                    "tushare": {
                        "enabled": True,
                        "token": "${TUSHARE_TOKEN}",
                        "budget": {
                            "total_points": 1500,  # 恰好够30天，每天50
                            "daily_limit": 50
                        }
                    }
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(adequate_budget_config, f)
            temp_config_path = f.name
        
        try:
            with patch.dict(os.environ, {'TUSHARE_TOKEN': 'test_token'}):
                validator = ConfigValidator(temp_config_path)
                validator.load_config()
                validator.validate_tushare_config(validator.config, 'dev')
                
                # 应该有预算不足警告（1500 < 50*30）
                budget_warnings = [r for r in validator.validation_results 
                                 if r.level == ValidationLevel.WARNING and '积分' in r.message and '月' in r.message]
                assert len(budget_warnings) == 1, "应该检测到预算可能不足警告"
                
        finally:
            os.unlink(temp_config_path)
    
    def test_environment_variable_missing(self):
        """测试环境变量缺失检测"""
        tushare_config = {
            "platform": {
                "personal_dev": {
                    "env_vars": ["TUSHARE_TOKEN", "AQUA_CONFIG_DIR"]
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(tushare_config, f)
            temp_config_path = f.name
        
        try:
            # 清除环境变量
            with patch.dict(os.environ, {}, clear=True):
                validator = ConfigValidator(temp_config_path)
                validator.load_config()
                validator.validate_cross_platform_config(validator.config, 'dev')
                
                # 应该有TUSHARE_TOKEN环境变量警告
                env_warnings = [r for r in validator.validation_results 
                              if r.level == ValidationLevel.WARNING and 'TUSHARE_TOKEN' in r.message]
                assert len(env_warnings) == 1, "应该检测到TUSHARE_TOKEN环境变量缺失"
                
        finally:
            os.unlink(temp_config_path)
    
    def test_validation_result_structure(self):
        """测试验证结果结构正确性"""
        # 创建会触发各种验证问题的配置
        problematic_config = {
            "datasources": {
                "api": {
                    "tushare": {
                        "enabled": False,  # 会触发警告
                        "token": "",       # 会触发错误
                        "budget": {
                            "total_points": 100,  # 会触发预算警告
                            "daily_limit": 50
                        }
                    }
                }
            },
            "platform": {
                "supported_platforms": ["windows"],  # 在macOS上会触发错误
                "personal_dev": {
                    "env_vars": ["MISSING_VAR"]  # 会触发环境变量警告
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(problematic_config, f)
            temp_config_path = f.name
        
        try:
            with patch.dict(os.environ, {}, clear=True):
                validator = ConfigValidator(temp_config_path)
                validator.load_config()
                
                # 运行所有验证
                validator.validate_tushare_config(validator.config, 'dev')
                validator.validate_cross_platform_config(validator.config, 'dev')
                
                # 验证结果结构
                for result in validator.validation_results:
                    assert isinstance(result, ValidationResult), "结果应该是ValidationResult类型"
                    assert hasattr(result, 'level'), "结果应该有level属性"
                    assert hasattr(result, 'message'), "结果应该有message属性"
                    assert hasattr(result, 'path'), "结果应该有path属性"
                    assert result.level in [ValidationLevel.ERROR, ValidationLevel.WARNING, ValidationLevel.INFO], \
                        f"验证级别应该是有效值: {result.level}"
                    assert isinstance(result.message, str), "消息应该是字符串"
                    assert len(result.message) > 0, "消息不应该为空"
                
                # 应该有各种类型的验证结果
                errors = [r for r in validator.validation_results if r.level == ValidationLevel.ERROR]
                warnings = [r for r in validator.validation_results if r.level == ValidationLevel.WARNING]
                
                assert len(errors) > 0, "应该有错误检测"
                assert len(warnings) > 0, "应该有警告检测"
                
        finally:
            os.unlink(temp_config_path)


def test_integration_with_actual_config():
    """与实际配置文件的集成测试"""
    # 使用实际的配置文件进行测试
    actual_config_path = "config/settings.toml"
    
    if os.path.exists(actual_config_path):
        # 设置测试环境变量，避免环境变量错误
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'test_token_for_integration_test'}):
            validator = ConfigValidator(actual_config_path)
            
            # 运行完整验证
            results = validator.validate_all()
            
            # 验证Feature 1相关的配置没有严重错误
            feature1_errors = []
            for result in results:
                if result.level == ValidationLevel.ERROR:
                    if any(keyword in result.path.lower() for keyword in 
                          ['tushare', 'personal_dev', 'memory_limit', 'platform']):
                        feature1_errors.append(result)
            
            # Feature 1的配置不应该有严重错误
            assert len(feature1_errors) == 0, \
                f"Feature 1配置存在错误: {[e.message for e in feature1_errors]}"
            
            print(f"✅ 实际配置文件验证完成，检测到{len(results)}个验证结果")