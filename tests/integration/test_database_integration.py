#!/usr/bin/env python3
"""
数据库集成测试
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch

from src.database.duckdb_init_check import DuckDBInitCheck
from src.utils.config_loader_v2 import ConfigLoaderV2
from src.utils.time_utils import get_beijing_time_now


class TestDatabaseIntegration:
    """数据库集成测试"""

    def setup_method(self):
        """测试前设置"""
        # 创建临时数据库文件
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        
        # 创建临时数据字典文件
        self.temp_dict = tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False)
        self.temp_dict.write("""
# 数据字典

## 表结构

### test_table
测试表

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| name | VARCHAR(50) | 名称 |
| created_at | TIMESTAMP | 创建时间 |
""")
        self.temp_dict.close()
        
        # 模拟配置
        self.config = {
            'database': {
                'path': self.temp_db.name,
                'auto_create': True,
                'backup_dir': 'backup/'
            }
        }
        
        # 创建连接管理器模拟
        self.mock_connection_manager = Mock()
        self.mock_connection_manager.test_connection.return_value = True
        self.mock_connection_manager.table_exists.return_value = False
        
        # 创建检查器实例
        self.checker = DuckDBInitCheck(
            self.config,
            self.mock_connection_manager,
            Path(self.temp_dict.name)
        )

    def teardown_method(self):
        """测试后清理"""
        # 删除临时文件
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
        if os.path.exists(self.temp_dict.name):
            os.unlink(self.temp_dict.name)

    def test_full_database_lifecycle(self):
        """测试完整数据库生命周期"""
        # 1. 检查数据库不存在
        result = self.checker.check_database_exists()
        assert result.success is True  # 文件存在但是空的
        
        # 2. 检查数据字典存在
        result = self.checker.check_data_dictionary()
        assert result.success is True
        
        # 3. 解析数据字典
        result = self.checker.parse_data_dictionary()
        assert result.success is True
        assert len(result.data) == 1
        assert result.data[0].name == 'test_table'
        
        # 4. 检查表不存在
        result = self.checker.check_table_exists('test_table')
        assert result.success is False
        
        # 5. 初始化数据库
        with patch.object(self.checker.connection_manager, 'get_cursor') as mock_cursor:
            mock_cursor.return_value.__enter__.return_value = Mock()
            result = self.checker.initialize_database()
            assert result.success is True

    def test_database_repair_workflow(self):
        """测试数据库修复工作流"""
        # 1. 模拟部分表缺失
        def mock_table_exists(table_name):
            if table_name == 'test_table':
                return False
            return True
        
        self.mock_connection_manager.table_exists.side_effect = mock_table_exists
        
        # 2. 执行修复
        with patch.object(self.checker, 'create_table') as mock_create:
            mock_create.return_value = self.checker.CheckResult(True, "Table created")
            
            result = self.checker.repair_database()
            assert result.success is True
            mock_create.assert_called_once()

    def test_backup_and_restore_workflow(self):
        """测试备份和恢复工作流"""
        # 1. 创建备份
        with patch('shutil.copy2') as mock_copy:
            result = self.checker.backup_database()
            assert result.success is True
            mock_copy.assert_called_once()
        
        # 2. 恢复数据库
        backup_path = Path('backup.db')
        with patch('pathlib.Path.exists', return_value=True):
            with patch('shutil.copy2') as mock_copy:
                result = self.checker.restore_database(backup_path)
                assert result.success is True
                mock_copy.assert_called_once()

    def test_health_check_integration(self):
        """测试健康检查集成"""
        # 模拟各种检查结果
        with patch.object(self.checker, 'check_database_exists') as mock_db_check:
            with patch.object(self.checker, 'check_connection') as mock_conn_check:
                with patch.object(self.checker, 'check_data_dictionary') as mock_dict_check:
                    with patch.object(self.checker, 'check_all_tables_exist') as mock_tables_check:
                        
                        mock_db_check.return_value = self.checker.CheckResult(True, "DB exists")
                        mock_conn_check.return_value = self.checker.CheckResult(True, "Connection OK")
                        mock_dict_check.return_value = self.checker.CheckResult(True, "Dict OK")
                        mock_tables_check.return_value = self.checker.CheckResult(True, "Tables OK")
                        
                        result = self.checker.full_health_check()
                        
                        assert result.success is True
                        assert "健康检查通过" in result.message

    def test_data_dictionary_parsing_integration(self):
        """测试数据字典解析集成"""
        # 创建复杂的数据字典
        complex_dict_content = """
# 数据字典

## 表结构

### stock_basic_info
股票基础信息表

| 字段名 | 类型 | 描述 |
|--------|------|------|
| symbol | VARCHAR(10) | 股票代码 |
| name | VARCHAR(50) | 股票名称 |
| market | VARCHAR(10) | 交易所代码 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### futures_basic_info
期货基础信息表

| 字段名 | 类型 | 描述 |
|--------|------|------|
| symbol | VARCHAR(20) | 期货代码 |
| name | VARCHAR(100) | 期货名称 |
| exchange | VARCHAR(20) | 交易所 |
| multiplier | INTEGER | 合约乘数 |
| created_at | TIMESTAMP | 创建时间 |

### stock_daily_data
股票日线数据表

| 字段名 | 类型 | 描述 |
|--------|------|------|
| symbol | VARCHAR(10) | 股票代码 |
| trade_date | DATE | 交易日期 |
| open_price | DECIMAL(10,2) | 开盘价 |
| high_price | DECIMAL(10,2) | 最高价 |
| low_price | DECIMAL(10,2) | 最低价 |
| close_price | DECIMAL(10,2) | 收盘价 |
| volume | BIGINT | 成交量 |
| amount | DECIMAL(15,2) | 成交额 |
"""
        
        # 创建新的临时数据字典文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_dict:
            temp_dict.write(complex_dict_content)
            temp_dict_path = temp_dict.name
        
        try:
            # 创建新的检查器实例
            checker = DuckDBInitCheck(
                self.config,
                self.mock_connection_manager,
                Path(temp_dict_path)
            )
            
            # 解析数据字典
            result = checker.parse_data_dictionary()
            
            assert result.success is True
            assert len(result.data) == 3
            
            # 验证第一个表
            stock_basic = result.data[0]
            assert stock_basic.name == 'stock_basic_info'
            assert len(stock_basic.columns) == 5
            assert stock_basic.columns[0]['name'] == 'symbol'
            assert stock_basic.columns[0]['type'] == 'VARCHAR(10)'
            
            # 验证第二个表
            futures_basic = result.data[1]
            assert futures_basic.name == 'futures_basic_info'
            assert len(futures_basic.columns) == 5
            assert futures_basic.columns[3]['name'] == 'multiplier'
            assert futures_basic.columns[3]['type'] == 'INTEGER'
            
            # 验证第三个表
            stock_daily = result.data[2]
            assert stock_daily.name == 'stock_daily_data'
            assert len(stock_daily.columns) == 8
            assert stock_daily.columns[2]['name'] == 'open_price'
            assert stock_daily.columns[2]['type'] == 'DECIMAL(10,2)'
            
        finally:
            os.unlink(temp_dict_path)

    def test_table_creation_integration(self):
        """测试表创建集成"""
        # 获取表结构
        schemas = self.checker.get_table_schemas()
        assert len(schemas) == 1
        
        test_table_schema = schemas[0]
        
        # 模拟游标
        mock_cursor = Mock()
        self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        # 创建表
        result = self.checker.create_table(test_table_schema)
        
        assert result.success is True
        assert "表创建成功" in result.message
        
        # 验证SQL语句被执行
        mock_cursor.execute.assert_called_once()
        executed_sql = mock_cursor.execute.call_args[0][0]
        assert "CREATE TABLE test_table" in executed_sql
        assert "id INTEGER" in executed_sql
        assert "name VARCHAR(50)" in executed_sql
        assert "created_at TIMESTAMP" in executed_sql

    def test_database_info_integration(self):
        """测试数据库信息集成"""
        # 获取数据库信息
        info = self.checker.get_database_info()
        
        assert info['path'] == self.temp_db.name
        assert info['exists'] is True
        assert info['size'] == 0  # 空文件
        assert 'created_at' in info
        assert 'modified_at' in info

    def test_table_data_check_integration(self):
        """测试表数据检查集成"""
        # 模拟表有数据
        mock_cursor = Mock()
        mock_cursor.fetchone.return_value = (100,)
        self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        result = self.checker.check_table_data('test_table')
        
        assert result.success is True
        assert result.data['row_count'] == 100
        assert "表有数据" in result.message
        
        # 验证SQL查询
        mock_cursor.execute.assert_called_once_with("SELECT COUNT(*) FROM test_table")

    def test_multi_table_initialization(self):
        """测试多表初始化"""
        # 创建多表数据字典
        multi_table_dict = """
# 数据字典

## 表结构

### table1
表1

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| name | VARCHAR(50) | 名称 |

### table2
表2

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| value | DECIMAL(10,2) | 值 |

### table3
表3

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键 |
| data | TEXT | 数据 |
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_dict:
            temp_dict.write(multi_table_dict)
            temp_dict_path = temp_dict.name
        
        try:
            # 创建新的检查器实例
            checker = DuckDBInitCheck(
                self.config,
                self.mock_connection_manager,
                Path(temp_dict_path)
            )
            
            # 模拟游标
            mock_cursor = Mock()
            self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
            
            # 初始化数据库
            result = checker.initialize_database()
            
            assert result.success is True
            assert "数据库初始化成功" in result.message
            
            # 验证所有表都被创建
            assert mock_cursor.execute.call_count == 3
            
            # 验证SQL语句包含所有表
            executed_calls = [call[0][0] for call in mock_cursor.execute.call_args_list]
            assert any("CREATE TABLE table1" in sql for sql in executed_calls)
            assert any("CREATE TABLE table2" in sql for sql in executed_calls)
            assert any("CREATE TABLE table3" in sql for sql in executed_calls)
            
        finally:
            os.unlink(temp_dict_path)

    def test_error_handling_integration(self):
        """测试错误处理集成"""
        # 测试数据字典文件不存在
        non_existent_path = Path('/non/existent/path.md')
        checker = DuckDBInitCheck(
            self.config,
            self.mock_connection_manager,
            non_existent_path
        )
        
        result = checker.check_data_dictionary()
        assert result.success is False
        assert "数据字典文件不存在" in result.message
        
        # 测试解析数据字典失败
        result = checker.parse_data_dictionary()
        assert result.success is False
        assert "数据字典文件不存在" in result.message

    def test_force_initialization_integration(self):
        """测试强制初始化集成"""
        # 模拟表已存在
        self.mock_connection_manager.table_exists.return_value = True
        
        # 模拟游标
        mock_cursor = Mock()
        self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        # 强制初始化
        result = self.checker.initialize_database(force=True)
        
        assert result.success is True
        
        # 验证删除和创建语句都被执行
        executed_calls = [call[0][0] for call in mock_cursor.execute.call_args_list]
        assert any("DROP TABLE IF EXISTS test_table" in sql for sql in executed_calls)
        assert any("CREATE TABLE test_table" in sql for sql in executed_calls)

    def test_health_report_generation(self):
        """测试健康报告生成"""
        # 模拟各种检查结果
        with patch.object(self.checker, 'full_health_check') as mock_health_check:
            with patch.object(self.checker, 'get_table_list') as mock_table_list:
                
                mock_health_check.return_value = self.checker.CheckResult(True, "All good")
                mock_table_list.return_value = ['test_table']
                
                report = self.checker.generate_health_report()
                
                assert report['overall_health'] is True
                assert report['database_info']['exists'] is True
                assert len(report['tables']) == 1
                assert report['tables'][0] == 'test_table'
                assert 'timestamp' in report
                assert 'checks' in report