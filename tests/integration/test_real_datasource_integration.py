"""
Feature 5A CLI真实数据源集成测试
测试CLI系统与真实数据源（TUSHARE、MySQL、CSV）的集成
"""
import pytest
import sys
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.cli.services.collect_service import CollectService


class TestRealDataSourceIntegration:
    """真实数据源集成测试"""
    
    def setup_method(self):
        """测试设置"""
        self.collect_service = CollectService()
    
    def test_tushare_real_extractor_initialization(self):
        """测试TUSHARE真实提取器初始化"""
        # 获取TUSHARE提取器
        extractor = self.collect_service._get_tushare_extractor()
        
        # 验证提取器不是空对象
        assert extractor is not None
        
        # 检查是否为真实提取器或Mock备选
        if hasattr(extractor, 'extract'):
            # 真实提取器
            assert hasattr(extractor, 'extract'), "真实提取器应该有extract方法"
            print("✅ TUSHARE真实提取器初始化成功")
        else:
            # Mock备选（当真实提取器初始化失败时）
            print(f"⚠️ TUSHARE提取器使用Mock备选")
    
    def test_storage_manager_real_initialization(self):
        """测试存储管理器真实初始化"""
        # 获取存储管理器
        storage = self.collect_service._get_storage_manager()
        
        # 验证存储管理器不是空对象
        assert storage is not None
        
        # 检查是否为真实存储管理器或Mock备选
        if hasattr(storage, 'save_data') and not hasattr(storage, 'error'):
            # 真实存储管理器
            print("✅ UnifiedStorageManager真实初始化成功")
        else:
            # Mock备选
            print(f"⚠️ 存储管理器使用Mock备选: {getattr(storage, 'error', 'Unknown error')}")
    
    def test_mysql_importer_initialization(self):
        """测试MySQL导入器初始化"""
        # 获取MySQL导入器
        mysql_importer = self.collect_service._get_mysql_importer()
        
        # 验证MySQL导入器不是空对象
        assert mysql_importer is not None
        
        # 检查是否为真实导入器或Mock备选
        if hasattr(mysql_importer, 'import_data'):
            print("✅ MySQLImporter真实初始化成功")
        else:
            print(f"⚠️ MySQL导入器使用Mock备选: {getattr(mysql_importer, 'error', 'Unknown error')}")
    
    def test_csv_importer_initialization(self):
        """测试CSV导入器初始化"""
        # 获取CSV导入器
        csv_importer = self.collect_service._get_csv_importer()
        
        # 验证CSV导入器不是空对象
        assert csv_importer is not None
        
        # 检查是否为真实导入器或Mock备选
        if hasattr(csv_importer, 'import_data'):
            print("✅ CSV导入器真实初始化成功")
        else:
            print(f"⚠️ CSV导入器使用Mock备选: {getattr(csv_importer, 'error', 'Unknown error')}")
    
    def test_tushare_capabilities_real_check(self):
        """测试TUSHARE数据源能力检查（真实）"""
        capabilities = self.collect_service.check_capabilities('tushare')
        
        # 验证基本结构
        assert capabilities['source'] == 'tushare'
        assert 'supported_types' in capabilities
        assert 'supported_frequencies' in capabilities
        assert 'status' in capabilities
        
        # 检查状态
        if capabilities['status'] == 'available':
            print("✅ TUSHARE数据源可用")
            assert 'stock' in capabilities['supported_types']
            assert 'futures' in capabilities['supported_types']
        else:
            print(f"⚠️ TUSHARE数据源不可用: {capabilities.get('error', 'Unknown')}")
    
    def test_mysql_capabilities_real_check(self):
        """测试MySQL数据源能力检查（真实）"""
        capabilities = self.collect_service.check_capabilities('mysql')
        
        # 验证基本结构
        assert capabilities['source'] == 'mysql'
        assert 'supported_types' in capabilities
        assert 'connection_status' in capabilities
        
        # 检查连接状态
        if capabilities['status'] == 'available':
            print("✅ MySQL数据源可用")
        else:
            print(f"⚠️ MySQL数据源不可用: {capabilities['connection_status']}")
    
    def test_csv_capabilities_real_check(self):
        """测试CSV数据源能力检查（真实）"""
        capabilities = self.collect_service.check_capabilities('csv')
        
        # 验证基本结构
        assert capabilities['source'] == 'csv'
        assert 'supported_types' in capabilities
        assert 'data_path' in capabilities
        
        # 检查可用性
        if capabilities['status'] == 'available':
            print("✅ CSV数据源可用")
            assert 'futures' in capabilities['supported_types']
            print(f"CSV数据路径: {capabilities['data_path']}")
        else:
            print("⚠️ CSV数据源不可用")
    
    def test_tushare_real_data_collection(self):
        """测试TUSHARE真实数据采集"""
        # 测试小量数据采集
        result = self.collect_service.collect_data(
            symbols=['000001.SZ'],
            source='tushare',
            data_type='stock',
            freq='daily',
            last_days=5  # 只采集5天数据进行测试
        )
        
        # 验证结果结构
        assert 'success' in result
        assert 'data_source_type' in result
        
        if result['success']:
            print(f"✅ TUSHARE真实数据采集成功: {result['message']}")
            print(f"数据源类型: {result['data_source_type']}")
            print(f"采集行数: {result['total_rows']}")
            
            # 验证为真实数据
            if result['data_source_type'] == 'real':
                assert result['total_rows'] > 0, "真实数据应该有实际行数"
                print("✅ 确认使用真实TUSHARE数据源")
            else:
                print("⚠️ 使用Mock数据源作为备选")
        else:
            print(f"⚠️ TUSHARE数据采集失败: {result.get('errors', 'Unknown error')}")
    
    def test_mysql_real_data_collection(self):
        """测试MySQL真实数据导入"""
        result = self.collect_service.collect_data(
            symbols=['test_symbol'],
            source='mysql',
            data_type='stock',
            freq='daily'
        )
        
        # 验证结果结构
        assert 'success' in result
        
        if result['success']:
            print(f"✅ MySQL真实数据导入成功: {result['message']}")
            assert result['data_source_type'] == 'real', "应该使用真实MySQL数据源"
        else:
            print(f"⚠️ MySQL数据导入失败: {result.get('errors', 'Unknown error')}")
    
    def test_csv_real_data_collection(self):
        """测试CSV真实数据导入"""
        result = self.collect_service.collect_data(
            symbols=['test_futures'],
            source='csv',
            data_type='futures',
            freq='5min'
        )
        
        # 验证结果结构
        assert 'success' in result
        
        if result['success']:
            print(f"✅ CSV真实数据导入成功: {result['message']}")
            assert result['data_source_type'] == 'real', "应该使用真实CSV数据源"
        else:
            print(f"⚠️ CSV数据导入失败: {result.get('errors', 'Unknown error')}")
    
    def test_data_source_type_reporting(self):
        """测试数据源类型报告功能"""
        # 测试所有数据源的类型报告
        sources = ['tushare', 'mysql', 'csv']
        
        for source in sources:
            result = self.collect_service.collect_data(
                symbols=['test'],
                source=source,
                preview=True  # 使用预览模式，不实际采集数据
            )
            
            print(f"{source}数据源预览: {result.get('success', False)}")
    
    def test_real_vs_mock_identification(self):
        """测试真实数据源vs Mock数据源识别"""
        # 获取所有数据源实例
        tushare_extractor = self.collect_service._get_tushare_extractor()
        storage_manager = self.collect_service._get_storage_manager()
        mysql_importer = self.collect_service._get_mysql_importer()
        csv_importer = self.collect_service._get_csv_importer()
        
        # 统计真实vs Mock实例
        real_count = 0
        mock_count = 0
        
        # TUSHARE Extractor
        if hasattr(tushare_extractor, 'extract') and not hasattr(tushare_extractor, 'error'):
            real_count += 1
            print("✅ TushareExtractor: 真实实例")
        else:
            mock_count += 1
            print("⚠️ TushareExtractor: Mock实例")
        
        # Storage Manager
        if hasattr(storage_manager, 'save_data') and not hasattr(storage_manager, 'error'):
            real_count += 1
            print("✅ UnifiedStorageManager: 真实实例")
        else:
            mock_count += 1
            print("⚠️ UnifiedStorageManager: Mock实例")
        
        # MySQL Importer
        if hasattr(mysql_importer, 'import_data') and not hasattr(mysql_importer, 'error'):
            real_count += 1
            print("✅ MySQLImporter: 真实实例")
        else:
            mock_count += 1
            print("⚠️ MySQLImporter: Mock实例")
        
        # CSV Importer
        if hasattr(csv_importer, 'import_data') and not hasattr(csv_importer, 'error'):
            real_count += 1
            print("✅ CSV导入器: 真实实例")
        else:
            mock_count += 1
            print("⚠️ CSV导入器: Mock实例")
        
        print(f"\n📊 数据源实例统计:")
        print(f"真实实例: {real_count}/4 ({real_count/4*100:.1f}%)")
        print(f"Mock实例: {mock_count}/4 ({mock_count/4*100:.1f}%)")
        
        # 验证至少有一些真实实例
        assert real_count > 0, "至少应该有一个真实数据源实例"


if __name__ == "__main__":
    # 直接运行测试
    test_suite = TestRealDataSourceIntegration()
    test_suite.setup_method()
    
    print("🚀 开始Feature 5A CLI真实数据源集成测试\n")
    
    try:
        test_suite.test_tushare_real_extractor_initialization()
        test_suite.test_storage_manager_real_initialization()
        test_suite.test_mysql_importer_initialization()
        test_suite.test_csv_importer_initialization()
        
        print("\n📋 数据源能力检查:")
        test_suite.test_tushare_capabilities_real_check()
        test_suite.test_mysql_capabilities_real_check()
        test_suite.test_csv_capabilities_real_check()
        
        print("\n🔍 真实数据采集测试:")
        test_suite.test_tushare_real_data_collection()
        test_suite.test_mysql_real_data_collection()
        test_suite.test_csv_real_data_collection()
        
        print("\n📊 数据源类型识别:")
        test_suite.test_real_vs_mock_identification()
        
        print("\n✅ Feature 5A CLI真实数据源集成测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()