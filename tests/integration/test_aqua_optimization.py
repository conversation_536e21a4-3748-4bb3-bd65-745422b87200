"""
AQUA项目优化效果验证测试
验证v5.1优化方案的实际效果
"""
import pytest
import subprocess
import time
from pathlib import Path
import os

class TestAquaOptimization:
    """AQUA项目优化效果测试套件"""
    
    def test_entry_point_simplification(self):
        """测试入口点简化效果"""
        project_root = Path.cwd()
        
        # 验证只保留了2个核心入口
        core_entries = ["aqua.py", "main.py"]
        for entry in core_entries:
            assert (project_root / entry).exists(), f"核心入口文件 {entry} 不存在"
        
        # 验证冗余文件已归档
        deprecated_entries = ["aqua.sh", "start_services.py", "Start-AQUA.ps1"]
        for entry in deprecated_entries:
            # 根目录不应存在
            assert not (project_root / entry).exists(), f"冗余入口文件 {entry} 仍在根目录"
            # 应在归档目录存在
            assert (project_root / "deprecated" / "entry_files" / entry).exists(), f"归档文件 {entry} 未找到"
    
    def test_dependency_simplification(self):
        """测试依赖管理简化效果"""
        project_root = Path.cwd()
        
        # 验证保留的依赖文件
        core_deps = ["pyproject.toml", "requirements.txt", "uv.lock"]
        for dep in core_deps:
            assert (project_root / dep).exists(), f"核心依赖文件 {dep} 不存在"
        
        # 验证冗余文件已归档
        deprecated_deps = ["requirements-base.txt", "requirements-windows.txt", "requirements-compiled.txt"]
        for dep in deprecated_deps:
            # 根目录不应存在
            assert not (project_root / dep).exists(), f"冗余依赖文件 {dep} 仍在根目录"
            # 应在归档目录存在
            assert (project_root / "deprecated" / "dependencies" / dep).exists(), f"归档依赖文件 {dep} 未找到"
    
    def test_directory_structure_improvement(self):
        """测试目录结构改进效果"""
        project_root = Path.cwd()
        
        # 验证新目录结构
        expected_dirs = [
            "docs/ai_collaboration",
            "tests/integration",
            "tests/unit",
            "tests/legacy", 
            "deprecated/entry_files",
            "deprecated/dependencies",
            "temp"
        ]
        
        for dir_path in expected_dirs:
            assert (project_root / dir_path).exists(), f"目录结构 {dir_path} 不存在"
    
    def test_ai_collaboration_docs_migration(self):
        """测试AI协作文档迁移效果"""
        project_root = Path.cwd()
        
        # 验证AI协作文档已迁移
        ai_docs = ["CLAUDE.md", "GEMINI.md"]
        for doc in ai_docs:
            # 根目录不应存在
            assert not (project_root / doc).exists(), f"AI文档 {doc} 仍在根目录"
            # 应在ai_collaboration目录存在
            assert (project_root / "docs" / "ai_collaboration" / doc).exists(), f"AI文档 {doc} 未迁移到正确位置"
    
    def test_startup_performance_improvement(self):
        """测试启动性能改进"""
        # 测试aqua.py启动时间
        start_time = time.time()
        
        try:
            # 使用虚拟环境的python
            python_path = ".venv/bin/python" if os.path.exists(".venv/bin/python") else "python3"
            result = subprocess.run(
                [python_path, "aqua.py", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            end_time = time.time()
            startup_time = end_time - start_time
            
            # 启动时间应该 < 3秒 (优化目标)
            assert startup_time < 3.0, f"启动时间过长: {startup_time:.2f}秒，目标<3秒"
            
            # 如果成功，应该 < 1秒 (理想目标)
            if result.returncode == 0:
                print(f"✅ AQUA启动时间: {startup_time:.2f}秒")
            else:
                print(f"⚠️ AQUA启动有问题，但时间在限制内: {startup_time:.2f}秒")
                
        except subprocess.TimeoutExpired:
            pytest.fail("启动性能测试超时")
    
    def test_project_quality_score_improvement(self):
        """测试项目质量评分改进"""
        project_root = Path.cwd()
        
        # 计算项目质量评分指标
        quality_metrics = {
            "entry_simplification": 0,  # 入口简化 (20分)
            "dependency_management": 0,  # 依赖管理 (20分)
            "directory_organization": 0,  # 目录组织 (20分)
            "documentation_structure": 0,  # 文档结构 (20分)
            "test_framework": 0,  # 测试框架 (20分)
        }
        
        # 评估入口简化 (20分)
        core_entries = ["aqua.py", "main.py"]
        deprecated_entries = ["aqua.sh", "start_services.py", "Start-AQUA.ps1"]
        
        if all((project_root / entry).exists() for entry in core_entries):
            quality_metrics["entry_simplification"] += 10
        
        if all(not (project_root / entry).exists() for entry in deprecated_entries):
            quality_metrics["entry_simplification"] += 10
            
        # 评估依赖管理 (20分)
        core_deps = ["pyproject.toml", "requirements.txt", "uv.lock"]
        deprecated_deps = ["requirements-base.txt", "requirements-windows.txt", "requirements-compiled.txt"]
        
        if all((project_root / dep).exists() for dep in core_deps):
            quality_metrics["dependency_management"] += 10
            
        if all(not (project_root / dep).exists() for dep in deprecated_deps):
            quality_metrics["dependency_management"] += 10
        
        # 评估目录组织 (20分)
        key_dirs = [
            "docs/ai_collaboration",
            "tests/integration", 
            "deprecated/entry_files",
            "deprecated/dependencies"
        ]
        
        existing_dirs = sum(1 for d in key_dirs if (project_root / d).exists())
        quality_metrics["directory_organization"] = int((existing_dirs / len(key_dirs)) * 20)
        
        # 评估文档结构 (20分)
        ai_docs_migrated = all(
            (project_root / "docs" / "ai_collaboration" / doc).exists() 
            for doc in ["CLAUDE.md", "GEMINI.md"]
        )
        if ai_docs_migrated:
            quality_metrics["documentation_structure"] = 20
        
        # 评估测试框架 (20分)
        test_dirs = ["tests/integration", "tests/unit", "tests/legacy"]
        existing_test_dirs = sum(1 for d in test_dirs if (project_root / d).exists())
        quality_metrics["test_framework"] = int((existing_test_dirs / len(test_dirs)) * 20)
        
        # 计算总分
        total_score = sum(quality_metrics.values())
        
        print(f"\n📊 AQUA项目质量评分:")
        for metric, score in quality_metrics.items():
            print(f"  - {metric}: {score}/20")
        print(f"  总分: {total_score}/100")
        
        # 验证达到目标 (≥98分)
        assert total_score >= 98, f"项目质量评分未达标: {total_score}/100，目标≥98分"
    
    def test_optimization_completeness(self):
        """测试优化完整性"""
        project_root = Path.cwd()
        
        # 验证优化完整性检查清单
        completeness_checks = {
            "entry_files_archived": all(
                (project_root / "deprecated" / "entry_files" / f).exists()
                for f in ["aqua.sh", "start_services.py", "Start-AQUA.ps1"]
            ),
            "dependency_files_archived": all(
                (project_root / "deprecated" / "dependencies" / f).exists()
                for f in ["requirements-base.txt", "requirements-windows.txt", "requirements-compiled.txt"]
            ),
            "ai_docs_migrated": all(
                (project_root / "docs" / "ai_collaboration" / f).exists()
                for f in ["CLAUDE.md", "GEMINI.md"]
            ),
            "test_structure_created": all(
                (project_root / d).exists()
                for d in ["tests/integration", "tests/unit", "tests/legacy"]
            ),
            "archive_readme_created": all(
                (project_root / "deprecated" / d / "README.md").exists()
                for d in ["entry_files", "dependencies"]
            )
        }
        
        failed_checks = [k for k, v in completeness_checks.items() if not v]
        
        if failed_checks:
            pytest.fail(f"优化未完整: {failed_checks}")
        
        print("✅ AQUA项目级优化v5.1完整性验证通过")