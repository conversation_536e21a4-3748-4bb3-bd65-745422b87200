#!/usr/bin/env python3
"""
性能优化集成测试

测试完整的性能优化链：数据库优化 + 缓存系统 + API接口集成
"""

import pytest
import tempfile
import shutil
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from fastapi.testclient import TestClient
import duckdb
import json

from main import app
from src.database.connection_manager import DuckDBConnectionManager
from src.database.performance_optimizer import DatabasePerformanceOptimizer
from src.cache.cache_manager import MultiLevelCacheManager
from src.utils.exceptions import DatabaseException, CacheException

client = TestClient(app)


class TestPerformanceOptimizationIntegration:
    """性能优化集成测试类"""
    
    @pytest.fixture
    def temp_db_config(self):
        """创建临时数据库配置"""
        temp_dir = tempfile.mkdtemp()
        db_path = Path(temp_dir) / "test_perf.db"
        
        yield temp_dir, str(db_path)
        
        # 清理
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def real_db_manager(self, temp_db_config):
        """创建真实数据库管理器"""
        temp_dir, db_path = temp_db_config
        
        with patch('src.database.connection_manager.ConfigLoader') as mock_loader_class:
            mock_loader = Mock()
            mock_loader.get_config.return_value = {
                "database": {
                    "path": db_path,
                    "auto_create": True
                }
            }
            mock_loader_class.return_value = mock_loader
            
            manager = DuckDBConnectionManager("test")
            
            # 创建测试表和数据
            connection = manager.get_connection()
            
            # 创建股票数据表
            connection.execute("""
                CREATE TABLE IF NOT EXISTS stock_data (
                    symbol VARCHAR(10),
                    name VARCHAR(100),
                    date DATE,
                    open DOUBLE,
                    high DOUBLE,
                    low DOUBLE,
                    close DOUBLE,
                    volume BIGINT
                )
            """)
            
            # 插入测试数据
            test_data = [
                ("000001", "平安银行", "2023-01-01", 10.50, 10.60, 10.40, 10.55, 1000000),
                ("000001", "平安银行", "2023-01-02", 10.55, 10.65, 10.45, 10.60, 1100000),
                ("000001", "平安银行", "2023-01-03", 10.60, 10.70, 10.50, 10.65, 1200000),
                ("000002", "万科A", "2023-01-01", 15.20, 15.30, 15.10, 15.25, 800000),
                ("000002", "万科A", "2023-01-02", 15.25, 15.35, 15.15, 15.30, 850000),
            ]
            
            for data in test_data:
                connection.execute("""
                    INSERT INTO stock_data 
                    (symbol, name, date, open, high, low, close, volume) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, data)
            
            # 创建期货数据表
            connection.execute("""
                CREATE TABLE IF NOT EXISTS futures_data (
                    symbol VARCHAR(10),
                    name VARCHAR(100),
                    date DATE,
                    open DOUBLE,
                    high DOUBLE,
                    low DOUBLE,
                    close DOUBLE,
                    volume BIGINT
                )
            """)
            
            # 插入期货测试数据
            futures_data = [
                ("IF2301", "沪深300指数期货", "2023-01-01", 4000.0, 4010.0, 3990.0, 4005.0, 50000),
                ("IF2301", "沪深300指数期货", "2023-01-02", 4005.0, 4015.0, 3995.0, 4010.0, 52000),
                ("IC2301", "中证500指数期货", "2023-01-01", 5500.0, 5520.0, 5480.0, 5510.0, 30000),
            ]
            
            for data in futures_data:
                connection.execute("""
                    INSERT INTO futures_data 
                    (symbol, name, date, open, high, low, close, volume) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, data)
            
            yield manager
            
            # 清理
            manager.close_connection()
    
    @pytest.fixture
    def real_cache_manager(self, temp_db_config):
        """创建真实缓存管理器"""
        temp_dir, _ = temp_db_config
        cache_dir = Path(temp_dir) / "cache"
        cache_dir.mkdir(exist_ok=True)
        
        cache_manager = MultiLevelCacheManager(
            l1_max_size=100,
            l2_cache_dir=str(cache_dir),
            l2_max_size=1000,
            default_ttl=300
        )
        
        yield cache_manager
        
        # 清理
        cache_manager.clear()
    
    def test_database_optimization_integration(self, real_db_manager):
        """测试数据库优化集成"""
        # 创建性能优化器
        optimizer = DatabasePerformanceOptimizer(real_db_manager)
        
        # 分析表性能
        analysis = optimizer.analyze_table_performance("stock_data")
        
        assert analysis["table_name"] == "stock_data"
        assert analysis["total_rows"] > 0
        assert analysis["total_size"] > 0
        assert "column_stats" in analysis
        
        # 优化表索引
        optimization_result = optimizer.optimize_table("stock_data")
        
        assert optimization_result["table_name"] == "stock_data"
        assert optimization_result["optimization_applied"] is True
        assert len(optimization_result["indexes_created"]) > 0
        
        # 验证索引创建后的查询性能
        start_time = time.time()
        connection = real_db_manager.get_connection()
        result = connection.execute(
            "SELECT * FROM stock_data WHERE symbol = '000001' ORDER BY date"
        ).fetchall()
        end_time = time.time()
        
        assert len(result) == 3
        assert (end_time - start_time) < 0.1  # 索引应该提高查询速度
    
    def test_cache_system_integration(self, real_cache_manager):
        """测试缓存系统集成"""
        # 测试多级缓存
        test_data = {
            "symbol": "000001",
            "data": [
                {"date": "2023-01-01", "close": 10.55},
                {"date": "2023-01-02", "close": 10.60}
            ]
        }
        
        cache_key = "stock_data:000001:2023-01-01:2023-01-02"
        
        # 第一次存储 - 应该存储到L1缓存
        real_cache_manager.put(cache_key, test_data)
        
        # 立即获取 - 应该从L1缓存命中
        cached_data = real_cache_manager.get(cache_key)
        
        assert cached_data is not None
        assert cached_data["symbol"] == "000001"
        assert len(cached_data["data"]) == 2
        
        # 获取缓存统计
        stats = real_cache_manager.get_cache_statistics()
        
        assert stats["l1_cache"]["hit_count"] >= 1
        assert stats["l1_cache"]["miss_count"] >= 0
        assert stats["l1_cache"]["size"] >= 1
        
        # 测试缓存过期
        real_cache_manager.put(cache_key, test_data, ttl=1)
        time.sleep(1.1)  # 等待过期
        
        expired_data = real_cache_manager.get(cache_key)
        assert expired_data is None
    
    def test_full_performance_api_integration(self, real_db_manager, real_cache_manager):
        """测试完整的性能API集成"""
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            with patch('src.api.routers.performance_router.cache_manager', real_cache_manager):
                # 使用真实数据库管理器
                mock_manager_class.return_value = real_db_manager
                
                # 第一次请求 - 数据库查询 + 缓存存储
                start_time = time.time()
                response1 = client.get("/api/performance/stock/000001")
                end_time = time.time()
                
                assert response1.status_code == 200
                data1 = response1.json()
                assert data1["success"] is True
                assert len(data1["data"]) == 3  # 000001有3条记录
                assert data1["from_cache"] is False
                
                first_response_time = end_time - start_time
                
                # 第二次请求 - 缓存命中
                start_time = time.time()
                response2 = client.get("/api/performance/stock/000001")
                end_time = time.time()
                
                assert response2.status_code == 200
                data2 = response2.json()
                assert data2["success"] is True
                assert data2["from_cache"] is True
                
                second_response_time = end_time - start_time
                
                # 缓存命中的响应时间应该更快
                assert second_response_time < first_response_time
                assert second_response_time < 0.1  # 缓存响应应该很快
    
    def test_database_cache_consistency(self, real_db_manager, real_cache_manager):
        """测试数据库和缓存一致性"""
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            with patch('src.api.routers.performance_router.cache_manager', real_cache_manager):
                mock_manager_class.return_value = real_db_manager
                
                # 第一次请求获取数据
                response1 = client.get("/api/performance/stock/000001")
                data1 = response1.json()
                
                # 直接从数据库查询验证数据一致性
                connection = real_db_manager.get_connection()
                db_result = connection.execute(
                    "SELECT * FROM stock_data WHERE symbol = '000001' ORDER BY date"
                ).fetchall()
                
                assert len(data1["data"]) == len(db_result)
                
                # 验证数据内容一致性
                for i, db_row in enumerate(db_result):
                    api_row = data1["data"][i]
                    assert api_row["symbol"] == db_row[0]
                    assert api_row["name"] == db_row[1]
                    assert float(api_row["close"]) == db_row[6]
    
    def test_performance_under_load(self, real_db_manager, real_cache_manager):
        """测试负载下的性能"""
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            with patch('src.api.routers.performance_router.cache_manager', real_cache_manager):
                mock_manager_class.return_value = real_db_manager
                
                # 并发请求测试
                import threading
                results = []
                
                def make_request():
                    start_time = time.time()
                    response = client.get("/api/performance/stock/000001")
                    end_time = time.time()
                    results.append({
                        "status_code": response.status_code,
                        "response_time": end_time - start_time,
                        "from_cache": response.json().get("from_cache", False)
                    })
                
                # 创建多个线程同时请求
                threads = []
                for _ in range(10):
                    thread = threading.Thread(target=make_request)
                    threads.append(thread)
                    thread.start()
                
                # 等待所有请求完成
                for thread in threads:
                    thread.join()
                
                # 验证结果
                assert len(results) == 10
                
                # 所有请求都应该成功
                for result in results:
                    assert result["status_code"] == 200
                    assert result["response_time"] < 1.0  # 响应时间应该合理
                
                # 第一个请求从数据库获取，其他请求应该从缓存获取
                cache_hits = sum(1 for r in results if r["from_cache"])
                assert cache_hits >= 8  # 大部分请求应该命中缓存
    
    def test_cache_invalidation_integration(self, real_db_manager, real_cache_manager):
        """测试缓存失效集成"""
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            with patch('src.api.routers.performance_router.cache_manager', real_cache_manager):
                mock_manager_class.return_value = real_db_manager
                
                # 第一次请求
                response1 = client.get("/api/performance/stock/000001")
                data1 = response1.json()
                assert data1["from_cache"] is False
                
                # 第二次请求 - 缓存命中
                response2 = client.get("/api/performance/stock/000001")
                data2 = response2.json()
                assert data2["from_cache"] is True
                
                # 手动清理缓存
                real_cache_manager.clear()
                
                # 第三次请求 - 缓存失效，重新查询数据库
                response3 = client.get("/api/performance/stock/000001")
                data3 = response3.json()
                assert data3["from_cache"] is False
    
    def test_error_handling_integration(self, real_cache_manager):
        """测试错误处理集成"""
        with patch('src.api.routers.performance_router.cache_manager', real_cache_manager):
            # 测试数据库连接错误
            with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
                mock_manager = Mock()
                mock_manager.execute_query.side_effect = DatabaseException("连接失败", "DATABASE_CONNECTION_ERROR")
                mock_manager_class.return_value = mock_manager
                
                # 缓存中也没有数据
                real_cache_manager.clear()
                
                response = client.get("/api/performance/stock/000001")
                # 应该返回错误状态码
                assert response.status_code in [500, 503]
    
    def test_cache_error_fallback(self, real_db_manager):
        """测试缓存错误降级"""
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            mock_manager_class.return_value = real_db_manager
            
            # 模拟缓存错误
            mock_cache = Mock()
            mock_cache.get.side_effect = CacheException("缓存服务不可用", "CACHE_SERVICE_ERROR")
            mock_cache.put.side_effect = CacheException("缓存服务不可用", "CACHE_SERVICE_ERROR")
            
            with patch('src.api.routers.performance_router.cache_manager', mock_cache):
                response = client.get("/api/performance/stock/000001")
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert data["from_cache"] is False
                # 缓存错误时应该降级到数据库直接查询
    
    def test_performance_optimization_benchmarks(self, real_db_manager, real_cache_manager):
        """测试性能优化基准测试"""
        # 创建性能优化器
        optimizer = DatabasePerformanceOptimizer(real_db_manager)
        
        # 优化数据库
        optimizer.optimize_table("stock_data")
        optimizer.optimize_table("futures_data")
        
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            with patch('src.api.routers.performance_router.cache_manager', real_cache_manager):
                mock_manager_class.return_value = real_db_manager
                
                # 测试不同查询的性能
                test_cases = [
                    ("/api/performance/stock/000001", "stock_data"),
                    ("/api/performance/stock/000002", "stock_data"),
                    ("/api/performance/futures/IF2301", "futures_data"),
                    ("/api/performance/futures/IC2301", "futures_data"),
                ]
                
                performance_results = []
                
                for endpoint, table_name in test_cases:
                    # 清理缓存确保每次都从数据库查询
                    real_cache_manager.clear()
                    
                    start_time = time.time()
                    response = client.get(endpoint)
                    end_time = time.time()
                    
                    performance_results.append({
                        "endpoint": endpoint,
                        "table_name": table_name,
                        "response_time": end_time - start_time,
                        "status_code": response.status_code
                    })
                
                # 验证性能结果
                for result in performance_results:
                    assert result["status_code"] == 200
                    assert result["response_time"] < 0.5  # 优化后响应时间应该很快
                
                # 计算平均响应时间
                avg_response_time = sum(r["response_time"] for r in performance_results) / len(performance_results)
                assert avg_response_time < 0.2  # 平均响应时间应该很快
    
    def test_memory_usage_optimization(self, real_db_manager, real_cache_manager):
        """测试内存使用优化"""
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            with patch('src.api.routers.performance_router.cache_manager', real_cache_manager):
                mock_manager_class.return_value = real_db_manager
                
                # 获取初始内存统计
                initial_stats = real_cache_manager.get_cache_statistics()
                
                # 执行多个请求
                endpoints = [
                    "/api/performance/stock/000001",
                    "/api/performance/stock/000002",
                    "/api/performance/futures/IF2301",
                    "/api/performance/futures/IC2301",
                ]
                
                for endpoint in endpoints:
                    response = client.get(endpoint)
                    assert response.status_code == 200
                
                # 获取最终内存统计
                final_stats = real_cache_manager.get_cache_statistics()
                
                # 验证内存使用合理
                assert final_stats["l1_cache"]["size"] <= final_stats["l1_cache"]["max_size"]
                assert final_stats["l2_cache"]["size"] <= final_stats["l2_cache"]["max_size"]
                
                # 验证缓存命中率
                total_requests = final_stats["l1_cache"]["hit_count"] + final_stats["l1_cache"]["miss_count"]
                if total_requests > 0:
                    hit_rate = final_stats["l1_cache"]["hit_count"] / total_requests
                    # 由于是重复请求，命中率应该较高
                    assert hit_rate >= 0.5


class TestPerformanceOptimizationEdgeCases:
    """性能优化边界情况测试"""
    
    def test_empty_database_handling(self):
        """测试空数据库处理"""
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            mock_manager = Mock()
            mock_manager.execute_query.return_value = []
            mock_manager_class.return_value = mock_manager
            
            with patch('src.api.routers.performance_router.cache_manager') as mock_cache:
                mock_cache.get.return_value = None
                
                response = client.get("/api/performance/stock/000001")
                assert response.status_code == 200
                
                data = response.json()
                assert data["success"] is True
                assert data["data"] == []
    
    def test_large_dataset_handling(self, real_db_manager):
        """测试大数据集处理"""
        # 创建大量测试数据
        connection = real_db_manager.get_connection()
        
        # 生成大量股票数据
        import random
        from datetime import datetime, timedelta
        
        large_data = []
        base_date = datetime(2023, 1, 1)
        
        for i in range(1000):  # 1000条记录
            date = base_date + timedelta(days=i % 365)
            price = 10.0 + random.uniform(-1, 1)
            large_data.append((
                "TEST001",
                "测试股票",
                date.strftime("%Y-%m-%d"),
                price,
                price + random.uniform(0, 0.5),
                price - random.uniform(0, 0.5),
                price + random.uniform(-0.3, 0.3),
                random.randint(100000, 2000000)
            ))
        
        for data in large_data:
            connection.execute("""
                INSERT INTO stock_data 
                (symbol, name, date, open, high, low, close, volume) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, data)
        
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            mock_manager_class.return_value = real_db_manager
            
            with patch('src.api.routers.performance_router.cache_manager') as mock_cache:
                mock_cache.get.return_value = None
                
                start_time = time.time()
                response = client.get("/api/performance/stock/TEST001?limit=100")
                end_time = time.time()
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert len(data["data"]) == 100  # 限制返回100条
                
                # 即使是大数据集，响应时间也应该合理
                assert (end_time - start_time) < 2.0
    
    def test_concurrent_cache_access(self, real_cache_manager):
        """测试并发缓存访问"""
        import threading
        import time
        
        results = []
        
        def cache_operation(operation_id):
            try:
                # 写入操作
                real_cache_manager.put(f"test_key_{operation_id}", {"id": operation_id, "data": "test"})
                
                # 读取操作
                result = real_cache_manager.get(f"test_key_{operation_id}")
                
                results.append({
                    "operation_id": operation_id,
                    "success": result is not None,
                    "data": result
                })
            except Exception as e:
                results.append({
                    "operation_id": operation_id,
                    "success": False,
                    "error": str(e)
                })
        
        # 创建多个线程并发访问缓存
        threads = []
        for i in range(20):
            thread = threading.Thread(target=cache_operation, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(results) == 20
        
        # 所有操作都应该成功
        successful_operations = [r for r in results if r["success"]]
        assert len(successful_operations) == 20
        
        # 验证缓存统计
        stats = real_cache_manager.get_cache_statistics()
        assert stats["l1_cache"]["size"] == 20


if __name__ == "__main__":
    pytest.main([__file__, "-v"])