#!/usr/bin/env python3
"""
数据导入集成测试
"""

import pytest
import tempfile
import os
import csv
from pathlib import Path
from unittest.mock import Mock, patch

from src.data_import.csv_importer import CSVImporter
from src.data_import.mysql_importer import MySQLImporter
from src.utils.config_loader_v2 import ConfigLoaderV2


class TestDataImportIntegration:
    """数据导入集成测试"""

    def setup_method(self):
        """测试前设置"""
        # 创建临时目录和CSV文件
        self.temp_dir = tempfile.mkdtemp()
        self.create_test_csv_files()
        
        # 模拟配置
        self.config = {
            'csv': {
                'data_dir': self.temp_dir,
                'batch_size': 1000,
                'encoding': 'utf-8'
            },
            'mysql': {
                'host': 'localhost',
                'port': 3306,
                'user': 'test_user',
                'password': 'test_password',
                'database': 'test_db'
            }
        }
        
        # 创建连接管理器模拟
        self.mock_connection_manager = Mock()
        self.mock_connection_manager.table_exists.return_value = False
        self.mock_connection_manager.create_table_from_csv.return_value = True
        self.mock_connection_manager.insert_from_csv.return_value = True
        self.mock_connection_manager.create_table.return_value = True
        self.mock_connection_manager.insert_data.return_value = True
        
        # 创建导入器实例
        self.csv_importer = CSVImporter(self.config, self.mock_connection_manager)
        self.mysql_importer = MySQLImporter(self.config, self.mock_connection_manager)

    def teardown_method(self):
        """测试后清理"""
        # 删除临时目录和文件
        import shutil
        shutil.rmtree(self.temp_dir)

    def create_test_csv_files(self):
        """创建测试CSV文件"""
        # 创建股票基础信息CSV
        stock_basic_file = Path(self.temp_dir) / 'stock_basic_info.csv'
        with open(stock_basic_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['symbol', 'name', 'market'])
            writer.writerow(['000001.SZ', '平安银行', 'SZ'])
            writer.writerow(['000002.SZ', '万科A', 'SZ'])
            writer.writerow(['600000.SH', '浦发银行', 'SH'])
        
        # 创建股票日线数据CSV
        stock_daily_file = Path(self.temp_dir) / '000001.SZ_daily.csv'
        with open(stock_daily_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['symbol', 'trade_date', 'open_price', 'close_price', 'volume'])
            writer.writerow(['000001.SZ', '2025-01-01', '10.50', '10.80', '1000000'])
            writer.writerow(['000001.SZ', '2025-01-02', '10.80', '11.00', '1200000'])
            writer.writerow(['000001.SZ', '2025-01-03', '11.00', '10.90', '900000'])
        
        # 创建期货基础信息CSV
        futures_basic_file = Path(self.temp_dir) / 'futures_basic_info.csv'
        with open(futures_basic_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['symbol', 'name', 'exchange', 'multiplier'])
            writer.writerow(['CU2501', '沪铜2501', 'SHFE', '5'])
            writer.writerow(['AL2501', '沪铝2501', 'SHFE', '5'])
            writer.writerow(['ZN2501', '沪锌2501', 'SHFE', '5'])
        
        # 创建期货Tick数据CSV
        futures_tick_file = Path(self.temp_dir) / 'CU2501_tick.csv'
        with open(futures_tick_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['symbol', 'datetime', 'last_price', 'volume', 'bid_price', 'ask_price'])
            writer.writerow(['CU2501', '2025-01-01 09:00:00', '75000', '10', '74990', '75010'])
            writer.writerow(['CU2501', '2025-01-01 09:00:01', '75010', '5', '75000', '75020'])
            writer.writerow(['CU2501', '2025-01-01 09:00:02', '75020', '8', '75010', '75030'])
        
        # 创建错误格式的CSV文件
        error_file = Path(self.temp_dir) / 'error_data.csv'
        with open(error_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['col1', 'col2'])
            writer.writerow(['value1'])  # 缺少列值
            writer.writerow(['value2', 'value3', 'extra_value'])  # 多余列值

    def test_csv_import_single_file_integration(self):
        """测试CSV单文件导入集成"""
        # 导入股票基础信息
        stock_basic_file = Path(self.temp_dir) / 'stock_basic_info.csv'
        result = self.csv_importer.import_single_file(stock_basic_file)
        
        assert result['success'] is True
        assert result['table_name'] == 'stock_basic_info'
        assert result['rows_imported'] == 3
        
        # 验证连接管理器方法被调用
        self.mock_connection_manager.table_exists.assert_called_with('stock_basic_info')
        self.mock_connection_manager.create_table_from_csv.assert_called_once()

    def test_csv_import_directory_integration(self):
        """测试CSV目录导入集成"""
        # 导入整个目录
        result = self.csv_importer.import_directory(Path(self.temp_dir))
        
        assert result['success'] is True
        assert result['total_files'] == 5  # 4个正常文件 + 1个错误文件
        assert result['successful_imports'] >= 4  # 至少4个成功
        
        # 验证导入结果
        summary = self.csv_importer.get_import_summary()
        assert summary['total_files'] >= 4
        assert summary['successful_imports'] >= 4

    def test_csv_import_with_pattern_integration(self):
        """测试CSV模式导入集成"""
        # 只导入基础信息文件
        result = self.csv_importer.import_directory(Path(self.temp_dir), pattern='*basic*')
        
        assert result['success'] is True
        assert result['total_files'] == 2  # stock_basic_info + futures_basic_info
        assert result['successful_imports'] == 2
        
        # 验证正确的表被创建
        summary = self.csv_importer.get_import_summary()
        table_names = [table['name'] for table in summary['tables_created']]
        assert 'stock_basic_info' in table_names
        assert 'futures_basic_info' in table_names

    def test_csv_import_stock_symbol_detection_integration(self):
        """测试CSV股票代码检测集成"""
        # 导入股票日线数据
        stock_daily_file = Path(self.temp_dir) / '000001.SZ_daily.csv'
        result = self.csv_importer.import_single_file(stock_daily_file)
        
        assert result['success'] is True
        assert result['table_name'] == 'stock_daily'  # 自动检测为stock_daily表
        assert result['rows_imported'] == 3

    def test_csv_import_futures_symbol_detection_integration(self):
        """测试CSV期货代码检测集成"""
        # 导入期货Tick数据
        futures_tick_file = Path(self.temp_dir) / 'CU2501_tick.csv'
        result = self.csv_importer.import_single_file(futures_tick_file)
        
        assert result['success'] is True
        assert result['table_name'] == 'futures_tick'  # 自动检测为futures_tick表
        assert result['rows_imported'] == 3

    def test_csv_import_existing_table_integration(self):
        """测试CSV导入到现有表集成"""
        # 模拟表已存在
        self.mock_connection_manager.table_exists.return_value = True
        
        # 导入股票基础信息
        stock_basic_file = Path(self.temp_dir) / 'stock_basic_info.csv'
        result = self.csv_importer.import_single_file(stock_basic_file)
        
        assert result['success'] is True
        assert result['table_name'] == 'stock_basic_info'
        assert result['rows_imported'] == 3
        
        # 验证使用插入而不是创建表
        self.mock_connection_manager.insert_from_csv.assert_called_once()
        self.mock_connection_manager.create_table_from_csv.assert_not_called()

    def test_csv_import_error_handling_integration(self):
        """测试CSV导入错误处理集成"""
        # 导入错误格式的文件
        error_file = Path(self.temp_dir) / 'error_data.csv'
        result = self.csv_importer.import_single_file(error_file)
        
        # 虽然格式不完美，但应该能够处理
        assert result['success'] in [True, False]  # 取决于具体实现
        
        # 导入不存在的文件
        non_existent_file = Path(self.temp_dir) / 'non_existent.csv'
        result = self.csv_importer.import_single_file(non_existent_file)
        
        assert result['success'] is False
        assert '文件验证失败' in result['error']

    def test_csv_batch_import_integration(self):
        """测试CSV批量导入集成"""
        # 创建多个数据源
        sources = [
            Path(self.temp_dir) / 'stock_basic_info.csv',
            Path(self.temp_dir) / 'futures_basic_info.csv'
        ]
        
        # 批量导入
        progress_calls = []
        def progress_callback(current, total, filename):
            progress_calls.append((current, total, filename))
        
        result = self.csv_importer.batch_import(sources, progress_callback=progress_callback)
        
        assert result['success'] is True
        assert result['total_files'] == 2
        assert result['successful_imports'] == 2
        assert len(progress_calls) == 2

    @patch('pymysql.connect')
    def test_mysql_import_single_table_integration(self, mock_connect):
        """测试MySQL单表导入集成"""
        # 模拟MySQL连接
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_connection
        
        # 模拟表结构查询
        mock_cursor.fetchall.side_effect = [
            [  # 表结构
                ('id', 'int', 'NO', 'PRI', None, 'auto_increment'),
                ('symbol', 'varchar(10)', 'NO', '', None, ''),
                ('name', 'varchar(50)', 'NO', '', None, '')
            ],
            [  # 表数据
                (1, '000001.SZ', '平安银行'),
                (2, '000002.SZ', '万科A'),
                (3, '600000.SH', '浦发银行')
            ]
        ]
        
        # 连接MySQL
        connect_result = self.mysql_importer.connect_mysql()
        assert connect_result is True
        
        # 导入单个表
        result = self.mysql_importer.import_table('stock_basic_info')
        
        assert result['success'] is True
        assert result['table_name'] == 'stock_basic_info'
        assert result['rows_imported'] == 3
        
        # 验证DuckDB表创建和数据插入
        self.mock_connection_manager.create_table.assert_called_once()
        self.mock_connection_manager.insert_data.assert_called_once()

    @patch('pymysql.connect')
    def test_mysql_import_all_tables_integration(self, mock_connect):
        """测试MySQL所有表导入集成"""
        # 模拟MySQL连接
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_connection
        
        # 模拟表列表和数据
        mock_cursor.fetchall.side_effect = [
            [('stock_basic_info',), ('futures_basic_info',)],  # 表列表
            [('id', 'int', 'NO', 'PRI', None, 'auto_increment')],  # 表1结构
            [(1,), (2,)],  # 表1数据
            [('symbol', 'varchar(20)', 'NO', '', None, '')],  # 表2结构
            [('CU2501',), ('AL2501',)]  # 表2数据
        ]
        
        # 连接MySQL
        connect_result = self.mysql_importer.connect_mysql()
        assert connect_result is True
        
        # 导入所有表
        result = self.mysql_importer.import_all_tables()
        
        assert result['success'] is True
        assert result['total_tables'] == 2
        assert result['successful_imports'] == 2
        assert result['failed_imports'] == 0

    @patch('pymysql.connect')
    def test_mysql_import_specific_tables_integration(self, mock_connect):
        """测试MySQL指定表导入集成"""
        # 模拟MySQL连接
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_connection
        
        # 模拟表结构和数据
        mock_cursor.fetchall.side_effect = [
            [('id', 'int', 'NO', 'PRI', None, 'auto_increment')],  # 表1结构
            [(1,), (2,), (3,)],  # 表1数据
            [('symbol', 'varchar(20)', 'NO', '', None, '')],  # 表2结构
            [('CU2501',), ('AL2501,')]  # 表2数据
        ]
        
        # 连接MySQL
        connect_result = self.mysql_importer.connect_mysql()
        assert connect_result is True
        
        # 导入指定表
        tables_to_import = ['stock_basic_info', 'futures_basic_info']
        result = self.mysql_importer.import_specific_tables(tables_to_import)
        
        assert result['success'] is True
        assert result['total_tables'] == 2
        assert result['successful_imports'] == 2

    @patch('pymysql.connect')
    def test_mysql_connection_error_integration(self, mock_connect):
        """测试MySQL连接错误集成"""
        # 模拟连接失败
        mock_connect.side_effect = Exception("Connection failed")
        
        # 尝试连接
        connect_result = self.mysql_importer.connect_mysql()
        assert connect_result is False
        
        # 尝试导入表
        result = self.mysql_importer.import_table('test_table')
        assert result['success'] is False
        assert 'MySQL连接不可用' in result['error']

    def test_mixed_import_workflow_integration(self):
        """测试混合导入工作流集成"""
        # 1. 首先导入CSV文件
        csv_result = self.csv_importer.import_directory(Path(self.temp_dir))
        assert csv_result['success'] is True
        
        # 2. 获取CSV导入摘要
        csv_summary = self.csv_importer.get_import_summary()
        assert csv_summary['successful_imports'] >= 4
        
        # 3. 模拟MySQL导入
        with patch('pymysql.connect') as mock_connect:
            mock_connection = Mock()
            mock_cursor = Mock()
            mock_connection.cursor.return_value = mock_cursor
            mock_connect.return_value = mock_connection
            
            # 模拟MySQL数据
            mock_cursor.fetchall.side_effect = [
                [('additional_table',)],  # 表列表
                [('id', 'int', 'NO', 'PRI', None, 'auto_increment')],  # 表结构
                [(1,), (2,), (3,)]  # 表数据
            ]
            
            # 连接和导入
            mysql_connect_result = self.mysql_importer.connect_mysql()
            assert mysql_connect_result is True
            
            mysql_result = self.mysql_importer.import_all_tables()
            assert mysql_result['success'] is True
        
        # 4. 获取MySQL导入摘要
        mysql_summary = self.mysql_importer.get_import_summary()
        assert mysql_summary['successful_imports'] >= 1

    def test_import_progress_tracking_integration(self):
        """测试导入进度追踪集成"""
        # 创建多个文件进行批量导入
        files = [
            Path(self.temp_dir) / 'stock_basic_info.csv',
            Path(self.temp_dir) / 'futures_basic_info.csv',
            Path(self.temp_dir) / '000001.SZ_daily.csv',
            Path(self.temp_dir) / 'CU2501_tick.csv'
        ]
        
        # 收集进度信息
        progress_history = []
        
        def progress_callback(current, total, filename):
            progress_history.append({
                'current': current,
                'total': total,
                'filename': filename.name,
                'progress': current / total * 100
            })
        
        # 批量导入
        result = self.csv_importer.batch_import(files, progress_callback=progress_callback)
        
        assert result['success'] is True
        assert len(progress_history) == 4
        
        # 验证进度递增
        for i, progress in enumerate(progress_history):
            assert progress['current'] == i + 1
            assert progress['total'] == 4
            assert progress['progress'] == (i + 1) / 4 * 100

    def test_import_error_recovery_integration(self):
        """测试导入错误恢复集成"""
        # 模拟部分导入失败
        def mock_create_table_from_csv(table_name, file_path):
            if 'error' in str(file_path):
                return False
            return True
        
        self.mock_connection_manager.create_table_from_csv.side_effect = mock_create_table_from_csv
        
        # 导入目录（包含错误文件）
        result = self.csv_importer.import_directory(Path(self.temp_dir))
        
        # 应该部分成功
        assert result['success'] is True  # 总体成功
        assert result['successful_imports'] >= 4  # 至少4个成功
        assert result['failed_imports'] <= 1  # 最多1个失败
        
        # 验证错误被记录
        summary = self.csv_importer.get_import_summary()
        assert summary['failed_imports'] <= 1

    def test_import_data_validation_integration(self):
        """测试导入数据验证集成"""
        # 创建包含特殊字符的CSV文件
        special_file = Path(self.temp_dir) / 'special_chars.csv'
        with open(special_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['id', 'name', 'description'])
            writer.writerow([1, '测试数据', '包含中文的描述'])
            writer.writerow([2, "O'Reilly", "Name with apostrophe"])
            writer.writerow([3, 'Multi\nLine', 'Description with\nnewline'])
        
        # 导入特殊字符文件
        result = self.csv_importer.import_single_file(special_file)
        
        assert result['success'] is True
        assert result['rows_imported'] == 3
        
        # 验证数据被正确处理
        self.mock_connection_manager.create_table_from_csv.assert_called_with(
            'special_chars', special_file
        )

    def test_import_performance_estimation_integration(self):
        """测试导入性能估算集成"""
        # 创建大文件进行性能测试
        large_file = Path(self.temp_dir) / 'large_data.csv'
        with open(large_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['id', 'name', 'value'])
            for i in range(1000):
                writer.writerow([i, f'item_{i}', i * 1.5])
        
        # 估算导入时间
        estimated_time = self.csv_importer.estimate_import_time(large_file)
        assert estimated_time > 0
        
        # 实际导入
        result = self.csv_importer.import_single_file(large_file)
        
        assert result['success'] is True
        assert result['rows_imported'] == 1000

    def test_import_configuration_integration(self):
        """测试导入配置集成"""
        # 测试不同的配置参数
        custom_config = {
            'csv': {
                'data_dir': self.temp_dir,
                'batch_size': 500,  # 自定义批量大小
                'encoding': 'utf-8',
                'skip_errors': True  # 跳过错误
            }
        }
        
        # 创建自定义配置的导入器
        custom_importer = CSVImporter(custom_config, self.mock_connection_manager)
        
        # 导入文件
        stock_basic_file = Path(self.temp_dir) / 'stock_basic_info.csv'
        result = custom_importer.import_single_file(stock_basic_file)
        
        assert result['success'] is True
        assert result['rows_imported'] == 3
        
        # 验证配置被正确应用
        assert custom_importer.csv_config['batch_size'] == 500