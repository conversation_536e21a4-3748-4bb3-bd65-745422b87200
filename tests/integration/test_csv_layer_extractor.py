#!/usr/bin/env python3
"""
CsvLayerExtractor集成测试

测试Phase 3.2.1实现的CSV分层提取器功能：
- CsvLayerExtractor基本功能和配置验证
- 与现有CSVImporter的集成验证
- 数据库写入和元数据字段验证
- 增量导入和状态追踪验证

遵循TDD原则：验证CSV分层提取器的完整性和可靠性
"""

import tempfile
import shutil
import json
from pathlib import Path
from typing import Dict, Any
import pytest
import polars as pl

from src.data_import.extractors.csv_layer_extractor import CsvLayerExtractor
from src.data_import.extractors.simple_extractor import ExtractorException, DataSourceError, DataValidationError
from src.utils.time_utils import get_beijing_time_now


class TestCsvLayerExtractorBasic:
    """CsvLayerExtractor基础功能测试"""
    
    def setup_method(self):
        """测试初始化"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.csv_data_dir = self.temp_dir / "csv_data"
        self.csv_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建基础配置
        self.base_config = {
            "name": "test_csv_extractor",
            "data_source": "csv",
            "target_table": "csv_fut_main_contract_kline_15min",
            "data_dir": str(self.csv_data_dir),
            "batch_size": 100,
            "enable_validation": True,
            "enable_status_tracking": False  # 禁用状态追踪简化测试
        }
        
        # 创建测试CSV文件
        self.create_test_csv_files()
    
    def teardown_method(self):
        """清理测试资源"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def create_test_csv_files(self):
        """创建测试CSV文件"""
        self.test_csv_files = []
        
        # 创建符合期货K线数据格式的CSV文件
        for i in range(3):
            csv_file = self.csv_data_dir / f"futures_kline_test_{i+1}.csv"
            
            # 标准期货K线数据格式
            content = "timestamp,contract_code,open_price,high_price,low_price,close_price,volume,amount\n"
            
            # 生成测试数据
            base_price = 3800 + i * 50
            for j in range(5):  # 每个文件5条记录
                timestamp = f"2023-01-{i+1:02d} {9+j:02d}:00:00"
                contract = f"RB230{i+1}"
                open_price = base_price + j
                high_price = open_price + 10
                low_price = open_price - 5
                close_price = open_price + 2
                volume = 1000 + j * 100
                amount = close_price * volume
                
                content += f"{timestamp},{contract},{open_price:.1f},{high_price:.1f},{low_price:.1f},{close_price:.1f},{volume},{amount:.1f}\n"
            
            csv_file.write_text(content, encoding='utf-8')
            self.test_csv_files.append(csv_file)
    
    def test_csv_layer_extractor_initialization(self):
        """测试CsvLayerExtractor初始化"""
        extractor = CsvLayerExtractor(self.base_config)
        
        # 验证基本属性
        assert extractor.name == "test_csv_extractor"
        assert extractor.data_source == "csv"
        assert extractor.target_table == "csv_fut_main_contract_kline_15min"
        assert extractor.data_dir.resolve() == self.csv_data_dir.resolve()
        assert extractor.config.get("batch_size") == 100
        
        # 验证SimpleExtractor接口
        assert hasattr(extractor, 'extract')
        assert hasattr(extractor, 'get_status')
        assert hasattr(extractor, 'validate_config')
    
    def test_csv_config_validation(self):
        """测试CSV配置验证"""
        # 测试正确配置
        extractor = CsvLayerExtractor(self.base_config)
        validation = extractor.validate_config()
        assert validation["valid"] == True
        assert len(validation["errors"]) == 0
        
        # 测试错误的数据源
        invalid_config = self.base_config.copy()
        invalid_config["data_source"] = "mysql"
        
        with pytest.raises(DataSourceError):
            CsvLayerExtractor(invalid_config)
        
        # 测试缺少data_dir
        invalid_config = self.base_config.copy()
        del invalid_config["data_dir"]
        
        with pytest.raises(DataValidationError):
            CsvLayerExtractor(invalid_config)
    
    def test_csv_file_scanning(self):
        """测试CSV文件扫描功能"""
        extractor = CsvLayerExtractor(self.base_config)
        
        # 测试文件扫描
        csv_files = extractor._scan_csv_files()
        
        assert len(csv_files) == 3, "应该扫描到3个CSV文件"
        
        # 解析路径以处理符号链接
        resolved_csv_files = [f.resolve() for f in csv_files]
        resolved_test_files = [f.resolve() for f in self.test_csv_files]
        
        for csv_file in csv_files:
            assert csv_file.suffix == '.csv'
            assert csv_file.exists()
        
        # 验证扫描到的文件与创建的文件匹配
        for resolved_csv in resolved_csv_files:
            assert resolved_csv in resolved_test_files
    
    def test_csv_file_reading_with_encoding_fallback(self):
        """测试CSV文件读取和编码回退"""
        extractor = CsvLayerExtractor(self.base_config)
        
        test_file = self.test_csv_files[0]
        
        # 测试正常读取
        df = extractor._read_csv_with_fallback_encoding(test_file)
        assert isinstance(df, pl.DataFrame)
        assert len(df) == 5, "应该读取到5条记录"
        assert "timestamp" in df.columns
        assert "contract_code" in df.columns
        assert "open_price" in df.columns
    
    def test_dataframe_standardization(self):
        """测试数据标准化功能"""
        extractor = CsvLayerExtractor(self.base_config)
        
        # 读取测试文件
        test_file = self.test_csv_files[0]
        raw_df = extractor._read_csv_with_fallback_encoding(test_file)
        
        # 标准化数据
        standardized_df = extractor._standardize_dataframe(raw_df, test_file)
        
        # 验证标准化结果
        assert isinstance(standardized_df, pl.DataFrame)
        assert len(standardized_df) == len(raw_df)
        
        # 验证元数据字段
        expected_metadata_fields = ["source_file", "data_source", "layer_type", "created_at", "updated_at"]
        for field in expected_metadata_fields:
            assert field in standardized_df.columns, f"缺少元数据字段: {field}"
        
        # 验证元数据值
        assert standardized_df["data_source"][0] == "csv"
        assert standardized_df["layer_type"][0] == "csv"
        assert standardized_df["source_file"][0] == test_file.name


class TestCsvLayerExtractorExtractionModes:
    """CsvLayerExtractor提取模式测试"""
    
    def setup_method(self):
        """测试初始化"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.csv_data_dir = self.temp_dir / "csv_data"
        self.csv_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试配置
        self.test_config = {
            "name": "extraction_test",
            "data_source": "csv",
            "target_table": "csv_fut_main_contract_kline_15min",
            "data_dir": str(self.csv_data_dir),
            "enable_status_tracking": False
        }
        
        # 创建测试数据
        self.create_sample_csv_data()
    
    def teardown_method(self):
        """清理资源"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def create_sample_csv_data(self):
        """创建样本CSV数据"""
        test_file = self.csv_data_dir / "sample_futures.csv"
        content = """timestamp,contract_code,open_price,high_price,low_price,close_price,volume,amount
2023-01-01 09:00:00,RB2305,3800.0,3810.0,3795.0,3805.0,1000,3805000.0
2023-01-01 09:15:00,RB2305,3805.0,3815.0,3800.0,3812.0,1200,4574400.0
2023-01-01 09:30:00,RB2305,3812.0,3820.0,3808.0,3818.0,1100,4199800.0"""
        test_file.write_text(content, encoding='utf-8')
        self.test_file = test_file
    
    def test_validate_mode_extraction(self):
        """测试验证模式提取"""
        extractor = CsvLayerExtractor(self.test_config)
        
        # 验证模式不应该返回数据
        result_df = extractor.extract(mode='validate')
        
        assert isinstance(result_df, pl.DataFrame)
        assert len(result_df) == 0, "验证模式应该返回空DataFrame"
        
        # 验证统计信息更新
        stats = extractor.get_status()
        assert stats["stats"]["total_extractions"] == 1
    
    def test_full_mode_extraction(self):
        """测试全量模式提取"""
        extractor = CsvLayerExtractor(self.test_config)
        
        # 全量提取
        result_df = extractor.extract(mode='full')
        
        assert isinstance(result_df, pl.DataFrame)
        assert len(result_df) == 3, "应该提取3条记录"
        
        # 验证数据内容
        assert "timestamp" in result_df.columns
        assert "contract_code" in result_df.columns
        assert "data_source" in result_df.columns
        assert "layer_type" in result_df.columns
        
        # 验证元数据
        assert all(result_df["data_source"] == "csv")
        assert all(result_df["layer_type"] == "csv")
    
    def test_auto_mode_extraction(self):
        """测试自动模式提取"""
        extractor = CsvLayerExtractor(self.test_config)
        
        # 自动模式提取
        result_df = extractor.extract(mode='auto')
        
        assert isinstance(result_df, pl.DataFrame)
        assert len(result_df) == 3, "自动模式应该提取所有数据"
        
        # 验证统计信息
        stats = extractor.get_status()
        assert stats["stats"]["successful_extractions"] == 1
        assert stats["stats"]["total_records"] == 3
    
    def test_invalid_mode_handling(self):
        """测试无效模式处理"""
        extractor = CsvLayerExtractor(self.test_config)
        
        # 测试无效提取模式
        with pytest.raises(ExtractorException):
            extractor.extract(mode='invalid_mode')


class TestCsvLayerExtractorErrorHandling:
    """CsvLayerExtractor错误处理测试"""
    
    def setup_method(self):
        """测试初始化"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.csv_data_dir = self.temp_dir / "csv_data"
        self.csv_data_dir.mkdir(parents=True, exist_ok=True)
    
    def teardown_method(self):
        """清理资源"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_nonexistent_data_directory(self):
        """测试不存在的数据目录处理"""
        config = {
            "name": "error_test",
            "data_source": "csv",
            "target_table": "test_table",
            "data_dir": "/nonexistent/directory"
        }
        
        # 应该抛出DataSourceError
        with pytest.raises(DataSourceError):
            CsvLayerExtractor(config)
    
    def test_empty_data_directory(self):
        """测试空数据目录处理"""
        config = {
            "name": "empty_test",
            "data_source": "csv",
            "target_table": "test_table",
            "data_dir": str(self.csv_data_dir),
            "enable_status_tracking": False
        }
        
        extractor = CsvLayerExtractor(config)
        
        # 空目录应该返回空结果
        result_df = extractor.extract(mode='auto')
        assert isinstance(result_df, pl.DataFrame)
        assert len(result_df) == 0
    
    def test_invalid_csv_file_handling(self):
        """测试无效CSV文件处理"""
        # 创建无效CSV文件
        invalid_csv = self.csv_data_dir / "invalid.csv"
        invalid_csv.write_text("invalid,csv,content\nwith,malformed,data\nextra", encoding='utf-8')
        
        config = {
            "name": "invalid_test",
            "data_source": "csv", 
            "target_table": "test_table",
            "data_dir": str(self.csv_data_dir),
            "enable_status_tracking": False
        }
        
        extractor = CsvLayerExtractor(config)
        
        # 应该能够处理错误并继续
        try:
            result_df = extractor.extract(mode='auto')
            # 如果没有抛出异常，检查结果
            assert isinstance(result_df, pl.DataFrame)
        except Exception as e:
            # 如果抛出异常，确认是合理的异常类型
            assert isinstance(e, (ExtractorException, DataSourceError, DataValidationError))


class TestCsvLayerExtractorStatusAndReporting:
    """CsvLayerExtractor状态和报告测试"""
    
    def setup_method(self):
        """测试初始化"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.csv_data_dir = self.temp_dir / "csv_data"
        self.csv_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试数据
        test_file = self.csv_data_dir / "status_test.csv"
        content = "timestamp,contract_code,open_price,close_price,volume\n2023-01-01 09:00:00,RB2305,3800.0,3805.0,1000"
        test_file.write_text(content, encoding='utf-8')
        
        self.test_config = {
            "name": "status_test",
            "data_source": "csv",
            "target_table": "test_table",
            "data_dir": str(self.csv_data_dir),
            "enable_status_tracking": False
        }
    
    def teardown_method(self):
        """清理资源"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_extractor_status_reporting(self):
        """测试提取器状态报告"""
        extractor = CsvLayerExtractor(self.test_config)
        
        # 获取初始状态
        initial_status = extractor.get_status()
        
        # 验证状态结构
        required_fields = ["name", "data_source", "target_table", "enabled", "stats"]
        for field in required_fields:
            assert field in initial_status, f"状态应该包含{field}字段"
        
        # 验证CSV特定状态
        assert initial_status["data_dir_exists"] == True
        assert initial_status["total_csv_files"] == 1
        
        # 执行提取后再次检查状态
        extractor.extract(mode='auto')
        updated_status = extractor.get_status()
        
        assert updated_status["stats"]["total_extractions"] == 1
        assert updated_status["stats"]["successful_extractions"] == 1
    
    def test_health_check_functionality(self):
        """测试健康检查功能"""
        extractor = CsvLayerExtractor(self.test_config)
        
        # 健康的提取器应该返回True
        assert extractor.is_healthy() == True
        
        # 验证配置
        config_validation = extractor.validate_config()
        assert config_validation["valid"] == True


@pytest.mark.integration
class TestCsvLayerExtractorFullIntegration:
    """CsvLayerExtractor完整集成测试标记"""
    
    def test_complete_csv_extraction_workflow(self):
        """完整CSV提取工作流程测试"""
        temp_dir = Path(tempfile.mkdtemp())
        try:
            csv_data_dir = temp_dir / "production_csv"
            csv_data_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建真实的期货数据文件
            for i in range(2):
                csv_file = csv_data_dir / f"futures_data_{i+1}.csv"
                content = "timestamp,contract_code,open_price,high_price,low_price,close_price,volume,amount\n"
                
                # 每个文件10条记录
                for j in range(10):
                    timestamp = f"2023-01-{i+1:02d} {9+j:02d}:00:00"
                    contract = f"RB230{i+1}"
                    base_price = 3800 + i * 10 + j
                    content += f"{timestamp},{contract},{base_price},{base_price+5},{base_price-3},{base_price+2},{1000+j*10},{(base_price+2)*(1000+j*10)}\n"
                
                csv_file.write_text(content, encoding='utf-8')
            
            # 配置提取器
            config = {
                "name": "integration_test",
                "data_source": "csv",
                "target_table": "csv_fut_main_contract_kline_15min",
                "data_dir": str(csv_data_dir),
                "batch_size": 50,
                "enable_validation": True,
                "enable_status_tracking": False
            }
            
            # 执行完整工作流程
            extractor = CsvLayerExtractor(config)
            
            # 1. 验证配置
            validation = extractor.validate_config()
            assert validation["valid"] == True
            
            # 2. 健康检查
            assert extractor.is_healthy() == True
            
            # 3. 测试连接
            connection_test = extractor.test_connection()
            assert connection_test["success"] == True
            assert connection_test["csv_files_found"] == 2
            
            # 4. 执行提取
            result_df = extractor.extract(mode='full')
            
            # 验证结果
            assert isinstance(result_df, pl.DataFrame)
            assert len(result_df) == 20, "应该提取20条记录"
            
            # 验证数据完整性
            required_columns = ["timestamp", "contract_code", "open_price", "high_price", 
                              "low_price", "close_price", "volume", "amount"]
            for col in required_columns:
                assert col in result_df.columns, f"缺少列: {col}"
            
            # 验证元数据字段
            metadata_columns = ["source_file", "data_source", "layer_type", "created_at", "updated_at"]
            for col in metadata_columns:
                assert col in result_df.columns, f"缺少元数据列: {col}"
            
            # 5. 验证最终状态
            final_status = extractor.get_status()
            assert final_status["stats"]["successful_extractions"] == 1
            assert final_status["stats"]["total_records"] == 20
            
        finally:
            shutil.rmtree(temp_dir)


if __name__ == "__main__":
    # 运行集成测试
    pytest.main([__file__, "-v", "--tb=short"])