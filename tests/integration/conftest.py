"""
集成测试通用配置和fixture
"""
import pytest
import os
import tempfile
from pathlib import Path

@pytest.fixture(scope="session")
def integration_test_environment():
    """集成测试会话级环境设置"""
    # 创建测试临时目录
    temp_base = tempfile.mkdtemp(prefix="aqua_integration_")
    test_env = {
        "temp_dir": Path(temp_base),
        "original_env": dict(os.environ),
        "test_databases": {},
        "test_files": []
    }
    
    # 设置测试环境变量
    os.environ["AQUA_ENV"] = "integration_test"
    os.environ["AQUA_TEST_MODE"] = "1"
    
    yield test_env
    
    # 清理测试环境
    import shutil
    shutil.rmtree(temp_base, ignore_errors=True)
    
    # 恢复原始环境变量
    os.environ.clear()
    os.environ.update(test_env["original_env"])

@pytest.fixture
def test_data_generator():
    """测试数据生成器"""
    import pandas as pd
    import random
    
    def generate_stock_data(symbol="TEST", days=30, base_price=100):
        """生成模拟股票数据"""
        dates = pd.date_range("2024-01-01", periods=days, freq="D")
        data = []
        
        current_price = base_price
        for date in dates:
            # 模拟价格波动
            change_pct = random.uniform(-0.05, 0.05)  # ±5%
            current_price *= (1 + change_pct)
            
            # 生成OHLC数据
            high = current_price * random.uniform(1.00, 1.03)
            low = current_price * random.uniform(0.97, 1.00)
            open_price = random.uniform(low, high)
            close_price = current_price
            volume = random.randint(500000, 2000000)
            
            data.append({
                "symbol": symbol,
                "date": date.strftime("%Y-%m-%d"),
                "open": round(open_price, 2),
                "high": round(high, 2),
                "low": round(low, 2),
                "close": round(close_price, 2),
                "volume": volume
            })
        
        return pd.DataFrame(data)
    
    return generate_stock_data

# 集成测试标记
pytestmark = [
    pytest.mark.integration,
    pytest.mark.slow
]