#!/usr/bin/env python3
"""
数据中心集成测试

测试前后端API集成，验证完整的数据流
"""

import pytest
import json
from pathlib import Path
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch, MagicMock

from main import app
from src.database.duckdb_init_check import <PERSON><PERSON><PERSON>nitChe<PERSON>
from src.database.connection_manager import DuckDBConnectionManager

client = TestClient(app)


class TestDataCenterIntegration:
    """数据中心集成测试类"""
    
    def test_api_health_check(self):
        """测试API健康检查"""
        response = client.get("/api/data/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "database_status" in data
    
    def test_api_tables_endpoint(self):
        """测试获取表列表API端点"""
        with patch('src.api.routers.data_router.DuckDBConnectionManager') as mock_manager_class:
            with patch('src.api.routers.data_router.DuckDBInitChecker') as mock_checker_class:
                # 模拟数据库管理器
                mock_manager = Mock()
                mock_manager.get_all_tables.return_value = ["test_table1", "test_table2"]
                mock_manager.get_table_count.return_value = 100
                mock_manager.get_table_info.return_value = [
                    {
                        "column_name": "id",
                        "column_type": "INTEGER",
                        "null": "NO",
                        "key": "PRI",
                        "default": None
                    },
                    {
                        "column_name": "name",
                        "column_type": "VARCHAR",
                        "null": "YES",
                        "key": "",
                        "default": None
                    }
                ]
                mock_manager_class.return_value = mock_manager
                
                # 模拟检查器
                mock_checker = Mock()
                mock_checker_class.return_value = mock_checker
                
                # 发送请求
                response = client.get("/api/data/tables?environment=test")
                assert response.status_code == 200
                
                data = response.json()
                assert data["success"] is True
                assert "data" in data
                assert len(data["data"]) == 2
                
                # 验证表信息结构
                table_info = data["data"][0]
                assert "name" in table_info
                assert "record_count" in table_info
                assert "columns" in table_info
                assert table_info["name"] == "test_table1"
                assert table_info["record_count"] == 100
    
    def test_api_table_data_endpoint(self):
        """测试获取表数据API端点"""
        with patch('src.api.routers.data_router.DuckDBConnectionManager') as mock_manager_class:
            mock_manager = Mock()
            mock_manager.table_exists.return_value = True
            mock_manager.get_table_info.return_value = [
                {"column_name": "id", "column_type": "INTEGER"},
                {"column_name": "name", "column_type": "VARCHAR"}
            ]
            mock_manager.execute_query.return_value = [
                (1, "test_name1"),
                (2, "test_name2")
            ]
            mock_manager.get_table_count.return_value = 2
            mock_manager_class.return_value = mock_manager
            
            response = client.get("/api/data/tables/test_table?environment=test&limit=10&offset=0")
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert "data" in data
            assert len(data["data"]) == 2
            
            # 验证分页信息
            assert "pagination" in data
            pagination = data["pagination"]
            assert pagination["limit"] == 10
            assert pagination["offset"] == 0
            assert pagination["total"] == 2
            assert pagination["has_next"] is False
    
    def test_api_table_meta_endpoint(self):
        """测试获取表元数据API端点"""
        with patch('src.api.routers.data_router.DuckDBConnectionManager') as mock_manager_class:
            mock_manager = Mock()
            mock_manager.table_exists.return_value = True
            mock_manager.get_table_info.return_value = [
                {
                    "column_name": "id",
                    "column_type": "INTEGER",
                    "null": "NO",
                    "key": "PRI",
                    "default": None
                },
                {
                    "column_name": "name",
                    "column_type": "VARCHAR",
                    "null": "YES",
                    "key": "",
                    "default": None
                }
            ]
            mock_manager.get_table_count.return_value = 50
            mock_manager_class.return_value = mock_manager
            
            response = client.get("/api/data/tables/test_table/meta?environment=test")
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert "data" in data
            
            meta_data = data["data"]
            assert meta_data["table_name"] == "test_table"
            assert meta_data["record_count"] == 50
            assert meta_data["column_count"] == 2
            assert "columns" in meta_data
    
    def test_api_csv_import_endpoint(self):
        """测试CSV导入API端点"""
        with patch('src.api.routers.data_router.CSVImporter') as mock_importer_class:
            mock_importer = Mock()
            mock_importer.import_single_file.return_value = {
                "success": True,
                "message": "导入成功",
                "table_name": "test_table",
                "record_count": 100
            }
            mock_importer_class.return_value = mock_importer
            
            # 创建测试文件
            test_file = ("test.csv", "id,name\n1,test1\n2,test2", "text/csv")
            
            response = client.post(
                "/api/data/import/csv?environment=test",
                files={"file": test_file}
            )
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert "data" in data
            assert data["data"]["table_name"] == "test_table"
            assert data["data"]["record_count"] == 100
    
    def test_api_error_handling(self):
        """测试API错误处理"""
        # 测试表不存在的情况
        with patch('src.api.routers.data_router.DuckDBConnectionManager') as mock_manager_class:
            mock_manager = Mock()
            mock_manager.table_exists.return_value = False
            mock_manager_class.return_value = mock_manager
            
            response = client.get("/api/data/tables/non_existent_table")
            assert response.status_code == 404
            
            data = response.json()
            assert "detail" in data
            assert "不存在" in data["detail"]
    
    def test_api_parameter_validation(self):
        """测试API参数验证"""
        # 测试无效的limit参数
        response = client.get("/api/data/tables/test_table?limit=0")
        assert response.status_code == 422
        
        # 测试无效的offset参数
        response = client.get("/api/data/tables/test_table?offset=-1")
        assert response.status_code == 422
        
        # 测试超过最大limit
        response = client.get("/api/data/tables/test_table?limit=2000")
        assert response.status_code == 422
    
    def test_api_response_format(self):
        """测试API响应格式一致性"""
        with patch('src.api.routers.data_router.DuckDBConnectionManager') as mock_manager_class:
            with patch('src.api.routers.data_router.DuckDBInitChecker') as mock_checker_class:
                mock_manager = Mock()
                mock_manager.get_all_tables.return_value = ["test_table"]
                mock_manager.get_table_count.return_value = 10
                mock_manager.get_table_info.return_value = [
                    {"column_name": "id", "column_type": "INTEGER", "null": "NO", "key": "PRI", "default": None}
                ]
                mock_manager_class.return_value = mock_manager
                
                mock_checker = Mock()
                mock_checker_class.return_value = mock_checker
                
                response = client.get("/api/data/tables")
                assert response.status_code == 200
                
                data = response.json()
                
                # 验证响应格式
                required_fields = ["success", "data", "message", "timestamp", "total_count"]
                for field in required_fields:
                    assert field in data
                
                assert isinstance(data["success"], bool)
                assert isinstance(data["data"], list)
                assert isinstance(data["message"], str)
                assert isinstance(data["timestamp"], str)
                assert isinstance(data["total_count"], int)
    
    def test_cors_and_headers(self):
        """测试CORS和HTTP头"""
        response = client.get("/api/data/health")
        assert response.status_code == 200
        
        # 验证响应头
        headers = response.headers
        assert "content-type" in headers
        assert "application/json" in headers["content-type"]
    
    def test_api_performance(self):
        """测试API性能"""
        with patch('src.api.routers.data_router.DuckDBConnectionManager') as mock_manager_class:
            with patch('src.api.routers.data_router.DuckDBInitChecker') as mock_checker_class:
                mock_manager = Mock()
                mock_manager.get_all_tables.return_value = ["test_table"]
                mock_manager.get_table_count.return_value = 10
                mock_manager.get_table_info.return_value = [
                    {"column_name": "id", "column_type": "INTEGER", "null": "NO", "key": "PRI", "default": None}
                ]
                mock_manager_class.return_value = mock_manager
                
                mock_checker = Mock()
                mock_checker_class.return_value = mock_checker
                
                import time
                start_time = time.time()
                
                response = client.get("/api/data/tables")
                
                end_time = time.time()
                response_time = end_time - start_time
                
                assert response.status_code == 200
                assert response_time < 2.0  # 响应时间应该小于2秒


if __name__ == "__main__":
    pytest.main([__file__, "-v"])