import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import patch, ANY

# 确保 src 在 sys.path 中
@pytest.fixture(autouse=True)
def ensure_src_in_path(monkeypatch):
    import sys
    from pathlib import Path
    project_root = Path(__file__).resolve().parent.parent.parent
    src_path = project_root / "src"
    monkeypatch.syspath_prepend(str(src_path))

@pytest.fixture
def cli_app():
    from aqua.main import app
    return app

@pytest.fixture
def runner():
    return CliRunner()

def test_cli_help_output(cli_app, runner):
    """测试 'aqua --help' 是否能显示预期的命令"""
    result = runner.invoke(cli_app, ["--help"])
    assert result.exit_code == 0
    assert "Usage: aqua [OPTIONS] COMMAND [ARGS]..." in result.stdout
    assert "init" in result.stdout
    assert "start" in result.stdout
    assert "stop" in result.stdout
    assert "status" in result.stdout

@patch('aqua.main.init_helpers')
@patch('aqua.main.ConfigLoader')
def test_init_command_calls_helpers(mock_config_loader, mock_init_helpers, cli_app, runner):
    """测试 'init' 命令是否会调用 init_helpers 中的函数并传递正确的路径"""
    # 模拟 ConfigLoader
    mock_config_instance = mock_config_loader.return_value
    mock_config_instance.env = 'dev'
    mock_db_path = Path("/tmp/fake/aqua_dev.duckdb")
    mock_config_instance.get_path.return_value = mock_db_path

    # 模拟 init_helpers
    mock_init_helpers.install_uv.return_value = True
    mock_init_helpers.create_project_venv.return_value = True
    mock_init_helpers.sync_dependencies.return_value = True
    mock_init_helpers.init_database.return_value = True
    
    result = runner.invoke(cli_app, ["--env", "dev", "init"])
    
    assert result.exit_code == 0, f"CLI command failed with output: {result.stdout}"
    mock_init_helpers.install_uv.assert_called_once()
    mock_init_helpers.create_project_venv.assert_called_once()
    mock_init_helpers.sync_dependencies.assert_called_once()
    # 验证 init_database 被调用，并且接收到了正确的 db_path
    mock_init_helpers.init_database.assert_called_with("dev", db_path=mock_db_path)

@patch('aqua.main.init_helpers')
def test_init_command_fails_on_sync_error(mock_init_helpers, cli_app, runner):
    """测试当依赖同步失败时，init命令是否会以失败码退出"""
    mock_init_helpers.install_uv.return_value = True
    mock_init_helpers.create_project_venv.return_value = True
    mock_init_helpers.sync_dependencies.return_value = False # 模拟失败
    
    result = runner.invoke(cli_app, ["init"])
    assert result.exit_code == 1
    assert "依赖同步失败" in result.stdout

@patch('aqua.main.ServiceManager')
def test_start_command_calls_service_manager(mock_service_manager_class, cli_app, runner):
    """测试 'start' 命令是否会调用 ServiceManager.start"""
    mock_instance = mock_service_manager_class.return_value
    mock_instance.is_running.return_value = False

    result = runner.invoke(cli_app, ["start"])
    
    assert result.exit_code == 0
    # 验证 ServiceManager 使用了正确的 Procfile 路径被实例化
    mock_service_manager_class.assert_called_with(procfile_path=ANY)
    mock_instance.start.assert_called_once()

@patch('aqua.main.ServiceManager')
def test_stop_command_calls_service_manager(mock_service_manager_class, cli_app, runner):
    """测试 'stop' 命令是否会调用 ServiceManager.stop"""
    mock_instance = mock_service_manager_class.return_value
    mock_instance.is_running.return_value = True

    result = runner.invoke(cli_app, ["stop"])
    
    assert result.exit_code == 0
    mock_service_manager_class.assert_called_with(procfile_path=ANY)
    mock_instance.stop.assert_called_once()

@patch('aqua.main.ServiceManager')
def test_status_command_calls_service_manager(mock_service_manager_class, cli_app, runner):
    """测试 'status' 命令是否会调用 ServiceManager.status 并打印输出"""
    mock_instance = mock_service_manager_class.return_value
    mock_instance.status.return_value = "Super-Duper-Running"
    
    result = runner.invoke(cli_app, ["status"])
    
    assert result.exit_code == 0
    mock_service_manager_class.assert_called_with(procfile_path=ANY)
    mock_instance.status.assert_called_once()
    assert "Super-Duper-Running" in result.stdout

# 新增的集成测试用例，用于实际启动和停止服务
@patch('aqua.main.ServiceManager')
def test_start_stop_integration(mock_service_manager_class, cli_app, runner):
    """测试 'start' 和 'stop' 命令的集成，验证服务能够正确启动和停止"""
    mock_instance = mock_service_manager_class.return_value
    
    # 模拟服务未运行，然后启动
    mock_instance.is_running.side_effect = [False, True] # 第一次调用返回False，第二次返回True
    mock_instance.start.return_value = None # 模拟启动成功
    result_start = runner.invoke(cli_app, ["start"])
    assert result_start.exit_code == 0
    mock_instance.start.assert_called_once()
    assert "服务已启动" in result_start.stdout or "Service started" in result_start.stdout

    # 模拟服务正在运行，然后停止
    mock_instance.is_running.side_effect = [True, False] # 第一次调用返回True，第二次返回False
    mock_instance.stop.return_value = None # 模拟停止成功
    result_stop = runner.invoke(cli_app, ["stop"])
    assert result_stop.exit_code == 0
    mock_instance.stop.assert_called_once()
    assert "服务已停止" in result_stop.stdout or "Service stopped" in result_stop.stdout

    # 验证在服务已运行时尝试启动
    mock_instance.is_running.return_value = True
    mock_instance.start.reset_mock() # 重置start的mock，确保只计算本次调用
    result_start_again = runner.invoke(cli_app, ["start"])
    assert result_start_again.exit_code == 0
    mock_instance.start.assert_not_called() # 此时不应再次调用start
    assert "服务已在运行" in result_start_again.stdout or "Service is already running" in result_start_again.stdout

    # 验证在服务未运行时尝试停止
    mock_instance.is_running.return_value = False
    mock_instance.stop.reset_mock() # 重置stop的mock
    result_stop_again = runner.invoke(cli_app, ["stop"])
    assert result_stop_again.exit_code == 0
    mock_instance.stop.assert_not_called() # 此时不应再次调用stop
    assert "服务未运行" in result_stop_again.stdout or "Service is not running" in result_stop_again.stdout