"""
AQUA CLI 完整集成测试套件
在macOS环境下验证所有新增功能的端到端集成
Windows环境测试计划预留
"""
import pytest
import os
import sys
import tempfile
import shutil
import json
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typer.testing import Cli<PERSON>unner
import subprocess

# 添加src路径到sys.path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

from aqua.main import app

runner = CliRunner()

class TestMacOSIntegration:
    """macOS环境下的完整集成测试"""
    
    @classmethod
    def setup_class(cls):
        """测试类初始化"""
        cls.temp_dir = Path(tempfile.mkdtemp(prefix="aqua_test_"))
        cls.original_cwd = Path.cwd()
        os.chdir(cls.temp_dir)
        
        # 创建基本的项目结构
        cls._create_test_project_structure()
    
    @classmethod
    def teardown_class(cls):
        """测试类清理"""
        os.chdir(cls.original_cwd)
        if cls.temp_dir.exists():
            shutil.rmtree(cls.temp_dir)
    
    @classmethod
    def _create_test_project_structure(cls):
        """创建测试项目结构"""
        # 创建基本目录
        directories = [
            "src/aqua/cli",
            "config", 
            "tests/unit",
            "tests/integration",
            "scripts",
            "logs",
            "temp",
            "reports"
        ]
        
        for dir_path in directories:
            (cls.temp_dir / dir_path).mkdir(parents=True, exist_ok=True)
        
        # 创建基本配置文件
        config_content = """
[app]
name = "AQUA"
version = "2.0.0"
default_environment = "dev"
environments = ["dev", "test", "prod"]

[platform.unix.paths]
datacenter_dir = "~/Documents/Data/duckdb/AQUA/DataCenter"

[dev.database]
path = "{datacenter_dir}/aqua_dev.duckdb"
        """.strip()
        
        (cls.temp_dir / "config" / "settings.toml").write_text(config_content)
        
        # 创建requirements.txt
        requirements = """
typer>=0.9.0
rich>=13.0.0
toml>=0.10.2
        """.strip()
        
        (cls.temp_dir / "requirements.txt").write_text(requirements)
        
        # 创建Procfile.dev
        procfile_content = """
web: echo "AQUA web service"
worker: echo "AQUA background worker"
        """.strip()
        
        (cls.temp_dir / "Procfile.dev").write_text(procfile_content)

class TestCLICommandsIntegration(TestMacOSIntegration):
    """CLI命令集成测试"""
    
    def test_main_app_startup(self):
        """测试主应用启动"""
        result = runner.invoke(app, ["--help"])
        assert result.exit_code == 0
        assert "AQUA 项目统一管理工具" in result.stdout
        assert "setup" in result.stdout
        assert "doctor" in result.stdout
        assert "dev" in result.stdout
    
    def test_enhanced_welcome_banner(self):
        """测试增强欢迎横幅"""
        # 模拟非测试环境
        with patch('sys.modules', {}):
            result = runner.invoke(app, ["--help"])
            assert result.exit_code == 0
            # 应该包含增强的欢迎信息
    
    def test_environment_variable_handling(self):
        """测试环境变量处理"""
        # 测试AQUA_ENV环境变量
        with patch.dict(os.environ, {'AQUA_ENV': 'test'}):
            result = runner.invoke(app, ["--help"])
            assert result.exit_code == 0
    
    def test_all_commands_help_accessibility(self):
        """测试所有命令的帮助信息可访问性"""
        commands = ["init", "start", "stop", "status", "setup", "doctor", "windows", "dev", "stats"]
        
        for command in commands:
            result = runner.invoke(app, [command, "--help"])
            assert result.exit_code == 0, f"Command {command} help failed"
            assert len(result.stdout) > 50, f"Command {command} help too short"

class TestSetupWizardIntegration(TestMacOSIntegration):
    """智能配置向导集成测试"""
    
    @patch('aqua.cli.setup_wizard.SetupWizard.run')
    def test_setup_command_execution(self, mock_run):
        """测试setup命令执行"""
        from aqua.cli.setup_wizard import SetupConfig
        
        # 模拟配置向导返回
        mock_config = SetupConfig(
            environment="dev",
            platform="unix",
            data_source_types=["csv"],
            memory_limit=1536
        )
        mock_run.return_value = mock_config
        
        result = runner.invoke(app, ["setup"])
        assert result.exit_code == 0
        mock_run.assert_called_once()
    
    @patch('aqua.cli.setup_wizard.EnvironmentDetector.detect_platform')
    def test_macos_platform_detection(self, mock_detect):
        """测试macOS平台检测"""
        mock_detect.return_value = "unix"
        
        from aqua.cli.setup_wizard import EnvironmentDetector
        detector = EnvironmentDetector()
        
        platform_type = detector.detect_platform()
        assert platform_type == "unix"
    
    @patch('aqua.cli.setup_wizard.EnvironmentDetector.detect_memory')
    def test_memory_detection_macos(self, mock_detect):
        """测试macOS内存检测"""
        mock_detect.return_value = 8192  # 8GB
        
        from aqua.cli.setup_wizard import EnvironmentDetector
        detector = EnvironmentDetector()
        
        memory = detector.detect_memory()
        assert memory >= 1024  # 至少1GB
    
    def test_data_source_detection_integration(self):
        """测试数据源检测集成"""
        from aqua.cli.setup_wizard import EnvironmentDetector
        
        detector = EnvironmentDetector()
        sources = detector.detect_data_sources()
        
        assert isinstance(sources, list)
        # 在测试环境中，可能没有配置数据源
        assert len(sources) >= 0

class TestHealthCheckerIntegration(TestMacOSIntegration):
    """健康检查系统集成测试"""
    
    def test_doctor_command_basic_execution(self):
        """测试doctor命令基本执行"""
        with patch('aqua.cli.health_checker.HealthChecker.run_full_check') as mock_check:
            from aqua.cli.health_checker import SystemReport, HealthStatus
            
            mock_report = SystemReport(overall_status=HealthStatus.HEALTHY)
            mock_check.return_value = mock_report
            
            with patch('aqua.cli.health_checker.HealthChecker.display_report'):
                result = runner.invoke(app, ["doctor", "--no-auto-fix"])
                assert result.exit_code == 0
    
    def test_health_checker_python_environment(self):
        """测试Python环境健康检查"""
        from aqua.cli.health_checker import HealthChecker
        
        checker = HealthChecker()
        result = checker._check_python_environment()
        
        assert result.name == "Python版本"
        # 在macOS测试环境中，Python版本应该是健康的
        from aqua.cli.health_checker import HealthStatus
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING]
    
    def test_health_checker_platform_compatibility(self):
        """测试平台兼容性检查"""
        from aqua.cli.health_checker import HealthChecker
        
        checker = HealthChecker()
        result = checker._check_platform_compatibility()
        
        assert result.name == "平台兼容性"
        # macOS应该有良好的兼容性
        assert "Darwin" in result.message or "macOS" in result.message
    
    def test_disk_space_check_macos(self):
        """测试macOS磁盘空间检查"""
        from aqua.cli.health_checker import HealthChecker
        
        checker = HealthChecker()
        result = checker._check_disk_space()
        
        assert result.name == "磁盘空间"
        from aqua.cli.health_checker import HealthStatus
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
    
    def test_permissions_check_macos(self):
        """测试macOS权限检查"""
        from aqua.cli.health_checker import HealthChecker
        
        checker = HealthChecker()
        result = checker._check_permissions()
        
        assert result.name == "文件权限"
        # 在测试环境中应有写权限
        from aqua.cli.health_checker import HealthStatus
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING]

class TestEnhancedUIIntegration(TestMacOSIntegration):
    """增强UI集成测试"""
    
    def test_enhanced_ui_initialization(self):
        """测试增强UI初始化"""
        from aqua.cli.enhanced_ui import enhanced_ui
        
        assert enhanced_ui.console is not None
        assert enhanced_ui.history is not None
        assert enhanced_ui.prompt is not None
    
    def test_command_history_persistence(self):
        """测试命令历史持久化"""
        from aqua.cli.enhanced_ui import CommandHistory, UserAction
        from datetime import datetime
        
        # 创建临时历史文件路径
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch('pathlib.Path.home') as mock_home:
                mock_home.return_value = Path(temp_dir)
                
                history = CommandHistory()
                
                # 添加测试操作
                action = UserAction(
                    timestamp=datetime.now(),
                    command="test_command",
                    parameters={"env": "dev"},
                    result="success",
                    duration=1.5
                )
                
                history.add_action(action)
                
                # 验证持久化
                assert len(history.actions) >= 1
                assert history.actions[-1].command == "test_command"
    
    def test_smart_prompt_integration(self):
        """测试智能提示集成"""
        from aqua.cli.enhanced_ui import SmartPrompt
        
        prompt = SmartPrompt()
        
        # 验证建议数据结构
        assert 'environment' in prompt.suggestions
        assert 'dev' in prompt.suggestions['environment']
        assert 'test' in prompt.suggestions['environment']
        assert 'prod' in prompt.suggestions['environment']
    
    def test_progress_tracker_functionality(self):
        """测试进度跟踪功能"""
        from aqua.cli.enhanced_ui import ProgressTracker
        
        tracker = ProgressTracker()
        
        # 测试创建进度条
        progress = tracker.create_enhanced_progress("Integration Test")
        assert progress is not None
    
    def test_error_handler_integration(self):
        """测试错误处理集成"""
        from aqua.cli.enhanced_ui import ErrorHandler
        
        handler = ErrorHandler()
        
        # 测试已知错误类型
        error_types = ['FileNotFoundError', 'PermissionError', 'ImportError']
        for error_type in error_types:
            assert error_type in handler.error_solutions
            assert 'description' in handler.error_solutions[error_type]
            assert 'solutions' in handler.error_solutions[error_type]

class TestWindowsCompatIntegration(TestMacOSIntegration):
    """Windows兼容性集成测试（macOS环境下模拟）"""
    
    def test_windows_compat_manager_non_windows(self):
        """测试非Windows环境下的兼容性管理器"""
        from aqua.cli.windows_compat import WindowsCompatibilityManager
        
        manager = WindowsCompatibilityManager()
        
        # 在macOS环境下应该识别为非Windows
        assert not manager.is_windows
        assert not manager.is_admin  # 非Windows环境下为False
    
    def test_windows_command_help_accessibility(self):
        """测试Windows命令帮助可访问性"""
        result = runner.invoke(app, ["windows", "--help"])
        assert result.exit_code == 0
        assert "Windows深度兼容性管理" in result.stdout
    
    @patch('platform.system')
    def test_windows_compat_mock_behavior(self, mock_system):
        """测试Windows兼容性模拟行为"""
        mock_system.return_value = "Windows"
        
        from aqua.cli.windows_compat import WindowsCompatibilityManager
        manager = WindowsCompatibilityManager()
        
        # 模拟Windows环境
        assert manager.is_windows
        
        # 测试兼容性状态检查
        status = manager.check_compatibility_status()
        assert status['platform'] == 'windows'
    
    def test_windows_compatibility_report_generation(self):
        """测试Windows兼容性报告生成"""
        from aqua.cli.windows_compat import WindowsCompatibilityManager
        
        manager = WindowsCompatibilityManager()
        report = manager.generate_compatibility_report()
        
        assert isinstance(report, str)
        assert len(report) > 0
        # 在非Windows环境下应该有相应说明
        assert "Windows" in report

class TestDevToolsIntegration(TestMacOSIntegration):
    """开发工具链集成测试"""
    
    def test_dev_command_help_functionality(self):
        """测试dev命令帮助功能"""
        result = runner.invoke(app, ["dev", "--help"])
        assert result.exit_code == 0
        assert "开发工具链集成" in result.stdout
        assert "--setup" in result.stdout
        assert "--check" in result.stdout
    
    def test_quality_analyzer_initialization(self):
        """测试质量分析器初始化"""
        from aqua.cli.dev_tools import QualityAnalyzer
        
        analyzer = QualityAnalyzer(self.temp_dir)
        
        assert analyzer.project_root == self.temp_dir
        assert analyzer.src_path == self.temp_dir / "src"
        assert analyzer.reports_dir.exists()
    
    def test_pre_commit_manager_config_generation(self):
        """测试Pre-commit管理器配置生成"""
        from aqua.cli.dev_tools import PreCommitManager
        
        manager = PreCommitManager(self.temp_dir)
        
        # 测试配置文件生成
        success = manager._create_pre_commit_config()
        assert success
        assert manager.pre_commit_config.exists()
        
        # 验证配置内容
        config_content = manager.pre_commit_config.read_text()
        assert "black" in config_content
        assert "ruff" in config_content
        assert "mypy" in config_content
    
    @patch('subprocess.run')
    def test_quality_analyzer_test_coverage(self, mock_run):
        """测试质量分析器测试覆盖率分析"""
        from aqua.cli.dev_tools import QualityAnalyzer
        
        # 模拟pytest成功执行
        mock_run.return_value.returncode = 0
        
        analyzer = QualityAnalyzer(self.temp_dir)
        
        # 创建模拟的覆盖率报告
        coverage_data = {
            "totals": {
                "percent_covered": 92.5
            }
        }
        
        coverage_file = analyzer.reports_dir / "coverage.json"
        with open(coverage_file, 'w') as f:
            json.dump(coverage_data, f)
        
        coverage = analyzer._analyze_test_coverage()
        assert coverage == 92.5
    
    def test_dev_tools_config_creation(self):
        """测试开发工具配置创建"""
        from aqua.cli.dev_tools import DevToolsConfig
        
        config = DevToolsConfig()
        
        assert config.pre_commit_enabled is True
        assert config.auto_format is True
        assert config.auto_lint is True
        assert config.type_checking is True

class TestServiceIntegration(TestMacOSIntegration):
    """服务管理集成测试"""
    
    @patch('aqua.cli.service_manager.ServiceManager.is_running')
    def test_status_command_integration(self, mock_is_running):
        """测试status命令集成"""
        mock_is_running.return_value = False
        
        result = runner.invoke(app, ["status"])
        assert result.exit_code == 0
        assert "服务状态" in result.stdout
    
    @patch('aqua.main.State')
    def test_start_command_integration(self, mock_state_class):
        """测试start命令集成"""
        # 创建mock状态和服务管理器
        mock_state = Mock()
        mock_service_manager = Mock()
        mock_service_manager.is_running.return_value = False
        mock_state.service_manager = mock_service_manager
        mock_state_class.return_value = mock_state
        
        with patch('aqua.main.State', return_value=mock_state):
            result = runner.invoke(app, ["start"])
            assert result.exit_code == 0
            mock_service_manager.start.assert_called_once()
    
    @patch('aqua.cli.service_manager.ServiceManager.is_running')
    @patch('aqua.cli.service_manager.ServiceManager.stop')
    def test_stop_command_integration(self, mock_stop, mock_is_running):
        """测试stop命令集成"""
        mock_is_running.return_value = True
        mock_stop.return_value = None
        
        result = runner.invoke(app, ["stop"])
        assert result.exit_code == 0
        mock_stop.assert_called_once()

class TestConfigurationIntegration(TestMacOSIntegration):
    """配置系统集成测试"""
    
    def test_config_loader_integration(self):
        """测试配置加载器集成"""
        with patch('aqua.config.ConfigLoader') as mock_loader_class:
            mock_loader = Mock()
            mock_loader.env = "dev"
            mock_loader_class.return_value = mock_loader
            
            result = runner.invoke(app, ["--help"])
            assert result.exit_code == 0
    
    def test_environment_selection_integration(self):
        """测试环境选择集成"""
        # 测试默认环境
        result = runner.invoke(app, ["--help"])
        assert result.exit_code == 0
        
        # 测试指定环境
        result = runner.invoke(app, ["--env", "test", "--help"])
        assert result.exit_code == 0

class TestEndToEndWorkflows(TestMacOSIntegration):
    """端到端工作流测试"""
    
    @patch('aqua.cli.setup_wizard.SetupWizard.run')
    @patch('aqua.cli.health_checker.HealthChecker.run_full_check')
    def test_new_user_workflow(self, mock_health_check, mock_setup):
        """测试新用户完整工作流"""
        from aqua.cli.setup_wizard import SetupConfig
        from aqua.cli.health_checker import SystemReport, HealthStatus
        
        # 模拟配置向导
        mock_config = SetupConfig(
            environment="dev",
            platform="unix",
            data_source_types=["csv"]
        )
        mock_setup.return_value = mock_config
        
        # 模拟健康检查
        mock_report = SystemReport(overall_status=HealthStatus.HEALTHY)
        mock_health_check.return_value = mock_report
        
        # 执行新用户工作流
        # 1. 配置向导
        result1 = runner.invoke(app, ["setup"])
        assert result1.exit_code == 0
        
        # 2. 健康检查
        with patch('aqua.cli.health_checker.HealthChecker.display_report'):
            result2 = runner.invoke(app, ["doctor"])
            assert result2.exit_code == 0
    
    def test_developer_daily_workflow(self):
        """测试开发者日常工作流"""
        # 1. 查看统计
        result1 = runner.invoke(app, ["stats"])
        assert result1.exit_code == 0
        
        # 2. 查看帮助
        result2 = runner.invoke(app, ["dev", "--help"])
        assert result2.exit_code == 0
        
        # 3. 检查状态
        with patch('aqua.cli.service_manager.ServiceManager.is_running', return_value=False):
            result3 = runner.invoke(app, ["status"])
            assert result3.exit_code == 0
    
    def test_troubleshooting_workflow(self):
        """测试故障排除工作流"""
        # 1. 健康检查
        with patch('aqua.cli.health_checker.HealthChecker.run_full_check') as mock_check:
            from aqua.cli.health_checker import SystemReport, HealthStatus
            
            mock_report = SystemReport(overall_status=HealthStatus.WARNING)
            mock_check.return_value = mock_report
            
            with patch('aqua.cli.health_checker.HealthChecker.display_report'):
                result = runner.invoke(app, ["doctor", "--auto-fix"])
                assert result.exit_code == 0

class TestPerformanceIntegration(TestMacOSIntegration):
    """性能集成测试"""
    
    def test_cli_startup_performance(self):
        """测试CLI启动性能"""
        start_time = time.time()
        
        result = runner.invoke(app, ["--help"])
        
        elapsed_time = time.time() - start_time
        
        assert result.exit_code == 0
        assert elapsed_time < 5.0  # 启动时间应小于5秒
    
    def test_command_response_time(self):
        """测试命令响应时间"""
        commands_to_test = [
            ["--help"],
            ["status", "--help"],
            ["setup", "--help"],
            ["doctor", "--help"]
        ]
        
        for cmd in commands_to_test:
            start_time = time.time()
            result = runner.invoke(app, cmd)
            elapsed_time = time.time() - start_time
            
            assert result.exit_code == 0
            assert elapsed_time < 3.0  # 每个命令响应时间应小于3秒
    
    def test_memory_usage_stability(self):
        """测试内存使用稳定性"""
        # 连续执行多个命令，检查内存是否稳定
        for _ in range(10):
            result = runner.invoke(app, ["--help"])
            assert result.exit_code == 0
            
            # 简单的内存检查（模拟）
            # 在实际环境中可以使用psutil检查内存使用

class TestErrorRecoveryIntegration(TestMacOSIntegration):
    """错误恢复集成测试"""
    
    def test_configuration_error_recovery(self):
        """测试配置错误恢复"""
        # 创建损坏的配置文件
        (self.temp_dir / "config" / "settings.toml").write_text("invalid toml content [[[")
        
        # CLI应该能够处理配置错误
        result = runner.invoke(app, ["--help"])
        # 根据错误处理策略，可能返回错误码或使用默认配置
        assert result.exit_code in [0, 1]
    
    def test_missing_dependency_handling(self):
        """测试缺失依赖处理"""
        with patch('importlib.import_module') as mock_import:
            mock_import.side_effect = ImportError("No module named 'test_module'")
            
            # 健康检查应该能够检测到缺失的依赖
            from aqua.cli.health_checker import HealthChecker
            checker = HealthChecker()
            result = checker._check_dependencies()
            
            # 应该检测到依赖问题
            assert result.name == "项目依赖"

# Windows环境测试计划（预留）
class TestWindowsEnvironmentPlan:
    """Windows环境测试计划（未来实现）"""
    
    def setup_windows_test_plan(self):
        """Windows测试计划设置"""
        return {
            "environment": "Windows 11",
            "test_categories": [
                "UTF-8编码测试",
                "PowerShell兼容性测试", 
                "长路径支持测试",
                "权限管理测试",
                "虚拟终端测试",
                "中文字符显示测试",
                "Windows服务集成测试"
            ],
            "tools_required": [
                "Windows Terminal",
                "PowerShell 7+",
                "Python 3.11+",
                "Git for Windows"
            ],
            "test_data": {
                "chinese_text_samples": ["中文测试", "量化分析", "数据处理"],
                "long_path_samples": ["C:\\" + "很长的路径名" * 20],
                "special_chars": ["@", "#", "$", "%", "^", "&", "*"]
            }
        }
    
    @pytest.mark.skip(reason="Windows环境测试预留")
    def test_windows_utf8_encoding(self):
        """Windows UTF-8编码测试"""
        pass
    
    @pytest.mark.skip(reason="Windows环境测试预留")
    def test_windows_powershell_compatibility(self):
        """Windows PowerShell兼容性测试"""
        pass
    
    @pytest.mark.skip(reason="Windows环境测试预留")
    def test_windows_chinese_display(self):
        """Windows中文显示测试"""
        pass
    
    @pytest.mark.skip(reason="Windows环境测试预留")
    def test_windows_service_integration(self):
        """Windows服务集成测试"""
        pass
    
    @pytest.mark.skip(reason="Windows环境测试预留")
    def test_windows_long_path_support(self):
        """Windows长路径支持测试"""
        pass

# 测试运行器
class TestRunner:
    """集成测试运行器"""
    
    @staticmethod
    def run_macos_integration_tests():
        """运行macOS集成测试"""
        test_classes = [
            TestCLICommandsIntegration,
            TestSetupWizardIntegration,
            TestHealthCheckerIntegration,
            TestEnhancedUIIntegration,
            TestWindowsCompatIntegration,
            TestDevToolsIntegration,
            TestServiceIntegration,
            TestConfigurationIntegration,
            TestEndToEndWorkflows,
            TestPerformanceIntegration,
            TestErrorRecoveryIntegration
        ]
        
        return test_classes
    
    @staticmethod
    def generate_test_report():
        """生成测试报告"""
        return {
            "platform": "macOS",
            "test_environment": "Integration Testing",
            "total_test_classes": 11,
            "estimated_test_cases": 50,
            "coverage_areas": [
                "CLI命令集成",
                "智能配置向导",
                "健康检查系统",
                "增强用户界面",
                "Windows兼容性（模拟）",
                "开发工具链",
                "服务管理",
                "配置系统",
                "端到端工作流",
                "性能测试",
                "错误恢复"
            ]
        }

# 运行特定测试的辅助函数
if __name__ == "__main__":
    # 运行所有集成测试
    pytest.main([
        __file__, 
        "-v", 
        "--tb=short",
        "-x"  # 遇到第一个失败就停止
    ])