"""
跨平台兼容性测试
"""
import pytest
import sys
import os
import platform
from pathlib import Path
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.cli.main import aqua


class TestPlatformCompatibility:
    """平台兼容性测试套件"""
    
    def setup_method(self):
        """测试设置"""
        self.runner = CliRunner()
        self.platform_info = {
            'system': platform.system(),
            'version': platform.version(),
            'machine': platform.machine(),
            'python_version': platform.python_version()
        }
    
    def test_platform_detection(self):
        """测试平台检测"""
        result = self.runner.invoke(aqua, ['status', '--verbose'])
        assert result.exit_code == 0
        
        # 验证平台信息包含在输出中
        assert self.platform_info['system'] in result.output
        assert self.platform_info['python_version'] in result.output
    
    @pytest.mark.skipif(not sys.platform.startswith('win'), reason="Windows specific test")
    def test_windows_compatibility(self):
        """测试Windows兼容性"""
        # 测试Windows路径处理
        result = self.runner.invoke(aqua, ['status', '--verbose'])
        assert result.exit_code == 0
        
        # Windows特定验证
        assert 'Windows' in result.output
        
        # 测试中文字符支持（Windows可能有编码问题）
        result = self.runner.invoke(aqua, ['--help'])
        assert result.exit_code == 0
        # Windows上可能无法正确显示中文，但不应该崩溃
    
    @pytest.mark.skipif(not sys.platform.startswith('darwin'), reason="macOS specific test")
    def test_macos_compatibility(self):
        """测试macOS兼容性"""
        # 测试macOS路径处理
        result = self.runner.invoke(aqua, ['status', '--verbose'])
        assert result.exit_code == 0
        
        # macOS特定验证
        assert 'Darwin' in result.output
        
        # 测试Unicode支持（macOS通常支持较好）
        result = self.runner.invoke(aqua, ['--help'])
        assert result.exit_code == 0
        assert '量化' in result.output or 'AQUA' in result.output
    
    @pytest.mark.skipif(not sys.platform.startswith('linux'), reason="Linux specific test")
    def test_linux_compatibility(self):
        """测试Linux兼容性"""
        # 测试Linux路径处理
        result = self.runner.invoke(aqua, ['status', '--verbose'])
        assert result.exit_code == 0
        
        # Linux特定验证
        assert 'Linux' in result.output
        
        # 测试终端兼容性
        result = self.runner.invoke(aqua, ['collect', '--help'])
        assert result.exit_code == 0
    
    def test_path_separator_handling(self):
        """测试路径分隔符处理"""
        result = self.runner.invoke(aqua, ['status', '--verbose'])
        assert result.exit_code == 0
        
        # 验证路径在输出中正确显示
        assert 'aqua.duckdb' in result.output or 'AQUA' in result.output
        
        # 路径应该使用正确的分隔符
        if sys.platform.startswith('win'):
            # Windows可能包含反斜杠路径
            pass  # 由于输出格式化，直接检查分隔符可能不准确
        else:
            # Unix-like系统使用正斜杠
            pass
    
    def test_file_encoding_support(self):
        """测试文件编码支持"""
        # 测试不同编码的处理
        result = self.runner.invoke(aqua, ['--help'])
        assert result.exit_code == 0
        
        # 测试UTF-8字符处理
        try:
            # 尝试查找中文字符
            has_chinese = '量化' in result.output
            # 无论是否显示中文，都不应该崩溃
            assert True
        except UnicodeDecodeError:
            # 如果有编码错误，说明需要改进
            pytest.fail("Unicode编码处理失败")
    
    def test_terminal_color_support(self):
        """测试终端颜色支持"""
        # 测试Rich颜色输出
        result = self.runner.invoke(aqua, ['status'])
        assert result.exit_code == 0
        
        # 即使终端不支持颜色，也应该能正常运行
        # Rich会自动降级到无颜色输出
        assert len(result.output) > 0
    
    def test_terminal_width_handling(self):
        """测试终端宽度处理"""
        # 测试Rich的自适应宽度
        result = self.runner.invoke(aqua, ['status', '--verbose'])
        assert result.exit_code == 0
        
        # 输出应该适应终端宽度
        lines = result.output.split('\n')
        # 检查是否有合理的行数（不会过度换行）
        assert len(lines) > 5  # 至少有一些内容行
    
    def test_environment_variables(self):
        """测试环境变量支持"""
        # 测试常见环境变量的处理
        original_path = os.environ.get('PATH')
        original_home = os.environ.get('HOME') or os.environ.get('USERPROFILE')
        
        # 验证CLI能正常运行（不依赖特定环境变量）
        result = self.runner.invoke(aqua, ['--help'])
        assert result.exit_code == 0
        
        # 验证环境变量访问不会导致错误
        result = self.runner.invoke(aqua, ['status'])
        assert result.exit_code == 0


class TestResourceConstraints:
    """资源限制测试"""
    
    def setup_method(self):
        """测试设置"""
        self.runner = CliRunner()
    
    def test_memory_constraints(self):
        """测试内存限制"""
        try:
            import psutil
            import os
            
            # 获取初始内存使用
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 执行一系列命令
            commands = [
                ['--help'],
                ['status'],
                ['collect', '--help'],
                ['collect', '--check-capabilities']
            ]
            
            for cmd in commands:
                result = self.runner.invoke(aqua, cmd)
                assert result.exit_code == 0
            
            # 检查内存使用增长
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_growth = final_memory - initial_memory
            
            # 内存增长应该在合理范围内
            assert memory_growth < 100, f"内存增长过多: {memory_growth:.1f}MB"
        except ImportError:
            pytest.skip("psutil未安装，跳过内存测试")
    
    def test_startup_performance(self):
        """测试启动性能"""
        import time
        
        # 测试多次启动时间
        startup_times = []
        
        for _ in range(3):
            start_time = time.time()
            result = self.runner.invoke(aqua, ['--help'])
            end_time = time.time()
            
            assert result.exit_code == 0
            startup_times.append(end_time - start_time)
        
        # 平均启动时间应小于2秒
        avg_startup_time = sum(startup_times) / len(startup_times)
        assert avg_startup_time < 2.0, f"平均启动时间过长: {avg_startup_time:.2f}秒"
        
        # 启动时间应该相对稳定（最大不超过最小的3倍）
        max_time = max(startup_times)
        min_time = min(startup_times)
        assert max_time / min_time < 3.0, f"启动时间不稳定: {min_time:.2f}s - {max_time:.2f}s"
    
    def test_concurrent_execution(self):
        """测试并发执行"""
        # 简化并发测试，避免Click CliRunner的线程安全问题
        import time
        
        # 顺序执行多个命令来模拟并发场景
        commands = [
            ['status'],
            ['--help'],
            ['collect', '--help']
        ]
        
        start_time = time.time()
        results = []
        
        for cmd in commands:
            result = self.runner.invoke(aqua, cmd)
            results.append(result.exit_code)
        
        end_time = time.time()
        
        # 验证所有命令都成功执行
        assert len(results) == 3
        assert all(exit_code == 0 for exit_code in results)
        
        # 执行时间应该合理
        total_time = end_time - start_time
        assert total_time < 10, f"命令执行时间过长: {total_time:.2f}秒"


class TestErrorHandling:
    """错误处理测试"""
    
    def setup_method(self):
        """测试设置"""
        self.runner = CliRunner()
    
    def test_invalid_command_handling(self):
        """测试无效命令处理"""
        # 测试不存在的命令
        result = self.runner.invoke(aqua, ['nonexistent-command'])
        assert result.exit_code != 0
        
        # 错误信息应该有帮助性
        assert 'Usage:' in result.output or 'Error' in result.output or 'help' in result.output
    
    def test_invalid_parameter_handling(self):
        """测试无效参数处理"""
        # 测试无效选项
        result = self.runner.invoke(aqua, ['collect', '--invalid-option'])
        assert result.exit_code != 0
        
        # 测试无效参数值
        result = self.runner.invoke(aqua, ['collect', '000001.SZ', '--source', 'invalid_source'])
        assert result.exit_code != 0
    
    def test_missing_required_arguments(self):
        """测试缺少必需参数"""
        # collect命令在某些情况下需要symbols参数
        result = self.runner.invoke(aqua, ['collect', '--template', 'nonexistent'])
        # 应该有适当的错误处理
        assert result.exit_code == 0  # 因为有默认错误处理
    
    def test_permission_error_handling(self):
        """测试权限错误处理"""
        # 这个测试在CI环境中可能不适用，所以标记为可选
        try:
            # 尝试访问受限资源（如果有的话）
            result = self.runner.invoke(aqua, ['status'])
            # 主要确保不会因权限问题崩溃
            assert result.exit_code == 0
        except PermissionError:
            # 如果有权限错误，应该被优雅处理
            pass
    
    def test_keyboard_interrupt_handling(self):
        """测试键盘中断处理"""
        # 这个测试比较难实现，因为CliRunner不容易模拟KeyboardInterrupt
        # 主要测试CLI能正常启动和退出
        result = self.runner.invoke(aqua, ['--help'])
        assert result.exit_code == 0
    
    def test_unicode_error_handling(self):
        """测试Unicode错误处理"""
        # 测试含有特殊字符的输入
        result = self.runner.invoke(aqua, ['collect', '中文股票代码', '--preview'])
        # 应该不会因为Unicode问题崩溃
        assert result.exit_code == 0


class TestLocalization:
    """本地化测试"""
    
    def setup_method(self):
        """测试设置"""
        self.runner = CliRunner()
    
    def test_chinese_character_support(self):
        """测试中文字符支持"""
        result = self.runner.invoke(aqua, ['--help'])
        assert result.exit_code == 0
        
        # 测试是否包含中文字符（在支持的系统上）
        try:
            has_chinese = '量化' in result.output or '数据' in result.output
            # 无论是否显示中文，CLI都应该正常工作
            assert True
        except UnicodeError:
            # 如果有Unicode错误，需要改进
            pytest.fail("中文字符支持有问题")
    
    def test_timezone_handling(self):
        """测试时区处理"""
        result = self.runner.invoke(aqua, ['status', '--verbose'])
        assert result.exit_code == 0
        
        # 应该包含时区信息
        assert 'timezone' in result.output.lower() or '时区' in result.output
    
    def test_date_format_handling(self):
        """测试日期格式处理"""
        # 测试不同的日期格式输入
        result = self.runner.invoke(aqua, [
            'collect', '000001.SZ', 
            '--start-date', '2025-01-01',
            '--preview'
        ])
        assert result.exit_code == 0