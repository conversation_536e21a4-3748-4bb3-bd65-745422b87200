#!/usr/bin/env python3
"""
FileStatusTracker和JSON状态追踪集成测试

测试Phase 2实现的文件状态追踪功能：
- JSON文件状态记录和持久化
- 增量导入文件检测机制
- 跨session状态保持和恢复
- 状态文件的跨平台兼容性

遵循TDD原则：验证文件状态追踪的可靠性
"""

import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any
from datetime import datetime, timedelta
import pytest

from src.data_import.status.file_tracker import FileStatusTracker
from src.utils.time_utils import get_beijing_time_now
from src.utils.exceptions import AquaException


class TestFileStatusTrackerBasic:
    """FileStatusTracker基础功能测试"""
    
    def setup_method(self):
        """测试初始化"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.status_file = self.temp_dir / "test_status.json"
        self.test_data_dir = self.temp_dir / "test_data"
        self.test_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试文件
        self.test_files = []
        for i in range(3):
            test_file = self.test_data_dir / f"test_file_{i}.csv"
            test_file.write_text(f"header1,header2\ndata{i},value{i}\n", encoding='utf-8')
            self.test_files.append(test_file)
    
    def teardown_method(self):
        """清理测试资源"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_file_tracker_initialization(self):
        """测试FileStatusTracker初始化"""
        tracker = FileStatusTracker(self.status_file)
        
        # 验证初始化成功
        assert tracker is not None, "FileStatusTracker应该初始化成功"
        assert tracker.status_file == self.status_file, "状态文件路径应该正确"
        
        # 验证状态文件自动创建
        assert self.status_file.exists(), "状态文件应该自动创建"
        
        # 验证初始状态文件格式
        with open(self.status_file, 'r', encoding='utf-8') as f:
            status_data = json.load(f)
        
        assert isinstance(status_data, dict), "状态文件应该是JSON字典格式"
        assert "file_status" in status_data, "状态文件应该包含file_status字段"
        assert "statistics" in status_data, "状态文件应该包含statistics字段"
    
    def test_file_status_update(self):
        """测试文件状态更新功能"""
        tracker = FileStatusTracker(self.status_file)
        
        test_file = self.test_files[0]
        
        # 初始状态：文件应该被认为需要更新（新文件）
        assert tracker.is_file_updated(test_file), "新文件应该被认为需要更新"
        
        # 更新文件状态（模拟处理完成）
        tracker.update_status(test_file, records_count=2)
        
        # 现在文件应该不需要更新
        assert not tracker.is_file_updated(test_file), "更新状态后文件不应该需要重新处理"
    
    def test_file_update_detection(self):
        """测试文件更新检测功能"""
        tracker = FileStatusTracker(self.status_file)
        test_file = self.test_files[0]
        
        # 初始更新状态
        tracker.update_status(test_file, records_count=2)
        assert not tracker.is_file_updated(test_file), "刚更新状态的文件不应该被认为需要重新处理"
        
        # 修改文件
        import time
        time.sleep(0.1)  # 确保时间戳不同
        test_file.write_text("header1,header2\nnew_data,new_value\n", encoding='utf-8')
        
        # 检测更新
        assert tracker.is_file_updated(test_file), "修改后的文件应该被检测为更新"
        
        # 重新更新状态后应该不再需要处理
        tracker.update_status(test_file, records_count=3)
        assert not tracker.is_file_updated(test_file), "重新更新状态后不应该被认为需要重新处理"
    
    def test_batch_file_operations(self):
        """测试批量文件操作"""
        tracker = FileStatusTracker(self.status_file)
        
        # 初始状态：所有文件都需要处理
        files_to_process = tracker.get_files_to_process(self.test_files)
        assert len(files_to_process) == len(self.test_files), "初始状态下所有文件都应该需要处理"
        
        # 批量更新文件状态
        for i, test_file in enumerate(self.test_files):
            tracker.update_status(test_file, records_count=i+1)
        
        # 现在应该没有文件需要处理
        files_to_process = tracker.get_files_to_process(self.test_files)
        assert len(files_to_process) == 0, "更新状态后不应该有文件需要重新处理"
        
        # 验证处理摘要
        summary = tracker.get_processing_summary()
        assert isinstance(summary, dict), "处理摘要应该是字典类型"
        assert "statistics" in summary, "摘要应该包含statistics字段"
        assert summary["statistics"]["total_files_tracked"] == len(self.test_files), "总文件数应该正确"


class TestFileStatusTrackerPersistence:
    """FileStatusTracker持久化测试"""
    
    def setup_method(self):
        """测试初始化"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.status_file = self.temp_dir / "persistence_test.json"
        self.test_data_dir = self.temp_dir / "data"
        self.test_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试文件
        self.test_file = self.test_data_dir / "persistent_test.csv"
        self.test_file.write_text("col1,col2\nval1,val2\n", encoding='utf-8')
    
    def teardown_method(self):
        """清理资源"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_status_persistence_across_sessions(self):
        """测试状态在会话间的持久化"""
        # 第一个session：更新文件状态
        tracker1 = FileStatusTracker(self.status_file)
        tracker1.update_status(self.test_file, records_count=5)
        
        # 验证文件状态已更新
        assert not tracker1.is_file_updated(self.test_file), "文件状态应该已更新"
        
        # 第二个session：重新加载
        tracker2 = FileStatusTracker(self.status_file)
        
        # 验证状态被正确恢复
        assert not tracker2.is_file_updated(self.test_file), "重新加载后文件状态应该保持"
        
        # 验证摘要信息一致性
        summary1 = tracker1.get_processing_summary()
        summary2 = tracker2.get_processing_summary()
        assert summary1["statistics"]["total_files_tracked"] == summary2["statistics"]["total_files_tracked"], "文件计数应该一致"
    
    def test_status_file_corruption_recovery(self):
        """测试状态文件损坏时的恢复"""
        # 创建损坏的状态文件
        self.status_file.write_text("{invalid json content", encoding='utf-8')
        
        # 应该能够处理损坏的文件并重新初始化
        tracker = FileStatusTracker(self.status_file)
        
        # 验证能够正常工作
        tracker.update_status(self.test_file, records_count=1)
        assert not tracker.is_file_updated(self.test_file), "损坏恢复后应该能正常工作"
        
        # 验证文件被重新创建为有效格式
        with open(self.status_file, 'r', encoding='utf-8') as f:
            status_data = json.load(f)
        assert isinstance(status_data, dict), "恢复后应该是有效的JSON"
    
    def test_concurrent_access_safety(self):
        """测试并发访问安全性（基础测试）"""
        tracker1 = FileStatusTracker(self.status_file)
        tracker2 = FileStatusTracker(self.status_file)
        
        # 两个tracker操作同一个文件
        tracker1.update_status(self.test_file, records_count=3)
        tracker2.update_status(self.test_file, records_count=5)
        
        # 验证状态一致性（最后一次更新生效）
        assert not tracker1.is_file_updated(self.test_file), "tracker1应该反映最新状态"
        assert not tracker2.is_file_updated(self.test_file), "tracker2应该反映最新状态"
        
        # 验证摘要一致性
        summary1 = tracker1.get_processing_summary()
        summary2 = tracker2.get_processing_summary()
        assert isinstance(summary1, dict), "tracker1摘要应该有效"
        assert isinstance(summary2, dict), "tracker2摘要应该有效"


class TestFileStatusTrackerIncrementalLogic:
    """FileStatusTracker增量导入逻辑测试"""
    
    def setup_method(self):
        """测试初始化"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.status_file = self.temp_dir / "incremental_test.json"
        self.data_dir = self.temp_dir / "csv_data"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建多个测试文件模拟真实场景
        self.create_test_csv_files()
    
    def teardown_method(self):
        """清理资源"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def create_test_csv_files(self):
        """创建测试CSV文件"""
        self.csv_files = []
        
        # 创建不同日期的文件
        base_content = "timestamp,contract_code,open_price,close_price,volume\n"
        
        for i in range(5):
            csv_file = self.data_dir / f"data_202301{i+1:02d}.csv"
            content = base_content + f"2023-01-{i+1:02d} 09:00:00,RB2305,3800.{i},3805.{i},{1000+i*100}\n"
            csv_file.write_text(content, encoding='utf-8')
            self.csv_files.append(csv_file)
    
    def test_initial_full_import_detection(self):
        """测试初始完整导入检测"""
        tracker = FileStatusTracker(self.status_file)
        
        # 初始状态：所有文件都应该被认为需要处理
        files_to_process = tracker.get_files_to_process(self.csv_files)
        
        # 验证所有文件都需要处理
        assert len(files_to_process) == len(self.csv_files), "初始状态下所有文件都应该需要处理"
        
        for csv_file in self.csv_files:
            assert csv_file in files_to_process, f"文件{csv_file}应该在待处理列表中"
    
    def test_incremental_import_detection(self):
        """测试增量导入检测"""
        tracker = FileStatusTracker(self.status_file)
        
        # 第一轮：处理所有文件
        for i, csv_file in enumerate(self.csv_files):
            tracker.update_status(csv_file, records_count=i+1)
        
        # 验证没有文件需要重新处理
        files_to_process = tracker.get_files_to_process(self.csv_files)
        assert len(files_to_process) == 0, "处理后不应该有文件需要重新处理"
        
        # 修改一个文件
        import time
        time.sleep(0.1)
        modified_file = self.csv_files[2]
        current_content = modified_file.read_text(encoding='utf-8')
        modified_file.write_text(current_content + "2023-01-03 09:15:00,RB2305,3801.0,3806.0,1150\n", encoding='utf-8')
        
        # 检测增量更新
        files_to_process = tracker.get_files_to_process(self.csv_files)
        assert len(files_to_process) == 1, "应该只有一个文件需要重新处理"
        assert modified_file in files_to_process, "修改的文件应该在待处理列表中"
    
    def test_new_file_detection(self):
        """测试新文件检测"""
        tracker = FileStatusTracker(self.status_file)
        
        # 更新现有文件状态
        for i, csv_file in enumerate(self.csv_files):
            tracker.update_status(csv_file, records_count=i+1)
        
        # 添加新文件
        new_file = self.data_dir / "new_data_20230106.csv"
        new_content = "timestamp,contract_code,open_price,close_price,volume\n2023-01-06 09:00:00,RB2305,3806.0,3810.0,1300\n"
        new_file.write_text(new_content, encoding='utf-8')
        
        # 检测新文件（包含新文件的列表）
        all_files = self.csv_files + [new_file]
        files_to_process = tracker.get_files_to_process(all_files)
        assert len(files_to_process) == 1, "应该检测到一个新文件"
        assert new_file in files_to_process, "新文件应该在待处理列表中"
    
    def test_file_size_change_detection(self):
        """测试文件大小变化检测"""
        tracker = FileStatusTracker(self.status_file)
        test_file = self.csv_files[0]
        
        # 更新文件状态
        tracker.update_status(test_file, records_count=1)
        original_size = test_file.stat().st_size
        
        # 追加内容（模拟文件增长）
        import time
        time.sleep(0.1)
        test_file.write_text(
            test_file.read_text(encoding='utf-8') + "2023-01-01 09:15:00,RB2305,3801.0,3806.0,1100\n", 
            encoding='utf-8'
        )
        
        # 验证大小变化被检测到
        assert tracker.is_file_updated(test_file), "文件大小变化应该被检测到"
        
        new_size = test_file.stat().st_size
        assert new_size > original_size, "文件大小应该增加"


class TestFileStatusTrackerErrorHandling:
    """FileStatusTracker错误处理测试"""
    
    def setup_method(self):
        """测试初始化"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.status_file = self.temp_dir / "error_test.json"
    
    def teardown_method(self):
        """清理资源"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_nonexistent_file_handling(self):
        """测试不存在文件的处理"""
        tracker = FileStatusTracker(self.status_file)
        nonexistent_file = Path("/nonexistent/path/file.csv")
        
        # 验证不存在的文件处理
        assert not tracker.is_file_updated(nonexistent_file), "不存在的文件不应该被认为需要更新"
        
        # 尝试更新不存在文件的状态应该处理错误
        # 根据实现，可能会记录警告但不抛出异常
        tracker.update_status(nonexistent_file, records_count=1)  # 应该优雅处理
        
        # 验证处理摘要仍然正常
        summary = tracker.get_processing_summary()
        assert isinstance(summary, dict), "即使有错误，摘要也应该正常返回"
    
    def test_permission_error_handling(self):
        """测试权限错误处理"""
        # 创建只读状态文件（如果操作系统支持）
        self.status_file.write_text('{"files": {}, "last_updated": "2023-01-01T00:00:00"}', encoding='utf-8')
        
        try:
            self.status_file.chmod(0o444)  # 设置为只读
            
            # 在某些系统上可能仍然可以写入，这是正常的
            tracker = FileStatusTracker(self.status_file)
            
            # 验证至少能够读取状态
            assert tracker is not None, "即使有权限问题也应该能创建tracker"
            
        except PermissionError:
            # 这是预期的错误，测试通过
            pass
        finally:
            # 恢复权限以便清理
            try:
                self.status_file.chmod(0o666)
            except:
                pass
    
    def test_invalid_status_file_path(self):
        """测试无效状态文件路径处理"""
        # 尝试在不存在的目录中创建状态文件
        invalid_status_file = Path("/nonexistent/directory/status.json")
        
        # 应该能够处理这种情况或给出有意义的错误
        try:
            tracker = FileStatusTracker(invalid_status_file)
            # 如果成功创建，那么可能是自动创建了目录
            assert tracker is not None
        except (FileNotFoundError, PermissionError, OSError):
            # 这些都是合理的错误
            pass


@pytest.mark.integration
class TestFileStatusTrackerFullIntegration:
    """FileStatusTracker完整集成测试标记"""
    
    def test_real_world_scenario_simulation(self):
        """模拟真实世界场景的集成测试"""
        temp_dir = Path(tempfile.mkdtemp())
        try:
            status_file = temp_dir / "production_status.json"
            data_dir = temp_dir / "production_data"
            data_dir.mkdir(parents=True, exist_ok=True)
            
            # 模拟真实CSV文件结构
            real_files = []
            for i in range(10):
                csv_file = data_dir / f"futures_kline_202301{i+1:02d}.csv"
                content = "timestamp,contract_code,open_price,high_price,low_price,close_price,volume,amount\n"
                for j in range(100):  # 每个文件100条记录
                    content += f"2023-01-{i+1:02d} {j%24:02d}:00:00,RB2305,{3800+j},{3810+j},{3790+j},{3805+j},{1000+j*10},{(3805+j)*(1000+j*10)}\n"
                csv_file.write_text(content, encoding='utf-8')
                real_files.append(csv_file)
            
            # 第一次完整导入
            tracker = FileStatusTracker(status_file)
            initial_files = tracker.get_files_to_process(real_files)
            assert len(initial_files) == 10, "初始应该有10个文件需要处理"
            
            # 模拟处理完成
            for i, real_file in enumerate(real_files):
                tracker.update_status(real_file, records_count=100)
            
            # 第二次检查：应该没有文件需要处理
            no_files = tracker.get_files_to_process(real_files)
            assert len(no_files) == 0, "处理后不应该有文件需要重新处理"
            
            # 模拟部分文件更新
            import time
            time.sleep(0.1)
            updated_files = real_files[3:6]  # 更新3个文件
            for f in updated_files:
                content = f.read_text(encoding='utf-8')
                f.write_text(content + "2023-01-04 23:59:00,RB2305,3900,3910,3890,3905,2000,7810000\n", encoding='utf-8')
            
            # 检测增量更新
            incremental_files = tracker.get_files_to_process(real_files)
            assert len(incremental_files) == 3, "应该检测到3个需要增量更新的文件"
            
            for updated_file in updated_files:
                assert updated_file in incremental_files, f"更新的文件{updated_file}应该被检测到"
            
        finally:
            shutil.rmtree(temp_dir)


if __name__ == "__main__":
    # 运行集成测试
    pytest.main([__file__, "-v", "--tb=short"])