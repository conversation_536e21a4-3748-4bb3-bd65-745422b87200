#!/usr/bin/env python3
"""
端到端分层数据导入流程集成测试

测试完整的分层数据导入流程，从配置加载到数据库写入：
- 完整配置驱动的数据导入流程
- 跨组件协作验证（ConfigLoader + FileStatusTracker + SimpleExtractor + DataImportManager）
- 数据库集成验证（DuckDB表创建、数据插入、查询验证）
- 错误恢复和重试机制验证
- 性能和资源管理验证

遵循TDD原则：验证整个系统的端到端可靠性
"""

import tempfile
import shutil
import json
from pathlib import Path
from typing import Dict, Any, List
import pytest
import duckdb

from scripts.data_import_manager import DataImportManager, SimpleExtractorFactory
from src.data_import.status.file_tracker import FileStatusTracker
from src.database.connection_manager import DuckDBConnectionManager
from src.utils.config_loader_v2 import ConfigLoaderV2
from src.utils.time_utils import get_beijing_time_now


class TestEndToEndLayeredImportBasic:
    """端到端分层导入基础流程测试"""
    
    def setup_method(self):
        """测试初始化"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.test_env = "test"
        
        # 创建测试数据目录结构
        self.csv_data_dir = self.temp_dir / "csv_data"
        self.csv_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试数据库路径
        self.test_db_path = self.temp_dir / "test_aqua.duckdb"
        
        # 创建测试状态文件路径
        self.status_file = self.temp_dir / "import_status.json"
        
        # 创建测试CSV数据
        self.create_realistic_test_data()
        
        # 初始化管理器
        self.manager = DataImportManager(self.test_env)
    
    def teardown_method(self):
        """清理测试资源"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def create_realistic_test_data(self):
        """创建现实的测试数据"""
        self.csv_files = []
        
        # 创建符合期货K线数据格式的CSV文件
        contracts = ["RB2305", "HC2305", "I2305"]
        
        for i, contract in enumerate(contracts):
            csv_file = self.csv_data_dir / f"futures_15min_{contract}_{20230101+i}.csv"
            
            # 创建标准期货K线数据格式
            content = "timestamp,contract_code,open_price,high_price,low_price,close_price,volume,amount\n"
            
            # 生成一天的15分钟K线数据（交易时间：9:00-15:00，21:00-23:00）
            base_price = 3800 + i * 100
            for hour in [9, 10, 11, 13, 14, 21, 22]:
                for minute in [0, 15, 30, 45]:
                    timestamp = f"2023-01-{i+1:02d} {hour:02d}:{minute:02d}:00"
                    open_price = base_price + minute * 0.1
                    high_price = open_price + 5
                    low_price = open_price - 3
                    close_price = open_price + 2
                    volume = 1000 + minute * 10
                    amount = close_price * volume
                    
                    content += f"{timestamp},{contract},{open_price:.1f},{high_price:.1f},{low_price:.1f},{close_price:.1f},{volume},{amount:.1f}\n"
            
            csv_file.write_text(content, encoding='utf-8')
            self.csv_files.append(csv_file)
    
    def test_e2e_configuration_to_database(self):
        """测试从配置加载到数据库写入的完整流程"""
        # 1. 配置加载验证
        config_loader = ConfigLoaderV2()
        data_layers_config = config_loader.get_data_layers_config()
        
        assert "csv" in data_layers_config, "应该有CSV层配置"
        csv_config = data_layers_config["csv"]
        assert csv_config["data_source"] == "csv", "CSV配置应该正确"
        
        # 2. 数据库连接验证
        conn_manager = DuckDBConnectionManager(self.test_env)
        conn = conn_manager.get_connection()
        assert conn is not None, "应该能建立数据库连接"
        
        # 3. 创建测试表结构
        test_table_sql = """
        CREATE TABLE IF NOT EXISTS csv_fut_main_contract_kline_15min (
            timestamp TIMESTAMP,
            contract_code VARCHAR,
            open_price DECIMAL(10, 4),
            high_price DECIMAL(10, 4),
            low_price DECIMAL(10, 4),
            close_price DECIMAL(10, 4),
            volume BIGINT,
            amount DECIMAL(15, 2),
            source_file VARCHAR,
            data_source VARCHAR DEFAULT 'csv',
            layer_type VARCHAR DEFAULT 'csv',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        conn.execute(test_table_sql)
        
        # 4. 验证表创建成功
        table_check = conn.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_name = 'csv_fut_main_contract_kline_15min'
        """).fetchone()
        
        assert table_check[0] == 1, "测试表应该创建成功"
        
        conn.close()
    
    def test_e2e_file_status_tracking_workflow(self):
        """测试文件状态追踪的完整工作流程"""
        # 1. 初始化文件状态追踪器
        tracker = FileStatusTracker(self.status_file)
        
        # 2. 第一次扫描：所有文件都需要处理
        files_to_process = tracker.get_files_to_process(self.csv_files)
        assert len(files_to_process) == len(self.csv_files), "初始扫描应该发现所有文件需要处理"
        
        # 3. 模拟处理第一个文件
        first_file = self.csv_files[0]
        tracker.update_status(first_file, records_count=28)  # 7小时 * 4个15分钟
        
        # 4. 第二次扫描：应该少一个文件
        files_to_process = tracker.get_files_to_process(self.csv_files)
        assert len(files_to_process) == len(self.csv_files) - 1, "处理后应该减少一个文件"
        assert first_file not in files_to_process, "已处理的文件不应该在待处理列表中"
        
        # 5. 验证状态持久化（在文件修改之前）
        tracker2 = FileStatusTracker(self.status_file)
        # 注意：未处理的文件对新tracker来说仍然是"需要更新"的（新文件）
        assert tracker2.is_file_updated(self.csv_files[1]), "未处理文件对新tracker应该仍需要处理"
        
        # 已处理的文件在修改前应该保持状态
        assert not tracker2.is_file_updated(first_file), "已处理文件的状态应该持久化"
        
        # 6. 模拟文件更新
        import time
        time.sleep(0.1)
        current_content = first_file.read_text(encoding='utf-8')
        first_file.write_text(current_content + "2023-01-01 23:45:00,RB2305,3820.0,3825.0,3815.0,3822.0,1200,4586400.0\n", encoding='utf-8')
        
        # 7. 第三次扫描：文件更新应该被检测到
        files_to_process = tracker.get_files_to_process(self.csv_files)
        assert first_file in files_to_process, "更新的文件应该被重新检测到"
    
    def test_e2e_extractor_factory_integration(self):
        """测试提取器工厂的端到端集成"""
        # 1. 工厂支持层验证
        supported_layers = SimpleExtractorFactory.get_supported_layers()
        assert "csv" in supported_layers, "工厂应该支持CSV层"
        
        # 2. CSV层配置验证
        csv_config = {
            "name": "test_csv_layer",
            "data_source": "csv",
            "target_table": "csv_fut_main_contract_kline_15min",
            "data_dir": str(self.csv_data_dir),
            "batch_size": 100,
            "max_retries": 2,
            "enable_validation": True,
            "encoding_fallback": ["utf-8", "utf-8-sig", "gbk"],
            "handle_bom": True
        }
        
        validation = SimpleExtractorFactory.validate_layer_config("csv", csv_config)
        assert validation["valid"] == True, "CSV配置应该通过验证"
        
        # 3. 验证不支持的层处理
        with pytest.raises(ValueError, match="不支持的数据层类型"):
            SimpleExtractorFactory.create_extractor("unsupported_layer", csv_config)
        
        # 4. 验证未实现的层处理
        with pytest.raises(NotImplementedError, match="TUSHARE层提取器尚未实现"):
            SimpleExtractorFactory.create_extractor("tushare", csv_config)
    
    def test_e2e_manager_layered_import_workflow(self):
        """测试管理器分层导入的完整工作流程"""
        # 1. 初始化管理器
        manager = DataImportManager(self.test_env)
        
        # 2. 验证分层模式配置
        if manager.layered_mode:
            assert "csv" in manager.enabled_layers, "CSV层应该被启用"
        
        # 3. 初始化文件追踪器
        manager.initialize_file_tracker()
        assert manager.file_tracker is not None, "文件追踪器应该初始化成功"
        
        # 4. 测试分层导入（可能因为CsvLayerExtractor未完全实现而失败）
        try:
            result = manager.import_layered_data("csv", mode="validate")
            
            # 如果成功，验证结果结构
            if isinstance(result, dict):
                assert "success" in result, "结果应该包含success字段"
                assert "layer_type" in result, "结果应该包含layer_type字段"
                
                if result["success"]:
                    assert result["layer_type"] == "csv", "层类型应该正确"
                    
        except (NotImplementedError, ImportError) as e:
            # 如果提取器未实现，这是预期的
            pytest.skip(f"CSV提取器未完全实现: {e}")
        
        # 5. 验证统计信息更新
        stats = manager.stats
        assert isinstance(stats["layered_extractions"], int), "分层提取次数应该是整数"
        assert isinstance(stats["extractor_results"], dict), "提取器结果应该是字典"
        
        # 6. 验证报告生成
        report = manager.generate_report()
        assert "layered_extractions" in report, "报告应该包含分层提取信息"
        assert "extractor_results" in report, "报告应该包含提取器结果"


class TestEndToEndLayeredImportAdvanced:
    """端到端分层导入高级功能测试"""
    
    def setup_method(self):
        """测试初始化"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.test_env = "test"
        self.manager = DataImportManager(self.test_env)
    
    def teardown_method(self):
        """清理资源"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_e2e_error_handling_and_recovery(self):
        """测试端到端错误处理和恢复"""
        # 1. 测试无效层类型的错误处理
        result = self.manager.import_layered_data("invalid_layer", mode="auto")
        
        assert isinstance(result, dict), "错误结果应该是字典"
        assert not result["success"], "无效层应该返回失败"
        assert "error" in result, "失败结果应该包含错误信息"
        
        # 2. 验证错误后管理器状态正常
        report = self.manager.generate_report()
        assert isinstance(report, dict), "错误后应该仍能生成报告"
        
        # 3. 验证错误统计
        assert isinstance(self.manager.stats["errors"], list), "错误列表应该存在"
        
        # 4. 测试连续错误处理
        for invalid_layer in ["layer1", "layer2", "layer3"]:
            result = self.manager.import_layered_data(invalid_layer, mode="auto")
            assert not result["success"], f"无效层{invalid_layer}应该返回失败"
        
        # 5. 验证管理器仍然功能正常
        final_report = self.manager.generate_report()
        assert isinstance(final_report, dict), "多次错误后管理器应该仍然正常"
    
    def test_e2e_layered_mode_disabled_handling(self):
        """测试分层模式禁用时的处理"""
        # 创建一个分层模式可能禁用的管理器
        manager = DataImportManager(self.test_env)
        
        # 模拟分层模式禁用
        original_layered_mode = manager.layered_mode
        manager.layered_mode = False
        
        try:
            # 测试run_layered_import的行为
            # 应该优雅地处理分层模式禁用的情况
            # 由于run_layered_import可能有用户交互，我们测试其他方法
            
            # 验证import_layered_data仍然可以工作
            result = manager.import_layered_data("csv", mode="validate")
            assert isinstance(result, dict), "即使分层模式禁用，也应该返回结果"
            
        finally:
            # 恢复原始状态
            manager.layered_mode = original_layered_mode
    
    def test_e2e_component_isolation(self):
        """测试组件隔离性"""
        # 1. 测试配置加载器独立性
        config_loader = ConfigLoaderV2()
        config1 = config_loader.get_config(self.test_env)
        config2 = config_loader.get_config(self.test_env)
        
        # 配置应该一致但可能是不同的对象实例
        assert config1.keys() == config2.keys(), "配置加载应该一致"
        
        # 2. 测试文件追踪器独立性
        status_file1 = self.temp_dir / "status1.json"
        status_file2 = self.temp_dir / "status2.json"
        
        tracker1 = FileStatusTracker(status_file1)
        tracker2 = FileStatusTracker(status_file2)
        
        # 两个追踪器应该独立工作
        assert tracker1.status_file != tracker2.status_file, "追踪器应该使用不同的状态文件"
        
        # 3. 测试管理器独立性
        manager1 = DataImportManager(self.test_env)
        manager2 = DataImportManager(self.test_env)
        
        # 管理器应该有独立的统计信息
        manager1.stats["layered_extractions"] = 5
        assert manager2.stats["layered_extractions"] != 5, "管理器统计应该独立"
    
    def test_e2e_resource_management(self):
        """测试资源管理"""
        # 1. 测试数据库连接管理
        conn_manager = DuckDBConnectionManager(self.test_env)
        
        # 多次获取连接应该正常工作
        conn1 = conn_manager.get_connection()
        conn2 = conn_manager.get_connection()
        
        assert conn1 is not None, "第一次连接应该成功"
        assert conn2 is not None, "第二次连接应该成功"
        
        # 连接应该是同一个实例（连接池机制）
        assert conn1 is conn2, "应该复用数据库连接"
        
        # 2. 测试文件资源管理
        large_file_count = 50
        test_files = []
        
        for i in range(large_file_count):
            test_file = self.temp_dir / f"large_test_{i}.csv"
            test_file.write_text(f"col1,col2\nvalue{i},data{i}\n", encoding='utf-8')
            test_files.append(test_file)
        
        # 创建追踪器处理大量文件
        tracker = FileStatusTracker(self.temp_dir / "large_status.json")
        files_to_process = tracker.get_files_to_process(test_files)
        
        assert len(files_to_process) == large_file_count, "应该能处理大量文件"
        
        # 3. 测试内存使用情况
        # 验证统计信息不会无限增长
        manager = DataImportManager(self.test_env)
        
        # 模拟多次操作
        for i in range(10):
            try:
                manager.import_layered_data("test_layer", mode="validate")
            except:
                pass
        
        # 验证统计结构仍然合理
        assert len(manager.stats["errors"]) < 100, "错误列表不应该无限增长"
        assert len(str(manager.stats)) < 10000, "统计信息大小应该合理"


@pytest.mark.integration
class TestEndToEndLayeredImportFullIntegration:
    """端到端分层导入完整集成测试标记"""
    
    def test_complete_import_simulation(self):
        """完整导入模拟测试"""
        temp_dir = Path(tempfile.mkdtemp())
        try:
            # 1. 设置完整测试环境
            csv_dir = temp_dir / "csv_data"
            csv_dir.mkdir(parents=True, exist_ok=True)
            status_file = temp_dir / "status.json"
            
            # 2. 创建真实的CSV数据
            test_csv = csv_dir / "futures_test.csv"
            csv_content = "timestamp,contract_code,open_price,high_price,low_price,close_price,volume,amount\n"
            csv_content += "2023-01-01 09:00:00,RB2305,3800.0,3805.0,3795.0,3802.0,1000,3802000.0\n"
            csv_content += "2023-01-01 09:15:00,RB2305,3802.0,3810.0,3800.0,3808.0,1200,4569600.0\n"
            test_csv.write_text(csv_content, encoding='utf-8')
            
            # 3. 初始化所有组件
            config_loader = ConfigLoaderV2()
            data_layers_config = config_loader.get_data_layers_config()
            
            file_tracker = FileStatusTracker(status_file)
            manager = DataImportManager("test")
            
            # 4. 执行完整工作流程
            # 配置验证
            assert "csv" in data_layers_config, "应该有CSV配置"
            
            # 文件状态追踪
            files_to_process = file_tracker.get_files_to_process([test_csv])
            assert len(files_to_process) == 1, "应该检测到需要处理的文件"
            
            # 模拟数据处理
            file_tracker.update_status(test_csv, records_count=2)
            
            # 验证处理结果
            files_after_processing = file_tracker.get_files_to_process([test_csv])
            assert len(files_after_processing) == 0, "处理后文件不应该需要重新处理"
            
            # 5. 生成最终报告
            report = manager.generate_report()
            
            # 验证报告完整性
            required_fields = ["session_id", "environment", "layered_mode", "enabled_layers"]
            for field in required_fields:
                assert field in report, f"完整报告应该包含{field}"
            
            # 6. 验证状态持久化
            summary = file_tracker.get_processing_summary()
            assert summary["statistics"]["total_files_tracked"] == 1, "摘要应该反映处理的文件数"
            
        finally:
            shutil.rmtree(temp_dir)
    
    def test_cross_component_error_propagation(self):
        """跨组件错误传播测试"""
        manager = DataImportManager("test")
        
        # 测试错误在组件间的传播
        error_scenarios = [
            ("invalid_config", "auto"),
            ("missing_layer", "validate"), 
            ("bad_mode", "nonexistent_mode"),
        ]
        
        for layer, mode in error_scenarios:
            try:
                result = manager.import_layered_data(layer, mode)
                
                # 错误应该被适当处理和记录
                if isinstance(result, dict) and not result.get("success", True):
                    assert "error" in result, "失败结果应该包含错误信息"
                    
            except Exception as e:
                # 异常应该是预期的类型
                assert isinstance(e, (ValueError, NotImplementedError, Exception)), \
                    f"异常类型应该合适: {type(e)}"
        
        # 验证错误后系统仍然稳定
        final_report = manager.generate_report()
        assert isinstance(final_report, dict), "错误后系统应该仍然稳定"
    
    def test_performance_characteristics(self):
        """性能特征测试"""
        import time
        
        # 1. 测试启动时间
        start_time = time.time()
        manager = DataImportManager("test")
        init_time = time.time() - start_time
        
        assert init_time < 5.0, "管理器初始化应该在5秒内完成"
        
        # 2. 测试报告生成性能
        start_time = time.time()
        report = manager.generate_report()
        report_time = time.time() - start_time
        
        assert report_time < 1.0, "报告生成应该在1秒内完成"
        assert isinstance(report, dict), "报告应该正确生成"
        
        # 3. 测试多次操作的性能一致性
        times = []
        for i in range(5):
            start_time = time.time()
            try:
                manager.import_layered_data("test_layer", "validate")
            except:
                pass
            operation_time = time.time() - start_time
            times.append(operation_time)
        
        # 操作时间应该相对一致（无内存泄漏等问题）
        avg_time = sum(times) / len(times)
        assert avg_time < 0.5, "单次操作应该在0.5秒内完成"
        
        # 最后一次操作不应该比第一次慢太多
        assert times[-1] < times[0] * 3, "性能不应该显著退化"


if __name__ == "__main__":
    # 运行端到端集成测试
    pytest.main([__file__, "-v", "--tb=short"])