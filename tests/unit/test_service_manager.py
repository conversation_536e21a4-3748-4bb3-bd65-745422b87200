#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA服务管理器单元测试

测试简化服务管理功能
版本: 1.0.0
创建时间: 2025-07-31
"""

import unittest
from unittest.mock import patch, MagicMock, call
from pathlib import Path
import sys
import subprocess
import time
import signal

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.service_manager import (
    ServiceStatus, Service, ServiceManager, create_startup_script
)


class TestServiceStatus(unittest.TestCase):
    """服务状态枚举测试"""
    
    def test_status_values(self):
        """测试状态值"""
        self.assertEqual(ServiceStatus.STOPPED.value, "stopped")
        self.assertEqual(ServiceStatus.STARTING.value, "starting")
        self.assertEqual(ServiceStatus.RUNNING.value, "running")
        self.assertEqual(ServiceStatus.ERROR.value, "error")


class TestService(unittest.TestCase):
    """服务数据类测试"""
    
    def test_service_creation(self):
        """测试服务创建"""
        service = Service(
            name="test_service",
            command="test command",
            description="测试服务",
            port=8080
        )
        
        self.assertEqual(service.name, "test_service")
        self.assertEqual(service.command, "test command")
        self.assertEqual(service.description, "测试服务")
        self.assertEqual(service.port, 8080)
        self.assertEqual(service.status, ServiceStatus.STOPPED)
        self.assertIsNone(service.process)
        self.assertIsNone(service.start_time)
        self.assertEqual(service.logs, [])
    
    def test_service_defaults(self):
        """测试服务默认值"""
        service = Service(name="minimal", command="cmd")
        
        self.assertEqual(service.description, "")
        self.assertIsNone(service.port)
        self.assertIsNone(service.health_check)
        self.assertEqual(service.startup_time, 3)
        self.assertTrue(service.required)


class TestServiceManager(unittest.TestCase):
    """服务管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        # 模拟配置和UI
        self.mock_config = MagicMock()
        self.mock_config.is_debug.return_value = False
        
        self.mock_ui = MagicMock()
        
        with patch('utils.service_manager.get_config_manager', return_value=self.mock_config), \
             patch('utils.service_manager.get_ui', return_value=self.mock_ui):
            self.manager = ServiceManager()
    
    def test_manager_initialization(self):
        """测试管理器初始化"""
        self.assertIsNotNone(self.manager.config)
        self.assertIsNotNone(self.manager.ui)
        self.assertIsInstance(self.manager.services, dict)
        self.assertFalse(self.manager.running)
        self.assertIsNotNone(self.manager.shutdown_event)
    
    def test_services_definition(self):
        """测试服务定义"""
        # 应该有后端服务
        self.assertIn("backend", self.manager.services)
        backend = self.manager.services["backend"]
        self.assertEqual(backend.name, "backend")
        self.assertEqual(backend.port, 8000)
        self.assertTrue(backend.required)
        self.assertIn("uvicorn", backend.command)
        
        # 可能有前端服务（取决于frontend目录是否存在）
        frontend_path = Path(__file__).parent.parent.parent / "frontend"
        if frontend_path.exists():
            self.assertIn("frontend", self.manager.services)
            frontend = self.manager.services["frontend"]
            self.assertEqual(frontend.name, "frontend")
            self.assertEqual(frontend.port, 5173)
            self.assertFalse(frontend.required)
    
    @patch('sys.platform', 'win32')
    def test_windows_venv_detection(self):
        """测试Windows虚拟环境检测"""
        with patch('utils.service_manager.get_config_manager', return_value=self.mock_config), \
             patch('utils.service_manager.get_ui', return_value=self.mock_ui):
            manager = ServiceManager()
            
            # 检查Windows路径
            backend = manager.services["backend"]
            self.assertIn("Scripts", backend.command)
    
    @patch('sys.platform', 'linux')
    def test_unix_venv_detection(self):
        """测试Unix虚拟环境检测"""
        with patch('utils.service_manager.get_config_manager', return_value=self.mock_config), \
             patch('utils.service_manager.get_ui', return_value=self.mock_ui):
            manager = ServiceManager()
            
            # 检查Unix路径
            backend = manager.services["backend"]
            self.assertIn("bin", backend.command)
    
    def test_debug_mode_command(self):
        """测试调试模式命令"""
        self.mock_config.is_debug.return_value = True
        
        with patch('utils.service_manager.get_config_manager', return_value=self.mock_config), \
             patch('utils.service_manager.get_ui', return_value=self.mock_ui):
            manager = ServiceManager()
            
            backend = manager.services["backend"]
            self.assertIn("--log-level debug", backend.command)
    
    @patch('subprocess.Popen')
    @patch('time.sleep')
    def test_start_service_success(self, mock_sleep, mock_popen):
        """测试启动服务成功"""
        # 模拟成功的进程
        mock_process = MagicMock()
        mock_process.poll.return_value = None  # 进程还在运行
        mock_popen.return_value = mock_process
        
        # 测试启动
        result = self.manager.start_service("backend")
        
        self.assertTrue(result)
        backend = self.manager.services["backend"]
        self.assertEqual(backend.status, ServiceStatus.RUNNING)
        self.assertEqual(backend.process, mock_process)
        self.assertIsNotNone(backend.start_time)
    
    @patch('subprocess.Popen')
    def test_start_service_failure(self, mock_popen):
        """测试启动服务失败"""
        # 模拟失败的进程
        mock_process = MagicMock()
        mock_process.poll.return_value = 1  # 进程已退出
        mock_popen.return_value = mock_process
        
        result = self.manager.start_service("backend")
        
        self.assertFalse(result)
        backend = self.manager.services["backend"]
        self.assertEqual(backend.status, ServiceStatus.ERROR)
    
    def test_start_nonexistent_service(self):
        """测试启动不存在的服务"""
        result = self.manager.start_service("nonexistent")
        self.assertFalse(result)
    
    def test_start_already_running_service(self):
        """测试启动已运行的服务"""
        # 设置服务为运行状态
        backend = self.manager.services["backend"]
        backend.status = ServiceStatus.RUNNING
        
        result = self.manager.start_service("backend")
        self.assertTrue(result)
    
    def test_stop_service_success(self):
        """测试停止服务成功"""
        # 模拟运行中的服务
        mock_process = MagicMock()
        backend = self.manager.services["backend"]
        backend.status = ServiceStatus.RUNNING
        backend.process = mock_process
        
        result = self.manager.stop_service("backend")
        
        self.assertTrue(result)
        mock_process.terminate.assert_called_once()
        mock_process.wait.assert_called()
        self.assertEqual(backend.status, ServiceStatus.STOPPED)
        self.assertIsNone(backend.process)
    
    def test_stop_service_force_kill(self):
        """测试强制终止服务"""
        # 模拟无法正常终止的进程
        mock_process = MagicMock()
        mock_process.wait.side_effect = [subprocess.TimeoutExpired("cmd", 5), None]
        
        backend = self.manager.services["backend"]
        backend.status = ServiceStatus.RUNNING
        backend.process = mock_process
        
        result = self.manager.stop_service("backend")
        
        self.assertTrue(result)
        mock_process.terminate.assert_called_once()
        mock_process.kill.assert_called_once()
        self.assertEqual(backend.status, ServiceStatus.STOPPED)
    
    def test_stop_nonexistent_service(self):
        """测试停止不存在的服务"""
        result = self.manager.stop_service("nonexistent")
        self.assertFalse(result)
    
    def test_stop_not_running_service(self):
        """测试停止未运行的服务"""
        result = self.manager.stop_service("backend")
        self.assertTrue(result)
    
    @patch.object(ServiceManager, 'start_service')
    def test_start_all_services_success(self, mock_start):
        """测试启动所有服务成功"""
        mock_start.return_value = True
        
        result = self.manager.start_all_services()
        
        self.assertTrue(result)
        self.assertTrue(self.manager.running)
        # 验证启动顺序（后端优先）
        calls = mock_start.call_args_list
        service_names = [call[0][0] for call in calls]
        if "backend" in service_names and "frontend" in service_names:
            backend_index = service_names.index("backend")
            frontend_index = service_names.index("frontend")
            self.assertLess(backend_index, frontend_index)
    
    @patch.object(ServiceManager, 'start_service')
    def test_start_all_services_required_failure(self, mock_start):
        """测试必需服务启动失败"""
        def mock_start_side_effect(service_name):
            if service_name == "backend":
                return False  # 后端服务启动失败
            return True
        
        mock_start.side_effect = mock_start_side_effect
        
        result = self.manager.start_all_services()
        
        self.assertFalse(result)
    
    @patch.object(ServiceManager, 'stop_service')
    def test_stop_all_services(self, mock_stop):
        """测试停止所有服务"""
        self.manager.running = True
        
        self.manager.stop_all_services()
        
        self.assertFalse(self.manager.running)
        self.assertTrue(self.manager.shutdown_event.is_set())
        
        # 验证所有服务都被停止
        self.assertGreaterEqual(mock_stop.call_count, len(self.manager.services))
    
    @patch.object(ServiceManager, 'start_service')
    @patch.object(ServiceManager, 'stop_service') 
    @patch('time.sleep')
    def test_restart_service(self, mock_sleep, mock_stop, mock_start):
        """测试重启服务"""
        mock_stop.return_value = True
        mock_start.return_value = True
        
        result = self.manager.restart_service("backend")
        
        self.assertTrue(result)
        mock_stop.assert_called_once_with("backend")
        mock_start.assert_called_once_with("backend")
        mock_sleep.assert_called_once_with(1)
    
    def test_get_service_logs(self):
        """测试获取服务日志"""
        # 添加日志到服务
        backend = self.manager.services["backend"]
        test_logs = ["log1", "log2", "log3", "log4", "log5"]
        backend.logs = test_logs
        
        # 获取最近3行日志
        logs = self.manager.get_service_logs("backend", 3)
        self.assertEqual(logs, ["log3", "log4", "log5"])
        
        # 获取不存在服务的日志
        logs = self.manager.get_service_logs("nonexistent")
        self.assertEqual(logs, [])
    
    @patch('time.sleep')
    def test_monitor_services_running(self, mock_sleep):
        """测试服务监控（正常运行）"""
        # 模拟运行中的服务
        mock_process = MagicMock()
        mock_process.poll.return_value = None  # 进程还在运行
        
        backend = self.manager.services["backend"]
        backend.status = ServiceStatus.RUNNING
        backend.process = mock_process
        
        self.manager.running = True
        
        # 模拟KeyboardInterrupt来停止监控
        mock_sleep.side_effect = [None, KeyboardInterrupt()]
        
        with patch.object(self.manager, 'stop_all_services') as mock_stop:
            self.manager.monitor_services()
            mock_stop.assert_called_once()
    
    @patch('time.sleep')
    def test_monitor_services_service_died(self, mock_sleep):
        """测试服务监控（服务意外退出）"""
        # 模拟已退出的服务
        mock_process = MagicMock()
        mock_process.poll.return_value = 1  # 进程已退出
        
        backend = self.manager.services["backend"]
        backend.status = ServiceStatus.RUNNING
        backend.process = mock_process
        backend.required = True
        
        self.manager.running = True
        
        # 第一次检查发现服务退出，第二次抛出异常停止
        mock_sleep.side_effect = [None, KeyboardInterrupt()]
        
        with patch.object(self.manager, 'restart_service', return_value=True) as mock_restart, \
             patch.object(self.manager, 'stop_all_services') as mock_stop:
            
            self.manager.monitor_services()
            
            # 验证尝试重启
            mock_restart.assert_called_once_with("backend")
            mock_stop.assert_called_once()
    
    @patch('requests.get')
    def test_health_check_success(self, mock_get):
        """测试健康检查成功"""
        # 模拟成功的HTTP响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        # 创建有健康检查的服务
        service = Service(
            name="test",
            command="test",
            health_check="http://localhost:8000/health",
            startup_time=0.1
        )
        
        # 启动健康检查线程并等待完成
        import threading
        health_check_done = threading.Event()
        
        def mock_health_check():
            try:
                import requests
                response = requests.get(service.health_check, timeout=5)
                if response.status_code == 200:
                    self.mock_ui.print_step.assert_any_call(
                        f"{service.description} 健康检查通过", "success"
                    )
            except Exception:
                pass
            finally:
                health_check_done.set()
        
        thread = threading.Thread(target=mock_health_check)
        thread.start()
        health_check_done.wait(timeout=1)
    
    @patch('socket.socket')
    def test_port_check_success(self, mock_socket_class):
        """测试端口检查成功"""
        # 模拟成功的socket连接
        mock_socket = MagicMock()
        mock_socket.connect_ex.return_value = 0  # 连接成功
        mock_socket_class.return_value.__enter__.return_value = mock_socket
        
        service = Service(name="test", command="test", port=8000)
        
        self.manager._check_port(service)
        
        # 验证socket连接尝试
        mock_socket.connect_ex.assert_called_once_with(('127.0.0.1', 8000))


class TestStartupScript(unittest.TestCase):
    """启动脚本测试"""
    
    @patch('builtins.open', create=True)
    @patch('os.chmod')
    def test_create_startup_script(self, mock_chmod, mock_open):
        """测试创建启动脚本"""
        mock_file = MagicMock()
        mock_open.return_value.__enter__.return_value = mock_file
        
        script_path = create_startup_script()
        
        # 验证文件写入
        mock_open.assert_called_once()
        mock_file.write.assert_called_once()
        
        # 验证权限设置
        mock_chmod.assert_called_once()
        
        # 验证返回路径
        self.assertIsInstance(script_path, Path)
        self.assertEqual(script_path.name, "start_aqua.py")
    
    @patch('builtins.open', create=True)
    @patch('os.chmod', side_effect=AttributeError())
    def test_create_startup_script_no_chmod(self, mock_chmod, mock_open):
        """测试在没有chmod的系统上创建启动脚本"""
        mock_file = MagicMock()
        mock_open.return_value.__enter__.return_value = mock_file
        
        # 应该不抛出异常
        script_path = create_startup_script()
        self.assertIsInstance(script_path, Path)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    @patch('utils.service_manager.get_config_manager')
    @patch('utils.service_manager.get_ui')
    @patch('subprocess.Popen')
    @patch('time.sleep')
    def test_full_service_lifecycle(self, mock_sleep, mock_popen, mock_get_ui, mock_get_config):
        """测试完整服务生命周期"""
        # 设置模拟
        mock_config = MagicMock()
        mock_config.is_debug.return_value = False
        mock_get_config.return_value = mock_config
        
        mock_ui = MagicMock()
        mock_get_ui.return_value = mock_ui
        
        mock_process = MagicMock()
        mock_process.poll.return_value = None
        mock_popen.return_value = mock_process
        
        # 创建管理器并执行完整生命周期
        manager = ServiceManager()
        
        # 1. 启动所有服务
        result = manager.start_all_services()
        self.assertTrue(result)
        
        # 2. 检查服务状态
        for service in manager.services.values():
            if service.required:
                self.assertEqual(service.status, ServiceStatus.RUNNING)
        
        # 3. 重启服务
        with patch.object(manager, 'stop_service', return_value=True), \
             patch.object(manager, 'start_service', return_value=True):
            result = manager.restart_service("backend") 
            self.assertTrue(result)
        
        # 4. 停止所有服务
        manager.stop_all_services()
        self.assertFalse(manager.running)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)