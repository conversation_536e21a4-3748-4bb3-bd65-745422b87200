#!/usr/bin/env python3
"""
快速测试AQUA统一API接口
"""
import requests
import json
import time

def test_unified_api():
    """测试统一数据路由API"""
    base_url = "http://127.0.0.1:8000/api/data"
    
    # 测试MySQL配置接口
    print("🔍 测试MySQL配置接口...")
    try:
        response = requests.get(f"{base_url}/config/mysql/dev", timeout=5)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器，请确保main.py正在运行")
        return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False
    
    # 测试导入预检查接口
    print("\n🔍 测试导入预检查接口...")
    precheck_data = {
        "import_type": "csv",
        "environment": "dev",
        "encoding": "utf-8",
        "delimiter": ",",
        "hasHeader": True
    }
    
    try:
        response = requests.post(
            f"{base_url}/import/pre-check", 
            json=precheck_data,
            timeout=10
        )
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    return True

if __name__ == "__main__":
    print("🚀 AQUA统一API测试工具")
    print("=" * 50)
    
    # 给后端服务器一些启动时间
    print("⏳ 等待后端服务器启动...")
    time.sleep(2)
    
    test_unified_api()
    
    print("\n✅ API测试完成")