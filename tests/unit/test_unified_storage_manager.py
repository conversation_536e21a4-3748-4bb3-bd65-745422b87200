#!/usr/bin/env python3
"""
统一存储管理器测试套件

测试功能：
1. 多环境数据库实例管理
2. 连接池管理和健康检查
3. 表结构版本管理和迁移
4. 统一备份和恢复策略
5. 跨平台兼容性
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
from datetime import datetime

from src.storage.unified_storage_manager import UnifiedStorageManager
from src.database.connection_manager import DuckDBConnectionManager


class TestUnifiedStorageManager:
    """统一存储管理器测试类"""

    @pytest.fixture
    def temp_dir(self):
        """创建临时测试目录"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def storage_manager(self, temp_dir):
        """创建统一存储管理器实例"""
        config = {
            'database': {
                'path': str(temp_dir / 'aqua_test.db'),
                'environments': {
                    'dev': str(temp_dir / 'dev_aqua.db'),
                    'test': str(temp_dir / 'test_aqua.db'),
                    'prod': str(temp_dir / 'prod_aqua.db')
                },
                'connection_pool_size': 5,
                'max_connections_per_db': 3
            }
        }
        return UnifiedStorageManager(config)

    def test_init_storage_manager(self, storage_manager):
        """测试存储管理器初始化"""
        assert storage_manager is not None
        assert hasattr(storage_manager, 'connection_pools')
        assert hasattr(storage_manager, 'environments')
        assert hasattr(storage_manager, 'health_checker')

    def test_multi_environment_management(self, storage_manager):
        """测试多环境数据库实例管理"""
        # 测试获取不同环境的连接
        dev_conn = storage_manager.get_connection('dev')
        test_conn = storage_manager.get_connection('test')
        prod_conn = storage_manager.get_connection('prod')
        
        assert dev_conn is not None
        assert test_conn is not None
        assert prod_conn is not None
        
        # 测试环境隔离
        assert dev_conn != test_conn
        assert test_conn != prod_conn

    def test_connection_pool_management(self, storage_manager):
        """测试连接池管理"""
        # 测试连接池创建
        pool = storage_manager.get_connection_pool('dev')
        assert pool is not None
        assert hasattr(pool, 'get_connection')
        assert hasattr(pool, 'return_connection')
        
        # 测试连接池大小限制
        connections = []
        for i in range(6):  # 超过配置的5个连接
            try:
                conn = storage_manager.get_connection('dev')
                connections.append(conn)
            except Exception as e:
                # 应该在第6个连接时抛出异常
                assert "connection pool full" in str(e).lower()
                break
        
        # 释放连接
        for conn in connections:
            storage_manager.return_connection('dev', conn)

    def test_health_check(self, storage_manager):
        """测试连接健康检查"""
        # 测试正常连接的健康检查
        health_status = storage_manager.check_health('dev')
        assert health_status['status'] == 'healthy'
        assert 'response_time' in health_status
        assert 'last_check' in health_status
        
        # 测试所有环境的健康检查
        all_health = storage_manager.check_all_health()
        assert 'dev' in all_health
        assert 'test' in all_health
        assert 'prod' in all_health

    def test_table_version_management(self, storage_manager):
        """测试表结构版本管理"""
        # 创建测试表
        table_name = 'test_futures_daily'
        schema = {
            'version': '1.0.0',
            'columns': [
                {'name': 'contract_code', 'type': 'VARCHAR', 'nullable': False},
                {'name': 'trade_date', 'type': 'DATE', 'nullable': False},
                {'name': 'open', 'type': 'DOUBLE', 'nullable': True},
                {'name': 'close', 'type': 'DOUBLE', 'nullable': True}
            ],
            'primary_keys': ['contract_code', 'trade_date'],
            'indexes': ['contract_code', 'trade_date']
        }
        
        # 测试表创建
        result = storage_manager.create_table_with_version('dev', table_name, schema)
        assert result['success'] is True
        assert result['version'] == '1.0.0'
        
        # 测试版本查询
        current_version = storage_manager.get_table_version('dev', table_name)
        assert current_version == '1.0.0'
        
        # 测试表结构迁移
        new_schema = schema.copy()
        new_schema['version'] = '1.1.0'
        new_schema['columns'].append({
            'name': 'volume', 'type': 'BIGINT', 'nullable': True
        })
        
        migration_result = storage_manager.migrate_table('dev', table_name, new_schema)
        assert migration_result['success'] is True
        assert migration_result['old_version'] == '1.0.0'
        assert migration_result['new_version'] == '1.1.0'

    def test_backup_and_recovery(self, storage_manager, temp_dir):
        """测试统一备份和恢复策略"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'contract_code': ['FU2403', 'FU2404'],
            'trade_date': ['2024-01-15', '2024-01-16'],
            'open': [3000.0, 3050.0],
            'close': [3020.0, 3080.0]
        })
        
        # 插入测试数据
        storage_manager.insert_data('dev', 'test_futures_daily', test_data)
        
        # 测试单表备份
        backup_path = temp_dir / 'backup_test_table.parquet'
        backup_result = storage_manager.backup_table(
            'dev', 'test_futures_daily', str(backup_path)
        )
        assert backup_result['success'] is True
        assert backup_path.exists()
        
        # 测试完整数据库备份
        db_backup_path = temp_dir / 'full_backup.db'
        full_backup_result = storage_manager.backup_database('dev', str(db_backup_path))
        assert full_backup_result['success'] is True
        assert db_backup_path.exists()
        
        # 测试表恢复
        # 先删除表
        storage_manager.drop_table('dev', 'test_futures_daily')
        
        # 恢复表
        recovery_result = storage_manager.restore_table(
            'dev', 'test_futures_daily', str(backup_path)
        )
        assert recovery_result['success'] is True
        
        # 验证数据恢复
        recovered_data = storage_manager.query_data('dev', 'test_futures_daily')
        assert len(recovered_data) == 2
        assert set(recovered_data['contract_code']) == {'FU2403', 'FU2404'}

    def test_cross_platform_compatibility(self, storage_manager):
        """测试跨平台兼容性"""
        import platform
        current_platform = platform.system()
        
        # 测试路径处理
        if current_platform == 'Windows':
            test_path = r'C:\data\aqua\test.db'
        else:
            test_path = '/data/aqua/test.db'
        
        normalized_path = storage_manager.normalize_path(test_path)
        assert isinstance(normalized_path, Path)
        
        # 测试平台特定的数据库配置
        platform_config = storage_manager.get_platform_config()
        assert 'platform' in platform_config
        assert platform_config['platform'] in ['Windows', 'Darwin', 'Linux']
        
        # 测试字符编码处理
        chinese_table_name = '期货日线数据'
        encoded_name = storage_manager.encode_table_name(chinese_table_name)
        assert isinstance(encoded_name, str)
        decoded_name = storage_manager.decode_table_name(encoded_name)
        assert decoded_name == chinese_table_name

    def test_performance_monitoring(self, storage_manager):
        """测试性能监控"""
        # 测试连接池性能统计
        pool_stats = storage_manager.get_pool_statistics('dev')
        assert 'active_connections' in pool_stats
        assert 'total_connections' in pool_stats
        assert 'average_response_time' in pool_stats
        
        # 测试查询性能监控
        with storage_manager.performance_monitor('dev') as monitor:
            # 执行一些数据库操作
            storage_manager.query_data('dev', 'INFORMATION_SCHEMA.TABLES')
        
        performance_report = monitor.get_report()
        assert 'execution_time' in performance_report
        assert 'memory_usage' in performance_report
        assert 'query_count' in performance_report

    def test_connection_failover(self, storage_manager):
        """测试连接故障转移"""
        # 模拟连接故障
        with patch.object(storage_manager, '_test_connection') as mock_test:
            mock_test.return_value = False
            
            # 测试自动故障转移
            failover_result = storage_manager.handle_connection_failure('dev')
            assert failover_result['action'] == 'failover'
            assert 'backup_connection' in failover_result
        
        # 测试故障恢复
        with patch.object(storage_manager, '_test_connection') as mock_test:
            mock_test.return_value = True
            
            recovery_result = storage_manager.recover_connection('dev')
            assert recovery_result['success'] is True

    def test_batch_operations(self, storage_manager):
        """测试批量操作"""
        # 准备批量测试数据
        batch_data = []
        for i in range(1000):
            batch_data.append({
                'contract_code': f'FU24{i:02d}',
                'trade_date': f'2024-01-{(i % 30) + 1:02d}',
                'open': 3000.0 + i,
                'close': 3020.0 + i
            })
        
        batch_df = pd.DataFrame(batch_data)
        
        # 测试批量插入性能
        start_time = datetime.now()
        result = storage_manager.batch_insert('dev', 'test_futures_daily', batch_df)
        execution_time = (datetime.now() - start_time).total_seconds()
        
        assert result['success'] is True
        assert result['inserted_rows'] == 1000
        assert execution_time < 5.0  # 批量插入应在5秒内完成
        
        # 测试批量更新
        update_data = batch_df.copy()
        update_data['close'] = update_data['close'] + 10
        
        update_result = storage_manager.batch_update(
            'dev', 'test_futures_daily', update_data, 
            match_columns=['contract_code', 'trade_date']
        )
        assert update_result['success'] is True
        assert update_result['updated_rows'] == 1000

    def test_transaction_management(self, storage_manager):
        """测试事务管理"""
        # 测试事务提交
        with storage_manager.transaction('dev') as tx:
            tx.insert_data('test_futures_daily', pd.DataFrame({
                'contract_code': ['TX001'],
                'trade_date': ['2024-01-20'],
                'open': [3000.0],
                'close': [3020.0]
            }))
            
            tx.insert_data('test_futures_daily', pd.DataFrame({
                'contract_code': ['TX002'],
                'trade_date': ['2024-01-20'],
                'open': [3100.0],
                'close': [3120.0]
            }))
        
        # 验证事务提交
        result = storage_manager.query_data(
            'dev', 'test_futures_daily', 
            where_clause="contract_code IN ('TX001', 'TX002')"
        )
        assert len(result) == 2
        
        # 测试事务回滚
        try:
            with storage_manager.transaction('dev') as tx:
                tx.insert_data('test_futures_daily', pd.DataFrame({
                    'contract_code': ['TX003'],
                    'trade_date': ['2024-01-20'],
                    'open': [3200.0],
                    'close': [3220.0]
                }))
                
                # 故意抛出异常触发回滚
                raise ValueError("Test rollback")
        except ValueError:
            pass
        
        # 验证事务回滚
        result = storage_manager.query_data(
            'dev', 'test_futures_daily',
            where_clause="contract_code = 'TX003'"
        )
        assert len(result) == 0

    def test_storage_quota_management(self, storage_manager):
        """测试存储配额管理"""
        # 设置存储配额
        storage_manager.set_storage_quota('dev', max_size_gb=1.0)
        
        # 检查当前存储使用情况
        usage = storage_manager.get_storage_usage('dev')
        assert 'used_space_mb' in usage
        assert 'available_space_mb' in usage
        assert 'usage_percentage' in usage
        
        # 测试配额警告
        quota_status = storage_manager.check_storage_quota('dev')
        assert 'status' in quota_status
        assert quota_status['status'] in ['ok', 'warning', 'critical']

    def test_error_handling(self, storage_manager):
        """测试错误处理"""
        # 测试无效环境处理
        with pytest.raises(ValueError):
            storage_manager.get_connection('invalid_env')
        
        # 测试数据库文件损坏处理
        with patch.object(storage_manager, '_is_database_corrupted') as mock_corrupted:
            mock_corrupted.return_value = True
            
            repair_result = storage_manager.repair_database('dev')
            assert 'repair_attempted' in repair_result
        
        # 测试连接超时处理
        with patch.object(storage_manager, '_get_connection_timeout') as mock_timeout:
            mock_timeout.side_effect = TimeoutError("Connection timeout")
            
            with pytest.raises(TimeoutError):
                storage_manager.get_connection('dev', timeout=1)