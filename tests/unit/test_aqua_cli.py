import unittest
from unittest.mock import patch, MagicMock
import sys
import os

# 将项目根目录添加到sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

class TestAquaCLI(unittest.TestCase):

    @patch('aqua.main')
    def test_cli_entrypoint_exists(self, mock_main):
        """
        测试 aqua.py 是否可以作为模块导入并且主函数可以被调用。
        """
        try:
            import aqua
            # 验证模块可以被导入
            self.assertIsNotNone(aqua)
            # 验证main函数存在
            self.assertTrue(callable(aqua.main))
        except ImportError:
            self.fail("无法导入 'aqua' 模块。请确保 aqua.py 在项目根目录。")

    @patch('argparse.ArgumentParser.parse_args')
    @patch('aqua.handle_init')
    def test_init_command_dispatch(self, mock_handle_init, mock_parse_args):
        """
        测试 'init' 子命令是否能被正确分发到其处理函数。
        """
        # 模拟 argparse 解析后的参数
        mock_args = MagicMock()
        mock_args.command = 'init'
        mock_args.env = 'dev'
        mock_args.func = mock_handle_init
        mock_parse_args.return_value = mock_args

        # 导入并运行主函数
        import aqua
        aqua.main()

        # 断言 init 命令的处理函数被正确调用
        mock_handle_init.assert_called_once_with(mock_args)

    @patch('argparse.ArgumentParser.parse_args')
    @patch('aqua.handle_start')
    def test_start_command_dispatch(self, mock_handle_start, mock_parse_args):
        """
        测试 'start' 子命令是否能被正确分发到其处理函数。
        """
        # 模拟 argparse 解析后的参数
        mock_args = MagicMock()
        mock_args.command = 'start'
        mock_args.env = 'test'
        mock_args.frontend = True
        mock_args.backend = True
        mock_args.func = mock_handle_start
        mock_parse_args.return_value = mock_args

        # 导入并运行主函数
        import aqua
        aqua.main()

        # 断言 start 命令的处理函数被正确调用
        mock_handle_start.assert_called_once_with(mock_args)

if __name__ == '__main__':
    unittest.main()
