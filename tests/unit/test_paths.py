#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA路径管理系统单元测试

测试路径解析、占位符处理、跨平台兼容性
版本: 1.0.0
创建时间: 2025-07-31
"""

import os
import platform
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

# 添加src到路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.paths import Paths, ensure_directory, resolve_path


class TestPaths(unittest.TestCase):
    """路径管理类测试"""
    
    def setUp(self):
        """测试前准备"""
        self.original_env = os.environ.get('AQUA_ENV')
        
    def tearDown(self):
        """测试后清理"""
        if self.original_env is not None:
            os.environ['AQUA_ENV'] = self.original_env
        elif 'AQUA_ENV' in os.environ:
            del os.environ['AQUA_ENV']
    
    def test_core_paths_definition(self):
        """测试核心路径定义"""
        # 验证路径是绝对路径
        self.assertTrue(Paths.ROOT.is_absolute())
        self.assertTrue(Paths.SRC.is_absolute())
        self.assertTrue(Paths.CONFIG.is_absolute())
        self.assertTrue(Paths.DATA.is_absolute())
        self.assertTrue(Paths.LOGS.is_absolute())
        self.assertTrue(Paths.CACHE.is_absolute())
        
        # 验证路径关系
        self.assertEqual(Paths.SRC.parent, Paths.ROOT)
        self.assertEqual(Paths.CONFIG.parent, Paths.ROOT)
        self.assertEqual(Paths.DATACENTER.parent, Paths.DATA)
        
    def test_resolve_placeholder_basic(self):
        """测试基本占位符解析"""
        # 测试基础占位符
        result = Paths.resolve_placeholder("{root}/test")
        self.assertTrue(result.endswith("/test"))
        self.assertNotIn("{root}", result)
        
        # 测试logs_root占位符（关键测试）
        result = Paths.resolve_placeholder("{logs_root}/test.log")
        self.assertIn("/logs/test.log", result)
        self.assertNotIn("{logs_root}", result)
        
        # 测试datacenter_dir占位符（关键测试）
        result = Paths.resolve_placeholder("{datacenter_dir}/data.db")
        self.assertIn("/data/datacenter/data.db", result)
        self.assertNotIn("{datacenter_dir}", result)
    
    def test_resolve_placeholder_date_env(self):
        """测试日期和环境占位符"""
        # 设置测试环境
        os.environ['AQUA_ENV'] = 'test'
        
        result = Paths.resolve_placeholder("{logs}/{env}_{date}.log")
        
        # 验证环境变量替换
        self.assertIn("test_", result)
        self.assertNotIn("{env}", result)
        
        # 验证日期格式
        from datetime import datetime
        current_date = datetime.now().strftime("%Y%m%d")
        self.assertIn(current_date, result)
        self.assertNotIn("{date}", result)
    
    def test_resolve_placeholder_multiple(self):
        """测试多个占位符同时处理"""
        os.environ['AQUA_ENV'] = 'dev'
        
        template = "{logs_root}/{env}/app_{date}.log"
        result = Paths.resolve_placeholder(template)
        
        # 验证所有占位符都被替换
        self.assertNotIn("{", result)
        self.assertNotIn("}", result)
        
        # 验证路径合理性
        self.assertIn("/logs/dev/app_", result)
    
    def test_resolve_placeholder_unknown(self):
        """测试未知占位符处理"""
        with patch('builtins.print') as mock_print:
            result = Paths.resolve_placeholder("{unknown_placeholder}/test")
            
            # 验证警告信息被打印
            mock_print.assert_called()
            warning_call = str(mock_print.call_args_list[0])
            self.assertIn("未解析的占位符", warning_call)
            
            # 验证结果包含未解析的占位符
            self.assertIn("{unknown_placeholder}", result)
    
    def test_get_absolute_path(self):
        """测试绝对路径获取"""
        # 测试相对路径转换
        result = Paths.get_absolute_path("config/settings.toml")
        self.assertTrue(result.is_absolute())
        self.assertTrue(str(result).endswith("config/settings.toml"))
        
        # 测试占位符路径转换
        result = Paths.get_absolute_path("{config}/settings.toml")
        self.assertTrue(result.is_absolute())
        self.assertIn("/config/settings.toml", str(result))
        
        # 测试已经是绝对路径的情况
        abs_path = "/tmp/test"
        result = Paths.get_absolute_path(abs_path)
        self.assertEqual(str(result), abs_path)
    
    def test_ensure_dirs(self):
        """测试目录自动创建"""
        # 使用临时目录进行测试
        with tempfile.TemporaryDirectory() as temp_dir:
            # 修改ROOT为临时目录
            with patch.object(Paths, 'ROOT', Path(temp_dir)):
                with patch.object(Paths, 'CONFIG', Path(temp_dir) / "config"):
                    with patch.object(Paths, 'DATA', Path(temp_dir) / "data"):
                        with patch.object(Paths, 'LOGS', Path(temp_dir) / "logs"):
                            with patch.object(Paths, 'CACHE', Path(temp_dir) / "cache"):
                                with patch.object(Paths, 'DATACENTER', Path(temp_dir) / "data" / "datacenter"):
                                    
                                    # 执行目录创建
                                    Paths.ensure_dirs()
                                    
                                    # 验证目录被创建
                                    self.assertTrue((Path(temp_dir) / "config").exists())
                                    self.assertTrue((Path(temp_dir) / "data").exists())
                                    self.assertTrue((Path(temp_dir) / "logs").exists())
                                    self.assertTrue((Path(temp_dir) / "cache").exists())
                                    self.assertTrue((Path(temp_dir) / "data" / "datacenter").exists())
    
    def test_clean_placeholder_dirs(self):
        """测试占位符目录清理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建错误的占位符目录
            error_dir = Path(temp_dir) / "{datacenter_dir}"
            error_dir.mkdir()
            
            logs_error_dir = Path(temp_dir) / "{logs_root}"
            logs_error_dir.mkdir()
            
            # 修改ROOT为临时目录
            with patch.object(Paths, 'ROOT', Path(temp_dir)):
                with patch('builtins.print') as mock_print:
                    Paths.clean_placeholder_dirs()
                    
                    # 验证目录被清理
                    self.assertFalse(error_dir.exists())
                    self.assertFalse(logs_error_dir.exists())
                    
                    # 验证清理信息被打印
                    print_calls = [str(call) for call in mock_print.call_args_list]
                    success_messages = [call for call in print_calls if "已清理" in call]
                    self.assertTrue(len(success_messages) >= 2)
    
    def test_platform_info(self):
        """测试平台信息获取"""
        platform_info = Paths.get_platform_info()
        
        # 验证必要字段存在
        required_fields = [
            'system', 'platform', 'architecture', 'python_version',
            'is_windows', 'is_macos', 'is_linux', 'path_separator', 'path_delimiter'
        ]
        
        for field in required_fields:
            self.assertIn(field, platform_info)
        
        # 验证布尔字段
        boolean_fields = ['is_windows', 'is_macos', 'is_linux']
        for field in boolean_fields:
            self.assertIsInstance(platform_info[field], (bool, str))
        
        # 验证路径分隔符
        self.assertIn(platform_info['path_separator'], ['/', '\\'])
    
    @patch('platform.system')
    def test_venv_paths_windows(self, mock_system):
        """测试Windows虚拟环境路径"""
        mock_system.return_value = "Windows"
        
        # 重新计算路径
        venv_scripts = Paths.ROOT / ".venv" / "Scripts"
        venv_python = venv_scripts / "python.exe"
        
        self.assertTrue(str(venv_scripts).endswith("Scripts"))
        self.assertTrue(str(venv_python).endswith("python.exe"))
    
    @patch('platform.system')
    def test_venv_paths_unix(self, mock_system):
        """测试Unix虚拟环境路径"""
        mock_system.return_value = "Darwin"  # 或 "Linux"
        
        # 重新计算路径
        venv_scripts = Paths.ROOT / ".venv" / "bin"
        venv_python = venv_scripts / "python"
        
        self.assertTrue(str(venv_scripts).endswith("bin"))
        self.assertTrue(str(venv_python).endswith("python"))


class TestUtilityFunctions(unittest.TestCase):
    """工具函数测试"""
    
    def test_ensure_directory(self):
        """测试目录确保函数"""
        with tempfile.TemporaryDirectory() as temp_dir:
            test_path = f"{temp_dir}/test/nested/dir"
            
            # 使用占位符
            with patch.object(Paths, 'ROOT', Path(temp_dir)):
                result = ensure_directory("test/nested/dir")
                
                self.assertTrue(result.exists())
                self.assertTrue(result.is_dir())
                self.assertEqual(result.name, "dir")
    
    def test_resolve_path(self):
        """测试路径解析便捷函数"""
        result = resolve_path("{logs_root}/test.log")
        
        self.assertNotIn("{logs_root}", result)
        self.assertIn("/logs/test.log", result)


class TestPathsIntegration(unittest.TestCase):
    """路径管理集成测试"""
    
    def test_full_workflow(self):
        """测试完整工作流程"""
        # 1. 解析占位符路径
        log_path = resolve_path("{logs_root}/{env}/app_{date}.log")
        
        # 2. 确保目录存在
        log_dir = str(Path(log_path).parent)
        ensure_directory(log_dir)
        
        # 3. 验证路径有效
        self.assertNotIn("{", log_path)
        self.assertTrue(Path(log_path).parent.exists())
    
    def test_placeholder_problem_resolution(self):
        """测试占位符问题解决方案"""
        # 这是解决{datacenter_dir}和{logs_root}问题的关键测试
        
        # 测试datacenter_dir占位符
        datacenter_path = resolve_path("{datacenter_dir}/test.db")
        self.assertNotIn("{datacenter_dir}", datacenter_path)
        self.assertIn("/data/datacenter/test.db", datacenter_path)
        
        # 测试logs_root占位符
        logs_path = resolve_path("{logs_root}/app.log")
        self.assertNotIn("{logs_root}", logs_path)
        self.assertIn("/logs/app.log", logs_path)
        
        # 测试cache_root占位符
        cache_path = resolve_path("{cache_root}/data.cache")
        self.assertNotIn("{cache_root}", cache_path)
        self.assertIn("/cache/data.cache", cache_path)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)