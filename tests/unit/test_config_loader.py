import unittest
from pathlib import Path
from unittest.mock import patch, mock_open
import sys
import platform
import toml

# 将项目根目录添加到 sys.path，以便正确导入 src.aqua.config
project_root = Path(__file__).resolve().parent.parent.parent
if str(project_root / 'src') not in sys.path:
    sys.path.insert(0, str(project_root / 'src'))

from aqua.utils.config_loader import ConfigLoader

class TestConfigLoader(unittest.TestCase):

    def setUp(self):
        self.mock_toml_content = """
        [app]
        name = "AQUA"
        version = "2.0"
        default_environment = "dev"
        environments = ["dev", "prod"]

        [paths]
        data_root = "~/AQUA_Data"
        log_dir = "{data_root}/logs"
        backup_dir = "{data_root}/backups"
        non_string_path = 123

        [dev]
        database_url = "sqlite:///./dev.db"

        [prod]
        database_url = "postgresql://user:password@host:port/prod_db"

        [platform.unix]
        data_root = "/opt/aqua_data"

        [platform.windows]
        data_root = "C:\\AQUA_Data"
        """
        self.mock_config_path = Path("/mock/path/to/settings.toml")

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_init_dev_env(self, mock_toml_load, mock_open_file, mock_exists):
        mock_toml_load.return_value = toml.loads(self.mock_toml_content)
        loader = ConfigLoader(config_path=self.mock_config_path, env='dev')
        self.assertEqual(loader.env, 'dev')
        self.assertEqual(loader.get_value('app.name'), 'AQUA')
        self.assertEqual(loader.get_value('dev.database_url'), 'sqlite:///./dev.db')
        self.assertIsNone(loader.get_value('prod.database_url')) # Should not be present in dev config

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_init_prod_env(self, mock_toml_load, mock_open_file, mock_exists):
        mock_toml_load.return_value = toml.loads(self.mock_toml_content)
        loader = ConfigLoader(config_path=self.mock_config_path, env='prod')
        self.assertEqual(loader.env, 'prod')
        self.assertEqual(loader.get_value('app.name'), 'AQUA')
        self.assertEqual(loader.get_value('prod.database_url'), 'postgresql://user:password@host:port/prod_db')
        self.assertIsNone(loader.get_value('dev.database_url')) # Should not be present in prod config

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_get_value(self, mock_toml_load, mock_open_file, mock_exists):
        mock_toml_load.return_value = toml.loads(self.mock_toml_content)
        loader = ConfigLoader(config_path=self.mock_config_path)
        self.assertEqual(loader.get_value('app.name'), 'AQUA')
        self.assertEqual(loader.get_value('app.version'), '2.0')
        self.assertEqual(loader.get_value('dev.database_url'), 'sqlite:///./dev.db')
        self.assertIsNone(loader.get_value('non_existent_key'))
        self.assertEqual(loader.get_value('non_existent_key', 'default_val'), 'default_val')

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_get_path_unix(self, mock_toml_load, mock_open_file, mock_exists):
        mock_toml_load.return_value = toml.loads(self.mock_toml_content)
        with patch('sys.platform', 'linux'):
            loader = ConfigLoader(config_path=self.mock_config_path)
            path = loader.get_path('paths.data_root')
            self.assertEqual(str(path), '/opt/aqua_data')

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_get_path_windows(self, mock_toml_load, mock_open_file, mock_exists):
        mock_toml_load.return_value = toml.loads(self.mock_toml_content)
        with patch('sys.platform', 'win32'):
            loader = ConfigLoader(config_path=self.mock_config_path)
            path = loader.get_path('paths.data_root')
            self.assertEqual(str(path), 'C:\\AQUA_Data')

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_get_path_with_placeholder(self, mock_toml_load, mock_open_file, mock_exists):
        mock_toml_load.return_value = toml.loads(self.mock_toml_content)
        with patch('sys.platform', 'linux'): # Use linux for consistent path separator
            loader = ConfigLoader(config_path=self.mock_config_path)
            log_dir_path = loader.get_path('paths.log_dir')
            self.assertEqual(str(log_dir_path), '/opt/aqua_data/logs')
            backup_dir_path = loader.get_path('paths.backup_dir')
            self.assertEqual(str(backup_dir_path), '/opt/aqua_data/backups')

    @patch('pathlib.Path.exists', return_value=False) # Simulate file not found
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_file_not_found(self, mock_toml_load, mock_open_file, mock_exists):
        with self.assertRaises(FileNotFoundError):
            ConfigLoader(config_path=self.mock_config_path)

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load', side_effect=toml.TomlDecodeError('Invalid TOML', 0, 0))
    def test_toml_decode_error(self, mock_toml_load, mock_open_file, mock_exists):
        with self.assertRaises(ValueError) as cm:
            ConfigLoader(config_path=self.mock_config_path)
        self.assertIn('配置文件格式错误', str(cm.exception))

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_invalid_environment(self, mock_toml_load, mock_open_file, mock_exists):
        mock_toml_load.return_value = toml.loads(self.mock_toml_content)
        with self.assertRaises(ValueError) as cm:
            ConfigLoader(config_path=self.mock_config_path, env='invalid_env')
        self.assertIn("Environment 'invalid_env' not found in supported environments list.", str(cm.exception))

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_get_value_non_existent_key(self, mock_toml_load, mock_open_file, mock_exists):
        mock_toml_load.return_value = toml.loads(self.mock_toml_content)
        loader = ConfigLoader(config_path=self.mock_config_path)
        self.assertIsNone(loader.get_value('non.existent.key'))
        self.assertEqual(loader.get_value('non.existent.key', 'default_value'), 'default_value')

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_get_path_non_string_value(self, mock_toml_load, mock_open_file, mock_exists):
        # Modify mock_toml_content to have a non-string path value for testing
        modified_content = self.mock_toml_content.replace('data_root = "~/AQUA_Data"', 'data_root = 123')
        mock_toml_load.return_value = toml.loads(modified_content)
        loader = ConfigLoader(config_path=self.mock_config_path)
        with self.assertRaises(TypeError) as cm:
            loader.get_path('paths.non_string_path')
        self.assertIn("Path key 'paths.non_string_path' did not resolve to a string.", str(cm.exception))

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_path_cache(self, mock_toml_load, mock_open_file, mock_exists):
        mock_toml_load.return_value = toml.loads(self.mock_toml_content)
        with patch('sys.platform', 'linux'):
            loader = ConfigLoader(config_path=self.mock_config_path)
            # First call, should calculate and cache
            path1 = loader.get_path('paths.data_root')
            # Second call, should use cache
            path2 = loader.get_path('paths.data_root')
            self.assertEqual(path1, path2)
            # Verify that _resolve_placeholders was called only once for this path
            # This is a bit tricky to mock directly, but we can infer from lru_cache behavior
            # For now, just checking that the paths are the same is sufficient.

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_deep_merge(self, mock_toml_load, mock_open_file, mock_exists):
        base = {'a': 1, 'b': {'c': 2, 'd': 3}}
        new = {'b': {'c': 4, 'e': 5}, 'f': 6}
        expected = {'a': 1, 'b': {'c': 4, 'd': 3, 'e': 5}, 'f': 6}
        loader = ConfigLoader(config_path=self.mock_config_path) # Need an instance to call _deep_merge
        merged = loader._deep_merge(base, new)
        self.assertEqual(merged, expected)

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_resolve_placeholders_nested(self, mock_toml_load, mock_open_file, mock_exists):
        # Add a nested placeholder scenario to mock_toml_content
        nested_toml_content = """
        [app]
        name = "AQUA"
        version = "2.0"
        default_environment = "dev"
        environments = ["dev", "prod"]

        [paths]
        base_dir = "/base"
        sub_dir = "{base_dir}/sub"
        final_dir = "{sub_dir}/final"

        [dev]
        database_url = "sqlite:///./dev.db"
        """
        mock_toml_load.return_value = toml.loads(nested_toml_content)
        with patch('sys.platform', 'linux'):
            loader = ConfigLoader(config_path=self.mock_config_path)
            final_path = loader.get_path('paths.final_dir')
            self.assertEqual(str(final_path), '/base/sub/final')

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_resolve_placeholders_circular_dependency(self, mock_toml_load, mock_open_file, mock_exists):
        circular_toml_content = """
        [paths]
        a = "{b}"
        b = "{a}"
        """
        mock_toml_load.return_value = toml.loads(circular_toml_content)
        with patch('sys.platform', 'linux'):
            loader = ConfigLoader(config_path=self.mock_config_path)
            with self.assertRaises(ValueError) as cm:
                loader.get_path('paths.a')
            self.assertIn("Circular placeholder dependency detected", str(cm.exception))

    @patch('pathlib.Path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open)
    @patch('toml.load')
    def test_resolve_placeholders_max_depth(self, mock_toml_load, mock_open_file, mock_exists):
        deep_toml_content = """
        [paths]
        p0 = "/root"
        p1 = "{p0}/1"
        p2 = "{p1}/2"
        p3 = "{p2}/3"
        p4 = "{p3}/4"
        p5 = "{p4}/5"
        p6 = "{p5}/6"
        """
        mock_toml_load.return_value = toml.loads(deep_toml_content)
        with patch('sys.platform', 'linux'):
            loader = ConfigLoader(config_path=self.mock_config_path)
            # Should resolve up to default max depth (5)
            self.assertEqual(str(loader.get_path('paths.p5')), '/root/1/2/3/4/5')
            with self.assertRaises(ValueError) as cm:
                loader.get_path('paths.p6') # Exceeds max depth
            self.assertIn("Maximum placeholder resolution depth exceeded", str(cm.exception))
