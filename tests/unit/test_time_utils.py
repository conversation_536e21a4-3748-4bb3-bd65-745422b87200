#!/usr/bin/env python3
"""
时间工具单元测试
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
import pytz

from src.utils.time_utils import (
    get_beijing_time_now,
    get_utc_time_now,
    beijing_to_utc,
    utc_to_beijing,
    format_datetime,
    parse_datetime,
    get_date_string,
    get_time_string,
    get_datetime_string,
    get_timestamp_string,
    get_iso_string,
    is_trading_day,
    is_trading_time,
    get_next_trading_day,
    get_previous_trading_day,
    calculate_time_difference,
    format_time_difference,
    normalize_datetime_to_beijing,
    BEIJING_TZ
)


class TestTimeUtils:
    """时间工具测试"""

    def test_get_beijing_time_now(self):
        """测试获取当前北京时间"""
        beijing_time = get_beijing_time_now()
        assert isinstance(beijing_time, datetime)
        assert beijing_time.tzinfo == BEIJING_TZ

    def test_get_utc_time_now(self):
        """测试获取当前UTC时间"""
        utc_time = get_utc_time_now()
        assert isinstance(utc_time, datetime)
        assert utc_time.tzinfo == timezone.utc

    def test_beijing_to_utc_with_timezone(self):
        """测试带时区的北京时间转UTC"""
        beijing_time = BEIJING_TZ.localize(datetime(2025, 1, 1, 12, 0, 0))
        utc_time = beijing_to_utc(beijing_time)
        
        assert utc_time.tzinfo == timezone.utc
        assert utc_time.hour == 4  # 北京时间12点 = UTC 4点

    def test_beijing_to_utc_without_timezone(self):
        """测试不带时区的北京时间转UTC"""
        naive_time = datetime(2025, 1, 1, 12, 0, 0)
        utc_time = beijing_to_utc(naive_time)
        
        assert utc_time.tzinfo == timezone.utc
        assert utc_time.hour == 4  # 北京时间12点 = UTC 4点

    def test_utc_to_beijing_with_timezone(self):
        """测试带时区的UTC时间转北京时间"""
        utc_time = datetime(2025, 1, 1, 4, 0, 0, tzinfo=timezone.utc)
        beijing_time = utc_to_beijing(utc_time)
        
        assert beijing_time.tzinfo == BEIJING_TZ
        assert beijing_time.hour == 12  # UTC 4点 = 北京时间12点

    def test_utc_to_beijing_without_timezone(self):
        """测试不带时区的UTC时间转北京时间"""
        naive_time = datetime(2025, 1, 1, 4, 0, 0)
        beijing_time = utc_to_beijing(naive_time)
        
        assert beijing_time.tzinfo == BEIJING_TZ
        assert beijing_time.hour == 12  # UTC 4点 = 北京时间12点

    def test_format_datetime_default(self):
        """测试默认格式化日期时间"""
        dt = datetime(2025, 1, 1, 12, 30, 45)
        formatted = format_datetime(dt)
        assert formatted == "2025-01-01 12:30:45"

    def test_format_datetime_custom(self):
        """测试自定义格式化日期时间"""
        dt = datetime(2025, 1, 1, 12, 30, 45)
        formatted = format_datetime(dt, "%Y/%m/%d %H:%M")
        assert formatted == "2025/01/01 12:30"

    def test_parse_datetime_default(self):
        """测试默认解析日期时间"""
        parsed = parse_datetime("2025-01-01 12:30:45")
        assert parsed.year == 2025
        assert parsed.month == 1
        assert parsed.day == 1
        assert parsed.hour == 12
        assert parsed.minute == 30
        assert parsed.second == 45
        assert parsed.tzinfo == BEIJING_TZ

    def test_parse_datetime_custom(self):
        """测试自定义解析日期时间"""
        parsed = parse_datetime("2025/01/01 12:30", "%Y/%m/%d %H:%M")
        assert parsed.year == 2025
        assert parsed.month == 1
        assert parsed.day == 1
        assert parsed.hour == 12
        assert parsed.minute == 30
        assert parsed.tzinfo == BEIJING_TZ

    def test_get_date_string_default(self):
        """测试默认日期字符串"""
        with patch('src.utils.time_utils.get_beijing_time_now') as mock_now:
            mock_now.return_value = datetime(2025, 1, 1, 12, 30, 45)
            date_str = get_date_string()
            assert date_str == "2025-01-01"

    def test_get_date_string_custom(self):
        """测试自定义日期字符串"""
        dt = datetime(2025, 1, 1, 12, 30, 45)
        date_str = get_date_string(dt)
        assert date_str == "2025-01-01"

    def test_get_time_string_default(self):
        """测试默认时间字符串"""
        with patch('src.utils.time_utils.get_beijing_time_now') as mock_now:
            mock_now.return_value = datetime(2025, 1, 1, 12, 30, 45)
            time_str = get_time_string()
            assert time_str == "12:30:45"

    def test_get_time_string_custom(self):
        """测试自定义时间字符串"""
        dt = datetime(2025, 1, 1, 12, 30, 45)
        time_str = get_time_string(dt)
        assert time_str == "12:30:45"

    def test_get_datetime_string_default(self):
        """测试默认日期时间字符串"""
        with patch('src.utils.time_utils.get_beijing_time_now') as mock_now:
            mock_now.return_value = datetime(2025, 1, 1, 12, 30, 45)
            datetime_str = get_datetime_string()
            assert datetime_str == "2025-01-01 12:30:45"

    def test_get_datetime_string_custom(self):
        """测试自定义日期时间字符串"""
        dt = datetime(2025, 1, 1, 12, 30, 45)
        datetime_str = get_datetime_string(dt)
        assert datetime_str == "2025-01-01 12:30:45"

    def test_get_timestamp_string_default(self):
        """测试默认时间戳字符串"""
        with patch('src.utils.time_utils.get_beijing_time_now') as mock_now:
            mock_now.return_value = datetime(2025, 1, 1, 12, 30, 45)
            timestamp_str = get_timestamp_string()
            assert timestamp_str == "20250101_123045"

    def test_get_timestamp_string_custom(self):
        """测试自定义时间戳字符串"""
        dt = datetime(2025, 1, 1, 12, 30, 45)
        timestamp_str = get_timestamp_string(dt)
        assert timestamp_str == "20250101_123045"

    def test_get_iso_string_default(self):
        """测试默认ISO字符串"""
        with patch('src.utils.time_utils.get_beijing_time_now') as mock_now:
            mock_time = BEIJING_TZ.localize(datetime(2025, 1, 1, 12, 30, 45))
            mock_now.return_value = mock_time
            iso_str = get_iso_string()
            assert iso_str.startswith("2025-01-01T12:30:45")

    def test_get_iso_string_custom(self):
        """测试自定义ISO字符串"""
        dt = BEIJING_TZ.localize(datetime(2025, 1, 1, 12, 30, 45))
        iso_str = get_iso_string(dt)
        assert iso_str.startswith("2025-01-01T12:30:45")

    def test_is_trading_day_weekday(self):
        """测试工作日是否为交易日"""
        # 2025-01-01是周三
        wednesday = datetime(2025, 1, 1)
        assert is_trading_day(wednesday) is True

    def test_is_trading_day_weekend(self):
        """测试周末是否为交易日"""
        # 2025-01-04是周六
        saturday = datetime(2025, 1, 4)
        assert is_trading_day(saturday) is False
        
        # 2025-01-05是周日
        sunday = datetime(2025, 1, 5)
        assert is_trading_day(sunday) is False

    def test_is_trading_time_trading_day_trading_hours(self):
        """测试交易日交易时间"""
        # 2025-01-01是周三，10点是交易时间
        wednesday_10am = datetime(2025, 1, 1, 10, 0, 0)
        assert is_trading_time(wednesday_10am) is True

    def test_is_trading_time_trading_day_non_trading_hours(self):
        """测试交易日非交易时间"""
        # 2025-01-01是周三，8点不是交易时间
        wednesday_8am = datetime(2025, 1, 1, 8, 0, 0)
        assert is_trading_time(wednesday_8am) is False
        
        # 2025-01-01是周三，16点不是交易时间
        wednesday_4pm = datetime(2025, 1, 1, 16, 0, 0)
        assert is_trading_time(wednesday_4pm) is False

    def test_is_trading_time_weekend(self):
        """测试周末非交易时间"""
        # 2025-01-04是周六，即使在交易时间内也不是交易时间
        saturday_10am = datetime(2025, 1, 4, 10, 0, 0)
        assert is_trading_time(saturday_10am) is False

    def test_get_next_trading_day_weekday(self):
        """测试获取下一个交易日（工作日）"""
        # 2025-01-01是周三，下一个交易日是周四
        wednesday = datetime(2025, 1, 1)
        next_day = get_next_trading_day(wednesday)
        assert next_day.weekday() == 3  # 周四

    def test_get_next_trading_day_friday(self):
        """测试获取下一个交易日（周五）"""
        # 2025-01-03是周五，下一个交易日是下周一
        friday = datetime(2025, 1, 3)
        next_day = get_next_trading_day(friday)
        assert next_day.weekday() == 0  # 周一
        assert next_day.day == 6  # 1月6日

    def test_get_previous_trading_day_weekday(self):
        """测试获取前一个交易日（工作日）"""
        # 2025-01-02是周四，前一个交易日是周三
        thursday = datetime(2025, 1, 2)
        prev_day = get_previous_trading_day(thursday)
        assert prev_day.weekday() == 2  # 周三

    def test_get_previous_trading_day_monday(self):
        """测试获取前一个交易日（周一）"""
        # 2025-01-06是周一，前一个交易日是上周五
        monday = datetime(2025, 1, 6)
        prev_day = get_previous_trading_day(monday)
        assert prev_day.weekday() == 4  # 周五
        assert prev_day.day == 3  # 1月3日

    def test_calculate_time_difference(self):
        """测试计算时间差"""
        start = datetime(2025, 1, 1, 10, 0, 0)
        end = datetime(2025, 1, 1, 12, 30, 45)
        
        diff = calculate_time_difference(start, end)
        
        assert diff["days"] == 0
        assert diff["hours"] == 2
        assert diff["minutes"] == 30
        assert diff["seconds"] == 45
        assert diff["total_seconds"] == 9045.0

    def test_format_time_difference_full(self):
        """测试格式化时间差（完整）"""
        diff = {
            "days": 1,
            "hours": 2,
            "minutes": 30,
            "seconds": 45
        }
        
        formatted = format_time_difference(diff)
        assert formatted == "1天 2小时 30分钟 45秒"

    def test_format_time_difference_partial(self):
        """测试格式化时间差（部分）"""
        diff = {
            "days": 0,
            "hours": 0,
            "minutes": 30,
            "seconds": 0
        }
        
        formatted = format_time_difference(diff)
        assert formatted == "30分钟"

    def test_format_time_difference_seconds_only(self):
        """测试格式化时间差（仅秒）"""
        diff = {
            "days": 0,
            "hours": 0,
            "minutes": 0,
            "seconds": 45
        }
        
        formatted = format_time_difference(diff)
        assert formatted == "45秒"

    def test_normalize_datetime_to_beijing_string(self):
        """测试标准化字符串时间到北京时间"""
        # 测试标准格式
        dt = normalize_datetime_to_beijing("2025-01-01 12:30:45")
        assert dt.year == 2025
        assert dt.month == 1
        assert dt.day == 1
        assert dt.hour == 12
        assert dt.minute == 30
        assert dt.second == 45
        assert dt.tzinfo == BEIJING_TZ

    def test_normalize_datetime_to_beijing_datetime(self):
        """测试标准化datetime对象到北京时间"""
        naive_dt = datetime(2025, 1, 1, 12, 30, 45)
        dt = normalize_datetime_to_beijing(naive_dt)
        assert dt.tzinfo == BEIJING_TZ

    def test_normalize_datetime_to_beijing_utc(self):
        """测试标准化UTC时间到北京时间"""
        utc_dt = datetime(2025, 1, 1, 4, 0, 0, tzinfo=timezone.utc)
        dt = normalize_datetime_to_beijing(utc_dt)
        assert dt.tzinfo == BEIJING_TZ
        assert dt.hour == 12  # UTC 4点 = 北京时间12点

    def test_normalize_datetime_to_beijing_invalid_string(self):
        """测试标准化无效字符串时间"""
        with pytest.raises(ValueError):
            normalize_datetime_to_beijing("invalid date string")

    def test_get_market_session_times(self):
        """测试获取市场交易时段"""
        from src.utils.time_utils import get_market_session_times
        
        sessions = get_market_session_times()
        assert "morning_session" in sessions
        assert "afternoon_session" in sessions
        assert "night_session" in sessions
        
        assert sessions["morning_session"]["start"] == "09:30"
        assert sessions["morning_session"]["end"] == "11:30"
        assert sessions["afternoon_session"]["start"] == "13:00"
        assert sessions["afternoon_session"]["end"] == "15:00"