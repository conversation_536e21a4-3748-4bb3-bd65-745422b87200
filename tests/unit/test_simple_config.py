#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA简化配置管理系统单元测试

测试简化配置系统的核心功能
版本: 1.0.0
创建时间: 2025-07-31
"""

import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

# 添加src到路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.simple_config import (
    SimpleConfig, AppConfig, DatabaseConfig, LoggingConfig, 
    CacheConfig, PerformanceConfig,
    get_config_manager, get_database_path, get_log_file_path,
    get_cache_dir, is_debug_mode, get_environment
)


class TestDatabaseConfig(unittest.TestCase):
    """数据库配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = DatabaseConfig()
        
        self.assertEqual(config.path, "{datacenter_dir}/aqua_{env}.duckdb")
        self.assertTrue(config.auto_create)
        self.assertEqual(config.memory_limit, "2GB")
        self.assertEqual(config.threads, 4)
    
    def test_resolved_path(self):
        """测试路径解析"""
        config = DatabaseConfig()
        resolved_path = config.get_resolved_path()
        
        # 验证占位符被解析
        self.assertNotIn("{datacenter_dir}", resolved_path)
        self.assertNotIn("{env}", resolved_path)
        self.assertIn("/data/datacenter/", resolved_path)
        self.assertTrue(resolved_path.endswith(".duckdb"))


class TestLoggingConfig(unittest.TestCase):
    """日志配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = LoggingConfig()
        
        self.assertEqual(config.level, "INFO")
        self.assertEqual(config.file_path, "{logs_root}/aqua_{env}_{date}.log")
        self.assertEqual(config.max_file_size, "10MB")
        self.assertEqual(config.backup_count, 5)
        self.assertTrue(config.console_output)
    
    def test_resolved_path(self):
        """测试路径解析"""
        config = LoggingConfig()
        resolved_path = config.get_resolved_path()
        
        # 验证占位符被解析
        self.assertNotIn("{logs_root}", resolved_path)
        self.assertNotIn("{env}", resolved_path)
        self.assertNotIn("{date}", resolved_path)
        self.assertIn("/logs/", resolved_path)
        self.assertTrue(resolved_path.endswith(".log"))


class TestCacheConfig(unittest.TestCase):
    """缓存配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = CacheConfig()
        
        self.assertTrue(config.enabled)
        self.assertEqual(config.l1_max_size, 1000)
        self.assertEqual(config.l1_ttl, 1800)
        self.assertEqual(config.l2_cache_dir, "{cache_root}")
        self.assertEqual(config.l2_max_size_mb, 100)
        self.assertEqual(config.default_ttl, 3600)
    
    def test_resolved_cache_dir(self):
        """测试缓存目录解析"""
        config = CacheConfig()
        resolved_dir = config.get_resolved_cache_dir()
        
        # 验证占位符被解析
        self.assertNotIn("{cache_root}", resolved_dir)
        self.assertIn("/cache", resolved_dir)


class TestPerformanceConfig(unittest.TestCase):
    """性能配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = PerformanceConfig()
        
        self.assertEqual(config.memory_limit_mb, 1536)
        self.assertEqual(config.max_workers, 2)
        self.assertEqual(config.chunk_size, 500)
        self.assertEqual(config.startup_target_seconds, 2.0)
        self.assertTrue(config.enable_monitoring)


class TestAppConfig(unittest.TestCase):
    """应用配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = AppConfig()
        
        self.assertEqual(config.name, "AQUA")
        self.assertEqual(config.version, "2.0.0")
        self.assertTrue(config.debug)
        self.assertEqual(config.environment, "dev")
        
        # 验证子配置存在
        self.assertIsInstance(config.database, DatabaseConfig)
        self.assertIsInstance(config.logging, LoggingConfig)
        self.assertIsInstance(config.cache, CacheConfig)
        self.assertIsInstance(config.performance, PerformanceConfig)


class TestSimpleConfig(unittest.TestCase):
    """简化配置管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.original_env_vars = {}
        env_vars_to_save = ["AQUA_ENV", "ENV", "ENVIRONMENT", "CI", "AQUA_LOG_LEVEL", "AQUA_DEBUG"]
        for var in env_vars_to_save:
            self.original_env_vars[var] = os.environ.get(var)
    
    def tearDown(self):
        """测试后清理"""
        for var, value in self.original_env_vars.items():
            if value is not None:
                os.environ[var] = value
            elif var in os.environ:
                del os.environ[var]
    
    def test_environment_detection_explicit(self):
        """测试显式环境指定"""
        config_manager = SimpleConfig("test")
        self.assertEqual(config_manager.environment, "test")
    
    def test_environment_detection_env_var(self):
        """测试环境变量检测"""
        os.environ["AQUA_ENV"] = "prod"
        config_manager = SimpleConfig()
        self.assertEqual(config_manager.environment, "prod")
    
    def test_environment_detection_ci(self):
        """测试CI环境检测"""
        # 清除其他环境变量
        for var in ["AQUA_ENV", "ENV", "ENVIRONMENT"]:
            if var in os.environ:
                del os.environ[var]
        
        os.environ["CI"] = "true"
        config_manager = SimpleConfig()
        self.assertEqual(config_manager.environment, "test")
    
    def test_environment_detection_default(self):
        """测试默认环境检测"""
        # 清除所有环境变量
        for var in ["AQUA_ENV", "ENV", "ENVIRONMENT", "CI"]:
            if var in os.environ:
                del os.environ[var]
        
        config_manager = SimpleConfig()
        self.assertEqual(config_manager.environment, "dev")
    
    def test_dev_environment_config(self):
        """测试开发环境配置"""
        config_manager = SimpleConfig("dev")
        config = config_manager.get_config()
        
        self.assertTrue(config.debug)
        self.assertEqual(config.logging.level, "DEBUG")
        self.assertEqual(config.performance.memory_limit_mb, 1536)
        self.assertTrue(config.performance.enable_monitoring)
    
    def test_test_environment_config(self):
        """测试测试环境配置"""
        config_manager = SimpleConfig("test")
        config = config_manager.get_config()
        
        self.assertFalse(config.debug)
        self.assertEqual(config.logging.level, "INFO")
        self.assertEqual(config.performance.memory_limit_mb, 2048)
        self.assertEqual(config.performance.max_workers, 4)
    
    def test_prod_environment_config(self):
        """测试生产环境配置"""
        config_manager = SimpleConfig("prod")
        config = config_manager.get_config()
        
        self.assertFalse(config.debug)
        self.assertEqual(config.logging.level, "WARNING")
        self.assertFalse(config.logging.console_output)
        self.assertEqual(config.performance.memory_limit_mb, 4096)
        self.assertEqual(config.performance.max_workers, 8)
    
    def test_env_overrides(self):
        """测试环境变量覆盖"""
        os.environ["AQUA_LOG_LEVEL"] = "ERROR"
        os.environ["AQUA_DEBUG"] = "false"
        
        config_manager = SimpleConfig("dev")
        config = config_manager.get_config()
        
        self.assertEqual(config.logging.level, "ERROR")
        self.assertFalse(config.debug)
    
    def test_database_path_resolution(self):
        """测试数据库路径解析"""
        config_manager = SimpleConfig("test")
        db_path = config_manager.get_database_path()
        
        self.assertNotIn("{datacenter_dir}", db_path)
        self.assertNotIn("{env}", db_path)
        self.assertIn("aqua_test.duckdb", db_path)
    
    def test_log_path_resolution(self):
        """测试日志路径解析"""
        config_manager = SimpleConfig("dev")
        log_path = config_manager.get_log_file_path()
        
        self.assertNotIn("{logs_root}", log_path)
        self.assertNotIn("{env}", log_path)
        self.assertNotIn("{date}", log_path)
        self.assertIn("aqua_dev_", log_path)
    
    def test_cache_dir_resolution(self):
        """测试缓存目录解析"""
        config_manager = SimpleConfig("prod")
        cache_dir = config_manager.get_cache_dir()
        
        self.assertNotIn("{cache_root}", cache_dir)
        self.assertIn("/cache", cache_dir)
    
    def test_environment_info(self):
        """测试环境信息获取"""
        config_manager = SimpleConfig("dev")
        env_info = config_manager.get_environment_info()
        
        required_keys = [
            "environment", "debug", "platform", "architecture",
            "database_path", "log_level", "memory_limit_mb",
            "cache_enabled", "startup_target"
        ]
        
        for key in required_keys:
            self.assertIn(key, env_info)
        
        self.assertEqual(env_info["environment"], "dev")
        self.assertTrue(env_info["debug"])
    
    def test_config_validation(self):
        """测试配置验证"""
        config_manager = SimpleConfig("test")
        validation = config_manager.validate_config()
        
        self.assertIn("valid", validation)
        self.assertIn("errors", validation)
        self.assertIn("warnings", validation)
        self.assertIn("info", validation)
        
        self.assertTrue(validation["valid"])
        self.assertIsInstance(validation["errors"], list)
        self.assertIsInstance(validation["warnings"], list)
        self.assertIsInstance(validation["info"], list)
    
    def test_config_caching(self):
        """测试配置缓存"""
        config_manager = SimpleConfig("dev")
        
        # 第一次获取
        config1 = config_manager.get_config()
        
        # 第二次获取应该返回相同对象（缓存）
        config2 = config_manager.get_config()
        
        self.assertIs(config1, config2)
    
    def test_config_reload(self):
        """测试配置重新加载"""
        config_manager = SimpleConfig("dev")
        
        # 获取初始配置
        config1 = config_manager.get_config()
        
        # 重新加载配置
        config_manager.reload_config()
        
        # 获取新配置，应该是不同的对象
        config2 = config_manager.get_config()
        
        self.assertIsNot(config1, config2)


class TestConvenienceFunctions(unittest.TestCase):
    """便捷函数测试"""
    
    def setUp(self):
        """测试前准备"""
        # 重置全局配置管理器
        import utils.simple_config
        utils.simple_config._config_manager = None
    
    def test_get_config_manager_singleton(self):
        """测试配置管理器单例模式"""
        manager1 = get_config_manager("dev")
        manager2 = get_config_manager("test")  # 这个参数应该被忽略
        
        self.assertIs(manager1, manager2)
        self.assertEqual(manager1.environment, "dev")
    
    def test_database_path_function(self):
        """测试数据库路径便捷函数"""
        path = get_database_path()
        
        self.assertIsInstance(path, str)
        self.assertNotIn("{", path)
        self.assertTrue(path.endswith(".duckdb"))
    
    def test_log_file_path_function(self):
        """测试日志文件路径便捷函数"""
        path = get_log_file_path()
        
        self.assertIsInstance(path, str)
        self.assertNotIn("{", path)
        self.assertTrue(path.endswith(".log"))
    
    def test_cache_dir_function(self):
        """测试缓存目录便捷函数"""
        path = get_cache_dir()
        
        self.assertIsInstance(path, str)
        self.assertNotIn("{", path)
        self.assertIn("cache", path)
    
    def test_debug_mode_function(self):
        """测试调试模式便捷函数"""
        is_debug = is_debug_mode()
        
        self.assertIsInstance(is_debug, bool)
    
    def test_environment_function(self):
        """测试环境名称便捷函数"""
        env = get_environment()
        
        self.assertIsInstance(env, str)
        self.assertIn(env, ["dev", "test", "prod"])


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_full_workflow(self):
        """测试完整工作流程"""
        # 1. 创建配置管理器
        config_manager = SimpleConfig("test")
        
        # 2. 验证配置
        validation = config_manager.validate_config()
        self.assertTrue(validation["valid"])
        
        # 3. 获取各种路径
        db_path = config_manager.get_database_path()
        log_path = config_manager.get_log_file_path()
        cache_dir = config_manager.get_cache_dir()
        
        # 4. 验证路径有效
        self.assertNotIn("{", db_path)
        self.assertNotIn("{", log_path)
        self.assertNotIn("{", cache_dir)
        
        # 5. 验证目录存在
        for path in [db_path, log_path, cache_dir]:
            parent_dir = Path(path).parent
            self.assertTrue(parent_dir.exists(), f"目录不存在: {parent_dir}")
    
    def test_cross_environment_consistency(self):
        """测试跨环境一致性"""
        environments = ["dev", "test", "prod"]
        
        for env in environments:
            config_manager = SimpleConfig(env)
            
            # 验证基础功能
            self.assertEqual(config_manager.environment, env)
            self.assertIsInstance(config_manager.get_config(), AppConfig)
            
            # 验证路径解析
            db_path = config_manager.get_database_path()
            log_path = config_manager.get_log_file_path()
            
            self.assertNotIn("{", db_path)
            self.assertNotIn("{", log_path)
            self.assertIn(f"aqua_{env}", db_path)
            self.assertIn(f"aqua_{env}", log_path)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)