#!/usr/bin/env python3
"""
智能报告生成器测试

测试覆盖：
1. 智能报告模板引擎和生成
2. 多格式报告输出处理
3. 定时报告任务调度
4. 复用UnifiedStorageManager和PriorityQueueManager
5. 报告分发和通知功能
"""

import pytest
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import tempfile
import json
import io
from pathlib import Path

# 假设的导入路径 - 基于架构设计  
from src.ai_agent.intelligent_report_generator import IntelligentReportGenerator
from src.ai_agent.ai_strategy import ReportGenerationStrategy
from src.storage.unified_storage_manager import UnifiedStorageManager
from src.storage.priority_queue_manager import PriorityQueueManager, TaskPriority
from src.storage.conflict_resolution_engine import NotificationManager
from src.utils.logger import get_logger


class TestIntelligentReportGenerator:
    """智能报告生成器测试类"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        return {
            "database": {
                "environments": {
                    "test": "test_reports.db"
                },
                "default_environment": "test"
            },
            "report_generation": {
                "template_path": "templates/reports/",
                "output_path": "reports/output/",
                "supported_formats": ["pdf", "html", "excel", "json"],
                "max_data_rows": 10000,
                "cache_enabled": True,
                "parallel_generation": True
            },
            "report_templates": {
                "daily_market_summary": {
                    "template_file": "daily_summary.html",
                    "data_sources": ["stock_daily", "futures_daily"],
                    "refresh_interval_hours": 24
                },
                "risk_analysis": {
                    "template_file": "risk_analysis.html", 
                    "data_sources": ["portfolio_positions", "risk_metrics"],
                    "refresh_interval_hours": 6
                }
            },
            "priority_queue": {
                "max_queue_size": 1000,
                "worker_threads": 2
            }
        }
    
    @pytest.fixture
    def sample_market_data(self):
        """样本市场数据"""
        dates = pd.date_range("20240101", periods=30, freq="D")
        np.random.seed(42)
        
        return pd.DataFrame({
            "trade_date": dates.strftime("%Y%m%d"),
            "ts_code": ["000001.SZ"] * 30,
            "open": np.random.uniform(9.8, 10.2, 30),
            "high": np.random.uniform(10.0, 10.5, 30),
            "low": np.random.uniform(9.5, 10.0, 30),
            "close": np.random.uniform(9.9, 10.3, 30),
            "vol": np.random.uniform(800000, 1200000, 30),
            "pct_chg": np.random.normal(0, 2, 30)
        })
    
    @pytest.fixture
    def sample_portfolio_data(self):
        """样本投资组合数据"""
        return pd.DataFrame({
            "symbol": ["000001.SZ", "000002.SZ", "600000.SH"],
            "position": [1000, 500, 800],
            "avg_cost": [10.0, 25.0, 8.5],
            "current_price": [10.2, 24.8, 8.7],
            "market_value": [10200, 12400, 6960],
            "pnl": [200, -100, 160],
            "weight": [0.35, 0.42, 0.23]
        })
    
    @pytest.fixture
    def report_generator(self, mock_config):
        """创建智能报告生成器实例"""
        with patch('src.storage.unified_storage_manager.UnifiedStorageManager'):
            with patch('src.storage.priority_queue_manager.PriorityQueueManager'):
                return IntelligentReportGenerator(mock_config)
    
    def test_report_generator_initialization(self, mock_config):
        """测试报告生成器初始化"""
        with patch('src.storage.unified_storage_manager.UnifiedStorageManager') as mock_storage:
            with patch('src.storage.priority_queue_manager.PriorityQueueManager') as mock_queue:
                generator = IntelligentReportGenerator(mock_config)
                
                # 验证组件初始化
                mock_storage.assert_called_once_with(mock_config)
                mock_queue.assert_called_once_with(mock_config)
                assert generator.config == mock_config
                assert generator.report_config == mock_config["report_generation"]
                
                # 验证专用组件
                assert hasattr(generator, 'template_engine')
                assert hasattr(generator, 'chart_generator')
                assert hasattr(generator, 'export_manager')
    
    def test_schedule_daily_market_report(self, report_generator):
        """测试调度日度市场报告"""
        # Given: 日度报告配置
        report_config = {
            "report_type": "daily_market_summary",
            "output_format": "pdf",
            "email_recipients": ["<EMAIL>"],
            "schedule_time": "09:00",
            "data_date": "20240115"
        }
        
        # When: 调度报告生成任务
        with patch.object(report_generator.task_manager, 'submit_task') as mock_submit:
            mock_submit.return_value = "task_001"
            
            task_id = report_generator.schedule_report(report_config)
        
        # Then: 验证任务调度
        mock_submit.assert_called_once()
        call_args = mock_submit.call_args
        assert call_args[1]["task_type"] == "generate_report"
        assert call_args[1]["priority"] == TaskPriority.MEDIUM
        assert task_id == "task_001"
    
    def test_generate_daily_market_summary_report(self, report_generator, sample_market_data):
        """测试生成日度市场摘要报告"""
        # Given: 报告配置和数据
        report_config = {
            "report_type": "daily_market_summary",
            "data_date": "20240115",
            "include_charts": True,
            "output_format": "html"
        }
        
        # Mock数据查询
        report_generator.storage_manager.query_data = Mock(return_value=sample_market_data)
        
        # When: 生成报告
        with patch.object(report_generator.template_engine, 'render_template') as mock_render:
            mock_render.return_value = "<html>Daily Market Summary Report</html>"
            
            with patch.object(report_generator.chart_generator, 'generate_charts') as mock_charts:
                mock_charts.return_value = {
                    "price_chart": "data:image/base64,chart_data",
                    "volume_chart": "data:image/base64,volume_data"
                }
                
                result = report_generator.generate_report(report_config)
        
        # Then: 验证报告生成结果
        assert result["success"] is True
        assert result["report_type"] == "daily_market_summary"
        assert result["output_format"] == "html"
        assert "report_content" in result
        assert "charts" in result
        assert result["generation_time"] > 0
    
    def test_generate_risk_analysis_report(self, report_generator, sample_portfolio_data):
        """测试生成风险分析报告"""
        # Given: 风险分析配置
        report_config = {
            "report_type": "risk_analysis",
            "portfolio_id": "portfolio_001",
            "risk_metrics": ["var", "sharpe_ratio", "max_drawdown"],
            "time_horizon": "1M",
            "confidence_level": 0.95
        }
        
        # Mock风险数据查询
        risk_metrics_data = pd.DataFrame({
            "metric": ["var_95", "sharpe_ratio", "max_drawdown"],
            "value": [0.025, 1.8, 0.15],
            "benchmark": [0.030, 1.5, 0.20]
        })
        
        report_generator.storage_manager.query_data = Mock(
            side_effect=[sample_portfolio_data, risk_metrics_data]
        )
        
        # When: 生成风险报告
        with patch.object(report_generator.template_engine, 'render_template') as mock_render:
            mock_render.return_value = "<html>Risk Analysis Report</html>"
            
            with patch.object(report_generator, '_calculate_risk_metrics') as mock_calc:
                mock_calc.return_value = {
                    "portfolio_var": 0.025,
                    "sharpe_ratio": 1.8,
                    "max_drawdown": 0.15,
                    "risk_grade": "B+"
                }
                
                result = report_generator.generate_report(report_config)
        
        # Then: 验证风险报告
        assert result["success"] is True
        assert result["report_type"] == "risk_analysis"
        assert "risk_metrics" in result
        assert result["risk_metrics"]["risk_grade"] == "B+"
    
    def test_generate_multi_format_reports(self, report_generator, sample_market_data):
        """测试生成多格式报告"""
        # Given: 多格式输出配置
        report_config = {
            "report_type": "daily_market_summary",
            "output_formats": ["pdf", "html", "excel"],
            "data_date": "20240115"
        }
        
        report_generator.storage_manager.query_data = Mock(return_value=sample_market_data)
        
        # When: 生成多格式报告
        with patch.object(report_generator.export_manager, 'export_to_pdf') as mock_pdf:
            mock_pdf.return_value = {"file_path": "report.pdf", "size_kb": 250}
            
            with patch.object(report_generator.export_manager, 'export_to_html') as mock_html:
                mock_html.return_value = {"file_path": "report.html", "size_kb": 150}
                
                with patch.object(report_generator.export_manager, 'export_to_excel') as mock_excel:
                    mock_excel.return_value = {"file_path": "report.xlsx", "size_kb": 180}
                    
                    result = report_generator.generate_multi_format_report(report_config)
        
        # Then: 验证多格式输出
        assert result["success"] is True
        assert len(result["output_files"]) == 3
        assert any(f["format"] == "pdf" for f in result["output_files"])
        assert any(f["format"] == "html" for f in result["output_files"])
        assert any(f["format"] == "excel" for f in result["output_files"])
    
    def test_intelligent_chart_generation(self, report_generator, sample_market_data):
        """测试智能图表生成"""
        # Given: 图表配置
        chart_config = {
            "chart_type": "candlestick",
            "data_source": sample_market_data,
            "x_axis": "trade_date",
            "ohlc_columns": ["open", "high", "low", "close"],
            "volume_column": "vol",
            "indicators": ["ma5", "ma20", "rsi"]
        }
        
        # When: 生成智能图表
        with patch.object(report_generator.chart_generator, 'create_candlestick_chart') as mock_chart:
            mock_chart.return_value = {
                "chart_data": "data:image/base64,chart_content",
                "chart_config": chart_config,
                "interactive": True
            }
            
            result = report_generator.generate_intelligent_chart(chart_config)
        
        # Then: 验证图表生成
        assert result["success"] is True
        assert result["chart_type"] == "candlestick"
        assert "chart_data" in result
        assert result["interactive"] is True
    
    def test_report_template_engine(self, report_generator):
        """测试报告模板引擎"""
        # Given: 模板数据
        template_data = {
            "report_title": "Daily Market Summary",
            "report_date": "2024-01-15",
            "market_data": {
                "total_volume": 1000000000,
                "avg_price_change": 1.5,
                "top_gainers": [{"symbol": "000001.SZ", "change": 5.2}]
            }
        }
        
        # When: 渲染模板
        with patch.object(report_generator.template_engine, 'load_template') as mock_load:
            mock_load.return_value = "template_content"
            
            with patch.object(report_generator.template_engine, 'render') as mock_render:
                mock_render.return_value = "rendered_html_content"
                
                result = report_generator.render_report_template(
                    "daily_summary.html", 
                    template_data
                )
        
        # Then: 验证模板渲染
        assert result["success"] is True
        assert result["rendered_content"] == "rendered_html_content"
        assert result["template_name"] == "daily_summary.html"
    
    def test_report_distribution_via_notification(self, report_generator):
        """测试通过通知系统分发报告"""
        # Given: 生成的报告
        report_data = {
            "report_id": "daily_001",
            "report_type": "daily_market_summary",
            "file_path": "reports/daily_summary_20240115.pdf",
            "recipients": ["<EMAIL>", "<EMAIL>"]
        }
        
        # When: 分发报告
        with patch.object(report_generator.notification_manager, 'send_notification') as mock_notify:
            result = report_generator.distribute_report(report_data)
        
        # Then: 验证报告分发
        mock_notify.assert_called_once()
        call_args = mock_notify.call_args[0][0]
        assert call_args["notification_type"] == "report_distribution"
        assert call_args["report_id"] == "daily_001"
        assert len(call_args["recipients"]) == 2
        assert result["success"] is True
    
    def test_scheduled_report_execution(self, report_generator):
        """测试定时报告执行"""
        # Given: 定时报告任务配置
        scheduled_config = {
            "report_type": "daily_market_summary",
            "schedule": "0 9 * * 1-5",  # 工作日9点
            "auto_distribute": True,
            "recipients": ["<EMAIL>"]
        }
        
        # When: 执行定时报告
        with patch.object(report_generator, 'generate_report') as mock_generate:
            mock_generate.return_value = {
                "success": True,
                "report_id": "scheduled_001",
                "file_path": "reports/scheduled_report.pdf"
            }
            
            with patch.object(report_generator, 'distribute_report') as mock_distribute:
                mock_distribute.return_value = {"success": True}
                
                result = report_generator.execute_scheduled_report(scheduled_config)
        
        # Then: 验证定时执行
        assert result["success"] is True
        assert result["report_id"] == "scheduled_001"
        assert result["auto_distributed"] is True
    
    def test_report_caching_mechanism(self, report_generator, sample_market_data):
        """测试报告缓存机制"""
        # Given: 启用缓存的报告配置
        report_config = {
            "report_type": "daily_market_summary",
            "data_date": "20240115",
            "enable_cache": True,
            "cache_ttl_hours": 24
        }
        
        # When: 首次生成报告
        with patch.object(report_generator, '_get_cached_report') as mock_cache_get:
            mock_cache_get.return_value = None  # 缓存未命中
            
            with patch.object(report_generator, '_cache_report') as mock_cache_set:
                with patch.object(report_generator, 'generate_report') as mock_generate:
                    mock_generate.return_value = {
                        "success": True,
                        "report_content": "generated_content"
                    }
                    
                    result1 = report_generator.get_or_generate_report(report_config)
        
        # Then: 验证首次生成
        assert result1["cache_hit"] is False
        mock_cache_set.assert_called_once()
        
        # When: 再次请求相同报告
        with patch.object(report_generator, '_get_cached_report') as mock_cache_get:
            mock_cache_get.return_value = {
                "report_content": "cached_content",
                "cached_at": datetime.now()
            }
            
            result2 = report_generator.get_or_generate_report(report_config)
        
        # Then: 验证缓存命中
        assert result2["cache_hit"] is True
        assert result2["report_content"] == "cached_content"
    
    def test_report_performance_monitoring(self, report_generator):
        """测试报告性能监控"""
        # Given: 启用性能监控
        report_generator.enable_performance_monitoring = True
        
        # When: 生成多个报告并监控性能
        report_configs = [
            {"report_type": "daily_summary", "data_date": "20240115"},
            {"report_type": "risk_analysis", "portfolio_id": "p001"},
            {"report_type": "performance_review", "period": "monthly"}
        ]
        
        for config in report_configs:
            with patch.object(report_generator, 'generate_report') as mock_generate:
                mock_generate.return_value = {"success": True, "generation_time": 2.5}
                report_generator.generate_report(config)
        
        # Then: 验证性能指标收集
        metrics = report_generator.get_performance_metrics()
        assert metrics["total_reports_generated"] == 3
        assert metrics["average_generation_time"] > 0
        assert "report_type_distribution" in metrics
    
    def test_report_error_handling_and_retry(self, report_generator):
        """测试报告错误处理和重试"""
        # Given: 会失败的报告配置
        report_config = {
            "report_type": "daily_market_summary",
            "data_date": "20240115"
        }
        
        # When: 模拟数据查询失败
        report_generator.storage_manager.query_data = Mock(
            side_effect=Exception("数据库连接失败")
        )
        
        with patch.object(report_generator, '_retry_report_generation') as mock_retry:
            mock_retry.return_value = {
                "success": False,
                "error": "数据库连接失败",
                "retry_attempts": 3
            }
            
            result = report_generator.generate_report(report_config)
        
        # Then: 验证错误处理
        assert result["success"] is False
        assert "数据库连接失败" in result["error"]
        assert result["retry_attempts"] == 3
    
    def test_batch_report_generation(self, report_generator):
        """测试批量报告生成"""
        # Given: 批量报告配置
        batch_configs = [
            {"report_type": "daily_summary", "data_date": "20240115"},
            {"report_type": "weekly_review", "week_ending": "20240115"},
            {"report_type": "portfolio_analysis", "portfolio_ids": ["p001", "p002"]}
        ]
        
        # When: 批量生成报告
        with patch.object(report_generator, 'generate_report') as mock_generate:
            mock_generate.return_value = {"success": True, "report_id": "batch_001"}
            
            result = report_generator.generate_batch_reports(
                batch_configs,
                parallel=True,
                max_workers=2
            )
        
        # Then: 验证批量生成
        assert result["success"] is True
        assert result["total_reports"] == 3
        assert result["successful_reports"] == 3
        assert result["execution_mode"] == "parallel"
    
    def test_custom_report_template_creation(self, report_generator):
        """测试自定义报告模板创建"""
        # Given: 自定义模板配置
        custom_template = {
            "template_name": "custom_performance",
            "template_content": "<html>Custom Performance Report: {{data}}</html>",
            "data_sources": ["portfolio_positions", "performance_metrics"],
            "parameters": {
                "portfolio_id": {"type": "string", "required": True},
                "start_date": {"type": "date", "required": True},
                "end_date": {"type": "date", "required": True}
            }
        }
        
        # When: 创建自定义模板
        result = report_generator.create_custom_template(custom_template)
        
        # Then: 验证模板创建
        assert result["success"] is True
        assert result["template_name"] == "custom_performance"
        assert "template_id" in result
    
    def test_report_data_validation(self, report_generator):
        """测试报告数据验证"""
        # Given: 包含异常数据的数据集
        invalid_data = pd.DataFrame({
            "trade_date": ["20240115", "invalid_date", "20240117"],
            "close": [10.0, None, -5.0],  # 包含空值和负价格
            "vol": [1000000, 0, 2000000]  # 包含零成交量
        })
        
        # When: 验证数据质量
        validation_result = report_generator.validate_report_data(
            invalid_data,
            rules={
                "no_null_prices": True,
                "positive_prices": True,
                "valid_dates": True,
                "non_zero_volume": True
            }
        )
        
        # Then: 验证数据质量检查
        assert validation_result["data_valid"] is False
        assert len(validation_result["validation_errors"]) > 0
        assert "null_prices" in validation_result["validation_errors"]
        assert "negative_prices" in validation_result["validation_errors"]


class TestReportGenerationStrategy:
    """报告生成策略测试"""
    
    @pytest.fixture
    def report_strategy(self):
        """创建报告生成策略实例"""
        return ReportGenerationStrategy()
    
    def test_report_strategy_execution(self, report_strategy):
        """测试报告生成策略执行"""
        # Given: 报告配置和上下文
        report_config = {
            "report_type": "daily_summary",
            "output_format": "pdf"
        }
        context = {
            "user_id": "analyst_001",
            "priority": "high",
            "deadline": datetime.now() + timedelta(hours=2)
        }
        
        # When: 执行策略
        with patch.object(report_strategy, '_generate_report') as mock_generate:
            mock_generate.return_value = {
                "success": True,
                "report_id": "strategy_001",
                "file_path": "reports/strategy_report.pdf"
            }
            
            result = report_strategy.execute(report_config, context)
        
        # Then: 验证策略执行结果
        assert result["success"] is True
        assert result["strategy_type"] == "report_generation"
        assert result["report_id"] == "strategy_001"
    
    def test_report_strategy_config_validation(self, report_strategy):
        """测试报告生成策略配置验证"""
        # Given: 有效配置
        valid_config = {
            "template_path": "templates/",
            "output_path": "reports/",
            "supported_formats": ["pdf", "html"]
        }
        
        # When: 验证配置
        result = report_strategy.validate_config(valid_config)
        
        # Then: 验证通过
        assert result is True
        
        # Given: 无效配置
        invalid_config = {
            "template_path": "templates/"
            # 缺少必需的配置项
        }
        
        # When: 验证无效配置
        result = report_strategy.validate_config(invalid_config)
        
        # Then: 验证失败
        assert result is False


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])