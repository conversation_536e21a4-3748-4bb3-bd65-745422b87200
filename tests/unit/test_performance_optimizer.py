#!/usr/bin/env python3
"""
数据库性能优化器单元测试
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from src.database.performance_optimizer import (
    DatabasePerformanceOptimizer,
    IndexInfo,
    IndexType,
    QueryPerformance
)
from src.utils.exceptions import DatabaseException


class TestDatabasePerformanceOptimizer:
    """数据库性能优化器测试类"""
    
    @pytest.fixture
    def temp_config(self):
        """创建临时配置文件"""
        temp_dir = tempfile.mkdtemp()
        config_path = Path(temp_dir) / "settings.toml"
        
        config_content = """
[test]
[test.database]
path = "test.db"
auto_create = true
"""
        
        with open(config_path, "w") as f:
            f.write(config_content)
        
        yield str(config_path)
        
        # 清理
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_optimizer(self, temp_config):
        """创建模拟的优化器实例"""
        with patch('src.database.performance_optimizer.DatabasePerformanceOptimizer.connect') as mock_connect:
            mock_connection = Mock()
            mock_connect.return_value = mock_connection
            
            optimizer = DatabasePerformanceOptimizer(temp_config, "test")
            optimizer._connection = mock_connection
            
            yield optimizer
    
    def test_init_optimizer(self, temp_config):
        """测试优化器初始化"""
        optimizer = DatabasePerformanceOptimizer(temp_config, "test")
        
        assert optimizer.config is not None
        assert optimizer.db_path == "test.db"
        assert len(optimizer.index_configs) > 0
        assert optimizer.performance_logs == []
    
    def test_index_info_creation(self):
        """测试索引信息创建"""
        index_info = IndexInfo(
            table_name="test_table",
            index_name="test_index",
            columns=["col1", "col2"],
            index_type=IndexType.COMPOSITE,
            is_unique=True,
            description="Test index"
        )
        
        assert index_info.table_name == "test_table"
        assert index_info.index_name == "test_index"
        assert index_info.columns == ["col1", "col2"]
        assert index_info.index_type == IndexType.COMPOSITE
        assert index_info.is_unique is True
        assert index_info.description == "Test index"
    
    def test_check_table_exists_success(self, mock_optimizer):
        """测试表存在性检查成功"""
        mock_optimizer._connection.execute.return_value.fetchone.return_value = (1,)
        
        result = mock_optimizer.check_table_exists("test_table")
        
        assert result is True
        mock_optimizer._connection.execute.assert_called_once()
    
    def test_check_table_exists_failure(self, mock_optimizer):
        """测试表存在性检查失败"""
        mock_optimizer._connection.execute.return_value.fetchone.return_value = (0,)
        
        result = mock_optimizer.check_table_exists("non_existent_table")
        
        assert result is False
    
    def test_check_table_exists_exception(self, mock_optimizer):
        """测试表存在性检查异常"""
        mock_optimizer._connection.execute.side_effect = Exception("Database error")
        
        result = mock_optimizer.check_table_exists("test_table")
        
        assert result is False
    
    def test_get_table_info_success(self, mock_optimizer):
        """测试获取表信息成功"""
        # 模拟表结构查询结果
        mock_optimizer._connection.execute.return_value.fetchall.side_effect = [
            [("col1", "VARCHAR"), ("col2", "INTEGER")],  # DESCRIBE结果
            [(100,)],  # COUNT结果
            [(1024,)]  # SIZE结果
        ]
        
        result = mock_optimizer.get_table_info("test_table")
        
        assert result["table_name"] == "test_table"
        assert result["row_count"] == 100
        assert result["exists"] is True
        assert len(result["columns"]) == 2
        assert result["columns"][0]["name"] == "col1"
        assert result["columns"][0]["type"] == "VARCHAR"
    
    def test_get_table_info_exception(self, mock_optimizer):
        """测试获取表信息异常"""
        mock_optimizer._connection.execute.side_effect = Exception("Database error")
        
        result = mock_optimizer.get_table_info("test_table")
        
        assert result["table_name"] == "test_table"
        assert result["exists"] is False
    
    @patch('src.database.performance_optimizer.DatabasePerformanceOptimizer.check_table_exists')
    def test_create_index_success(self, mock_check_table, mock_optimizer):
        """测试创建索引成功"""
        mock_check_table.return_value = True
        mock_optimizer._connection.execute.side_effect = [
            Mock(fetchall=Mock(return_value=[])),  # 检查索引不存在
            Mock()  # 创建索引
        ]
        
        index_info = IndexInfo(
            table_name="test_table",
            index_name="test_index",
            columns=["col1"],
            index_type=IndexType.BTREE
        )
        
        result = mock_optimizer.create_index(index_info)
        
        assert result is True
        assert mock_optimizer._connection.execute.call_count == 2
    
    @patch('src.database.performance_optimizer.DatabasePerformanceOptimizer.check_table_exists')
    def test_create_index_table_not_exists(self, mock_check_table, mock_optimizer):
        """测试创建索引时表不存在"""
        mock_check_table.return_value = False
        
        index_info = IndexInfo(
            table_name="non_existent_table",
            index_name="test_index",
            columns=["col1"],
            index_type=IndexType.BTREE
        )
        
        result = mock_optimizer.create_index(index_info)
        
        assert result is False
    
    @patch('src.database.performance_optimizer.DatabasePerformanceOptimizer.check_table_exists')
    def test_create_index_already_exists(self, mock_check_table, mock_optimizer):
        """测试创建已存在的索引"""
        mock_check_table.return_value = True
        mock_optimizer._connection.execute.return_value.fetchall.return_value = [
            ("test_index",)
        ]
        
        index_info = IndexInfo(
            table_name="test_table",
            index_name="test_index",
            columns=["col1"],
            index_type=IndexType.BTREE
        )
        
        result = mock_optimizer.create_index(index_info)
        
        assert result is True
    
    def test_drop_index_success(self, mock_optimizer):
        """测试删除索引成功"""
        mock_optimizer._connection.execute.return_value = Mock()
        
        result = mock_optimizer.drop_index("test_index")
        
        assert result is True
        mock_optimizer._connection.execute.assert_called_once()
    
    def test_drop_index_exception(self, mock_optimizer):
        """测试删除索引异常"""
        mock_optimizer._connection.execute.side_effect = Exception("Database error")
        
        result = mock_optimizer.drop_index("test_index")
        
        assert result is False
    
    @patch('src.database.performance_optimizer.DatabasePerformanceOptimizer.create_index')
    def test_create_all_indexes(self, mock_create_index, mock_optimizer):
        """测试创建所有索引"""
        mock_create_index.side_effect = [True, False, True]  # 模拟部分成功
        
        # 设置3个索引配置
        mock_optimizer.index_configs = [
            IndexInfo("table1", "index1", ["col1"], IndexType.BTREE),
            IndexInfo("table2", "index2", ["col2"], IndexType.BTREE),
            IndexInfo("table3", "index3", ["col3"], IndexType.BTREE)
        ]
        
        results = mock_optimizer.create_all_indexes()
        
        assert len(results) == 3
        assert results["index1"] is True
        assert results["index2"] is False
        assert results["index3"] is True
        assert mock_create_index.call_count == 3
    
    def test_analyze_query_performance(self, mock_optimizer):
        """测试查询性能分析"""
        # 模拟查询结果
        mock_optimizer._connection.execute.side_effect = [
            Mock(fetchall=Mock(return_value=[("row1",), ("row2",)])),  # 查询结果
            Mock(fetchall=Mock(return_value=[("Index Scan",)]))  # EXPLAIN结果
        ]
        
        with patch('time.time', side_effect=[1000, 1001]):  # 模拟时间
            performance = mock_optimizer.analyze_query_performance("SELECT * FROM test")
        
        assert performance.query == "SELECT * FROM test"
        assert performance.execution_time == 1
        assert performance.rows_returned == 2
        assert performance.index_used is True
        assert isinstance(performance.timestamp, datetime)
    
    def test_analyze_query_performance_exception(self, mock_optimizer):
        """测试查询性能分析异常"""
        mock_optimizer._connection.execute.side_effect = Exception("Database error")
        
        performance = mock_optimizer.analyze_query_performance("SELECT * FROM test")
        
        assert performance.query == "SELECT * FROM test"
        assert performance.execution_time == 0
        assert performance.rows_returned == 0
        assert performance.index_used is False
    
    @patch('src.database.performance_optimizer.DatabasePerformanceOptimizer.analyze_query_performance')
    def test_benchmark_queries(self, mock_analyze, mock_optimizer):
        """测试基准查询"""
        mock_performance = QueryPerformance(
            query="SELECT * FROM test",
            execution_time=0.1,
            rows_returned=10,
            rows_examined=10,
            index_used=True,
            timestamp=datetime.now()
        )
        mock_analyze.return_value = mock_performance
        
        results = mock_optimizer.benchmark_queries()
        
        assert len(results) >= 1
        assert mock_analyze.call_count >= 1
        assert all(isinstance(r, QueryPerformance) for r in results)
    
    def test_get_database_statistics_success(self, mock_optimizer):
        """测试获取数据库统计信息成功"""
        # 模拟查询结果
        mock_optimizer._connection.execute.side_effect = [
            Mock(fetchall=Mock(return_value=[("table1",), ("table2",)])),  # 表列表
            Mock(fetchall=Mock(return_value=[("index1", "table1", "col1")]))  # 索引列表
        ]
        
        # 模拟get_table_info方法
        with patch.object(mock_optimizer, 'get_table_info') as mock_get_table_info:
            mock_get_table_info.return_value = {
                "table_name": "table1",
                "row_count": 100,
                "exists": True
            }
            
            stats = mock_optimizer.get_database_statistics()
        
        assert "database_path" in stats
        assert "tables" in stats
        assert "indexes" in stats
        assert "total_tables" in stats
        assert "total_indexes" in stats
        assert "timestamp" in stats
    
    def test_get_database_statistics_exception(self, mock_optimizer):
        """测试获取数据库统计信息异常"""
        mock_optimizer._connection.execute.side_effect = Exception("Database error")
        
        stats = mock_optimizer.get_database_statistics()
        
        assert "error" in stats
    
    @patch('src.database.performance_optimizer.DatabasePerformanceOptimizer.create_all_indexes')
    @patch('src.database.performance_optimizer.DatabasePerformanceOptimizer.benchmark_queries')
    @patch('src.database.performance_optimizer.DatabasePerformanceOptimizer.get_database_statistics')
    @patch('src.database.performance_optimizer.DatabasePerformanceOptimizer.run_maintenance')
    def test_optimize_database(self, mock_maintenance, mock_stats, mock_benchmark, mock_create_indexes, mock_optimizer):
        """测试数据库优化"""
        # 模拟各个方法的返回值
        mock_create_indexes.return_value = {"index1": True, "index2": False}
        mock_benchmark.return_value = [
            QueryPerformance("SELECT 1", 0.1, 1, 1, True, datetime.now())
        ]
        mock_stats.return_value = {"total_tables": 5, "total_indexes": 3}
        mock_maintenance.return_value = {"analyze": True}
        
        results = mock_optimizer.optimize_database()
        
        assert "start_time" in results
        assert "end_time" in results
        assert "total_time" in results
        assert "steps" in results
        assert len(results["steps"]) == 4
        
        # 验证各步骤
        step_names = [step["step"] for step in results["steps"]]
        assert "create_indexes" in step_names
        assert "benchmark_queries" in step_names
        assert "collect_statistics" in step_names
        assert "run_maintenance" in step_names
    
    def test_run_maintenance_success(self, mock_optimizer):
        """测试运行维护成功"""
        mock_optimizer._connection.execute.side_effect = [
            Mock(),  # ANALYZE
            Mock(fetchall=Mock(return_value=[("ok",)]))  # PRAGMA integrity_check
        ]
        
        results = mock_optimizer.run_maintenance()
        
        assert results["analyze"] is True
        assert "integrity_check" in results
    
    def test_run_maintenance_exception(self, mock_optimizer):
        """测试运行维护异常"""
        mock_optimizer._connection.execute.side_effect = Exception("Database error")
        
        results = mock_optimizer.run_maintenance()
        
        assert "error" in results
    
    def test_print_optimization_report(self, mock_optimizer, capsys):
        """测试打印优化报告"""
        # 准备测试数据
        results = {
            "start_time": datetime.now(),
            "end_time": datetime.now(),
            "total_time": 10.5,
            "steps": [
                {
                    "step": "create_indexes",
                    "results": {"index1": True, "index2": False}
                },
                {
                    "step": "benchmark_queries",
                    "results": [
                        QueryPerformance("SELECT 1", 0.1, 1, 1, True, datetime.now())
                    ],
                    "average_time": 0.1
                },
                {
                    "step": "collect_statistics",
                    "results": {"total_tables": 5, "total_indexes": 3, "tables": [
                        {"table_name": "table1", "row_count": 100}
                    ]}
                }
            ]
        }
        
        mock_optimizer.print_optimization_report(results)
        
        captured = capsys.readouterr()
        assert "数据库性能优化报告" in captured.out
        assert "总耗时: 10.50秒" in captured.out
        assert "索引创建结果" in captured.out
        assert "查询性能测试" in captured.out
        assert "数据库统计" in captured.out


@pytest.mark.parametrize("index_type,expected_sql", [
    (IndexType.BTREE, "CREATE INDEX test_index ON test_table (col1)"),
    (IndexType.HASH, "CREATE INDEX test_index ON test_table (col1)"),
    (IndexType.COMPOSITE, "CREATE INDEX test_index ON test_table (col1, col2)")
])
def test_index_creation_sql(index_type, expected_sql):
    """测试不同索引类型的SQL生成"""
    columns = ["col1"] if index_type != IndexType.COMPOSITE else ["col1", "col2"]
    
    index_info = IndexInfo(
        table_name="test_table",
        index_name="test_index",
        columns=columns,
        index_type=index_type
    )
    
    # 这里只是验证IndexInfo对象的属性
    assert index_info.table_name == "test_table"
    assert index_info.index_name == "test_index"
    assert index_info.columns == columns
    assert index_info.index_type == index_type


class TestQueryPerformance:
    """查询性能测试类"""
    
    def test_query_performance_creation(self):
        """测试查询性能对象创建"""
        timestamp = datetime.now()
        performance = QueryPerformance(
            query="SELECT * FROM test",
            execution_time=0.5,
            rows_returned=100,
            rows_examined=200,
            index_used=True,
            timestamp=timestamp
        )
        
        assert performance.query == "SELECT * FROM test"
        assert performance.execution_time == 0.5
        assert performance.rows_returned == 100
        assert performance.rows_examined == 200
        assert performance.index_used is True
        assert performance.timestamp == timestamp


if __name__ == "__main__":
    pytest.main([__file__, "-v"])