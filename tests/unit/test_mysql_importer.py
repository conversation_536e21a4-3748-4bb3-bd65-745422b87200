#!/usr/bin/env python3
"""
MySQL导入器单元测试
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import pymysql

from src.data_import.mysql_importer import MySQLImporter


class TestMySQLImporter:
    """MySQL导入器测试"""

    def setup_method(self):
        """测试前设置"""
        self.mock_config = {
            'mysql': {
                'host': 'localhost',
                'port': 3306,
                'user': 'test_user',
                'password': 'test_password',
                'database': 'test_db'
            }
        }
        self.mock_connection_manager = Mock()
        self.importer = MySQLImporter(self.mock_config, self.mock_connection_manager)

    def test_init(self):
        """测试初始化"""
        assert self.importer.config == self.mock_config
        assert self.importer.connection_manager == self.mock_connection_manager
        assert self.importer.mysql_config == self.mock_config['mysql']

    @patch('pymysql.connect')
    def test_connect_mysql_success(self, mock_connect):
        """测试成功连接MySQL"""
        mock_connection = Mock()
        mock_connect.return_value = mock_connection
        
        result = self.importer.connect_mysql()
        
        assert result is True
        assert self.importer.mysql_connection == mock_connection
        mock_connect.assert_called_once_with(
            host='localhost',
            port=3306,
            user='test_user',
            password='test_password',
            database='test_db',
            charset='utf8mb4'
        )

    @patch('pymysql.connect')
    def test_connect_mysql_failure(self, mock_connect):
        """测试MySQL连接失败"""
        mock_connect.side_effect = pymysql.Error("Connection failed")
        
        result = self.importer.connect_mysql()
        
        assert result is False
        assert self.importer.mysql_connection is None

    def test_disconnect_mysql_with_connection(self):
        """测试断开MySQL连接（有连接）"""
        mock_connection = Mock()
        self.importer.mysql_connection = mock_connection
        
        self.importer.disconnect_mysql()
        
        mock_connection.close.assert_called_once()
        assert self.importer.mysql_connection is None

    def test_disconnect_mysql_without_connection(self):
        """测试断开MySQL连接（无连接）"""
        self.importer.mysql_connection = None
        
        # 不应该抛出异常
        self.importer.disconnect_mysql()

    def test_get_mysql_tables(self):
        """测试获取MySQL表列表"""
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchall.return_value = [
            ('table1',),
            ('table2',),
            ('table3',)
        ]
        
        self.importer.mysql_connection = mock_connection
        
        tables = self.importer.get_mysql_tables()
        
        assert tables == ['table1', 'table2', 'table3']
        mock_cursor.execute.assert_called_once_with("SHOW TABLES")

    def test_get_mysql_tables_no_connection(self):
        """测试无连接时获取MySQL表列表"""
        self.importer.mysql_connection = None
        
        tables = self.importer.get_mysql_tables()
        
        assert tables == []

    def test_get_table_schema(self):
        """测试获取表结构"""
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchall.return_value = [
            ('id', 'int', 'NO', 'PRI', None, 'auto_increment'),
            ('name', 'varchar(100)', 'NO', '', None, ''),
            ('age', 'int', 'YES', '', None, '')
        ]
        
        self.importer.mysql_connection = mock_connection
        
        schema = self.importer.get_table_schema('test_table')
        
        assert len(schema) == 3
        assert schema[0]['name'] == 'id'
        assert schema[0]['type'] == 'int'
        assert schema[0]['nullable'] == 'NO'
        assert schema[0]['primary_key'] == 'PRI'
        mock_cursor.execute.assert_called_once_with("DESCRIBE test_table")

    def test_get_table_schema_no_connection(self):
        """测试无连接时获取表结构"""
        self.importer.mysql_connection = None
        
        schema = self.importer.get_table_schema('test_table')
        
        assert schema == []

    def test_convert_mysql_type_to_duckdb_integer(self):
        """测试MySQL类型转换为DuckDB类型（整数）"""
        result = self.importer.convert_mysql_type_to_duckdb('int')
        assert result == 'INTEGER'
        
        result = self.importer.convert_mysql_type_to_duckdb('bigint')
        assert result == 'BIGINT'

    def test_convert_mysql_type_to_duckdb_string(self):
        """测试MySQL类型转换为DuckDB类型（字符串）"""
        result = self.importer.convert_mysql_type_to_duckdb('varchar(100)')
        assert result == 'VARCHAR(100)'
        
        result = self.importer.convert_mysql_type_to_duckdb('text')
        assert result == 'TEXT'

    def test_convert_mysql_type_to_duckdb_float(self):
        """测试MySQL类型转换为DuckDB类型（浮点数）"""
        result = self.importer.convert_mysql_type_to_duckdb('float')
        assert result == 'FLOAT'
        
        result = self.importer.convert_mysql_type_to_duckdb('double')
        assert result == 'DOUBLE'

    def test_convert_mysql_type_to_duckdb_datetime(self):
        """测试MySQL类型转换为DuckDB类型（日期时间）"""
        result = self.importer.convert_mysql_type_to_duckdb('datetime')
        assert result == 'TIMESTAMP'
        
        result = self.importer.convert_mysql_type_to_duckdb('date')
        assert result == 'DATE'

    def test_convert_mysql_type_to_duckdb_default(self):
        """测试MySQL类型转换为DuckDB类型（默认）"""
        result = self.importer.convert_mysql_type_to_duckdb('unknown_type')
        assert result == 'VARCHAR(255)'

    def test_create_duckdb_table_from_mysql_schema(self):
        """测试从MySQL模式创建DuckDB表"""
        mysql_schema = [
            {'name': 'id', 'type': 'int', 'nullable': 'NO', 'primary_key': 'PRI'},
            {'name': 'name', 'type': 'varchar(100)', 'nullable': 'NO', 'primary_key': ''},
            {'name': 'age', 'type': 'int', 'nullable': 'YES', 'primary_key': ''}
        ]
        
        self.mock_connection_manager.create_table.return_value = True
        
        result = self.importer.create_duckdb_table_from_mysql_schema(
            'test_table', mysql_schema
        )
        
        assert result is True
        self.mock_connection_manager.create_table.assert_called_once()

    def test_create_duckdb_table_from_mysql_schema_failure(self):
        """测试从MySQL模式创建DuckDB表失败"""
        mysql_schema = [
            {'name': 'id', 'type': 'int', 'nullable': 'NO', 'primary_key': 'PRI'}
        ]
        
        self.mock_connection_manager.create_table.return_value = False
        
        result = self.importer.create_duckdb_table_from_mysql_schema(
            'test_table', mysql_schema
        )
        
        assert result is False

    def test_get_table_data_with_limit(self):
        """测试获取表数据（带限制）"""
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchall.return_value = [
            (1, 'Alice', 25),
            (2, 'Bob', 30)
        ]
        
        self.importer.mysql_connection = mock_connection
        
        data = self.importer.get_table_data('test_table', limit=10)
        
        assert len(data) == 2
        assert data[0] == (1, 'Alice', 25)
        mock_cursor.execute.assert_called_once_with("SELECT * FROM test_table LIMIT 10")

    def test_get_table_data_without_limit(self):
        """测试获取表数据（不带限制）"""
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchall.return_value = [(1, 'Alice', 25)]
        
        self.importer.mysql_connection = mock_connection
        
        data = self.importer.get_table_data('test_table')
        
        assert len(data) == 1
        mock_cursor.execute.assert_called_once_with("SELECT * FROM test_table")

    def test_get_table_data_no_connection(self):
        """测试无连接时获取表数据"""
        self.importer.mysql_connection = None
        
        data = self.importer.get_table_data('test_table')
        
        assert data == []

    def test_import_table_success(self):
        """测试成功导入表"""
        # 模拟MySQL连接和数据
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchall.side_effect = [
            [  # 表结构
                ('id', 'int', 'NO', 'PRI', None, 'auto_increment'),
                ('name', 'varchar(100)', 'NO', '', None, '')
            ],
            [  # 表数据
                (1, 'Alice'),
                (2, 'Bob')
            ]
        ]
        
        self.importer.mysql_connection = mock_connection
        self.mock_connection_manager.create_table.return_value = True
        self.mock_connection_manager.insert_data.return_value = True
        
        result = self.importer.import_table('test_table')
        
        assert result['success'] is True
        assert result['table_name'] == 'test_table'
        assert result['rows_imported'] == 2
        assert result['duckdb_table_name'] == 'test_table'

    def test_import_table_no_connection(self):
        """测试无连接时导入表"""
        self.importer.mysql_connection = None
        
        result = self.importer.import_table('test_table')
        
        assert result['success'] is False
        assert 'MySQL连接不可用' in result['error']

    def test_import_table_create_failure(self):
        """测试创建表失败时导入表"""
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchall.return_value = [
            ('id', 'int', 'NO', 'PRI', None, 'auto_increment')
        ]
        
        self.importer.mysql_connection = mock_connection
        self.mock_connection_manager.create_table.return_value = False
        
        result = self.importer.import_table('test_table')
        
        assert result['success'] is False
        assert '创建DuckDB表失败' in result['error']

    def test_import_all_tables_success(self):
        """测试成功导入所有表"""
        # 模拟MySQL连接和数据
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchall.side_effect = [
            [('table1',), ('table2',)],  # 表列表
            [('id', 'int', 'NO', 'PRI', None, 'auto_increment')],  # table1结构
            [(1,), (2,)],  # table1数据
            [('name', 'varchar(100)', 'NO', '', None, '')],  # table2结构
            [('Alice',), ('Bob',)]  # table2数据
        ]
        
        self.importer.mysql_connection = mock_connection
        self.mock_connection_manager.create_table.return_value = True
        self.mock_connection_manager.insert_data.return_value = True
        
        result = self.importer.import_all_tables()
        
        assert result['success'] is True
        assert result['total_tables'] == 2
        assert result['successful_imports'] == 2
        assert result['failed_imports'] == 0

    def test_import_all_tables_no_connection(self):
        """测试无连接时导入所有表"""
        self.importer.mysql_connection = None
        
        result = self.importer.import_all_tables()
        
        assert result['success'] is False
        assert 'MySQL连接不可用' in result['error']

    def test_import_all_tables_no_tables(self):
        """测试无表时导入所有表"""
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchall.return_value = []
        
        self.importer.mysql_connection = mock_connection
        
        result = self.importer.import_all_tables()
        
        assert result['success'] is False
        assert '未找到MySQL表' in result['error']

    def test_import_specific_tables_success(self):
        """测试成功导入指定表"""
        # 模拟MySQL连接和数据
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchall.side_effect = [
            [('id', 'int', 'NO', 'PRI', None, 'auto_increment')],  # table1结构
            [(1,), (2,)],  # table1数据
            [('name', 'varchar(100)', 'NO', '', None, '')],  # table2结构
            [('Alice',), ('Bob',)]  # table2数据
        ]
        
        self.importer.mysql_connection = mock_connection
        self.mock_connection_manager.create_table.return_value = True
        self.mock_connection_manager.insert_data.return_value = True
        
        result = self.importer.import_specific_tables(['table1', 'table2'])
        
        assert result['success'] is True
        assert result['total_tables'] == 2
        assert result['successful_imports'] == 2

    def test_import_specific_tables_partial_success(self):
        """测试部分成功导入指定表"""
        # 模拟MySQL连接和数据
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchall.side_effect = [
            [('id', 'int', 'NO', 'PRI', None, 'auto_increment')],  # table1结构
            [(1,), (2,)],  # table1数据
            Exception("Table not found")  # table2结构查询失败
        ]
        
        self.importer.mysql_connection = mock_connection
        self.mock_connection_manager.create_table.return_value = True
        self.mock_connection_manager.insert_data.return_value = True
        
        result = self.importer.import_specific_tables(['table1', 'table2'])
        
        assert result['success'] is True
        assert result['total_tables'] == 2
        assert result['successful_imports'] == 1
        assert result['failed_imports'] == 1

    def test_get_import_summary(self):
        """测试获取导入摘要"""
        # 模拟一些导入结果
        self.importer.import_results = [
            {'success': True, 'table_name': 'table1', 'rows_imported': 100},
            {'success': True, 'table_name': 'table2', 'rows_imported': 200},
            {'success': False, 'error': 'Some error'}
        ]
        
        summary = self.importer.get_import_summary()
        
        assert summary['total_tables'] == 3
        assert summary['successful_imports'] == 2
        assert summary['failed_imports'] == 1
        assert summary['total_rows'] == 300
        assert len(summary['imported_tables']) == 2

    def test_clear_import_results(self):
        """测试清除导入结果"""
        self.importer.import_results = [{'some': 'data'}]
        self.importer.clear_import_results()
        assert len(self.importer.import_results) == 0

    def test_test_connection_success(self):
        """测试连接测试成功"""
        with patch.object(self.importer, 'connect_mysql', return_value=True):
            with patch.object(self.importer, 'disconnect_mysql'):
                result = self.importer.test_connection()
                assert result is True

    def test_test_connection_failure(self):
        """测试连接测试失败"""
        with patch.object(self.importer, 'connect_mysql', return_value=False):
            result = self.importer.test_connection()
            assert result is False

    def test_get_connection_info(self):
        """测试获取连接信息"""
        info = self.importer.get_connection_info()
        
        assert info['host'] == 'localhost'
        assert info['port'] == 3306
        assert info['user'] == 'test_user'
        assert info['database'] == 'test_db'
        assert 'password' not in info  # 密码不应该出现在连接信息中

    def test_estimate_import_time(self):
        """测试估算导入时间"""
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_cursor.fetchone.return_value = (1000,)  # 行数
        
        self.importer.mysql_connection = mock_connection
        
        estimated_time = self.importer.estimate_import_time('test_table')
        
        assert estimated_time > 0
        mock_cursor.execute.assert_called_once_with("SELECT COUNT(*) FROM test_table")

    def test_estimate_import_time_no_connection(self):
        """测试无连接时估算导入时间"""
        self.importer.mysql_connection = None
        
        estimated_time = self.importer.estimate_import_time('test_table')
        
        assert estimated_time == 0