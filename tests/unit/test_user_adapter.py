#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA用户适配器单元测试

测试用户级别适配功能
版本: 1.0.0
创建时间: 2025-08-01
"""

import unittest
from unittest.mock import patch, MagicMock, mock_open
from pathlib import Path
import sys
import json
import tempfile
import shutil
from datetime import datetime

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.user_adapter import (
    UserLevel, AdaptationMode, UserProfile, AdaptationConfig, UserAdapter
)


class TestEnums(unittest.TestCase):
    """枚举类测试"""
    
    def test_user_level_values(self):
        """测试用户级别枚举值"""
        self.assertEqual(UserLevel.BEGINNER.value, "beginner")
        self.assertEqual(UserLevel.INTERMEDIATE.value, "intermediate")
        self.assertEqual(UserLevel.ADVANCED.value, "advanced")
        self.assertEqual(UserLevel.EXPERT.value, "expert")
    
    def test_adaptation_mode_values(self):
        """测试适配模式枚举值"""
        self.assertEqual(AdaptationMode.SIMPLE.value, "simple")
        self.assertEqual(AdaptationMode.STANDARD.value, "standard")
        self.assertEqual(AdaptationMode.ADVANCED.value, "advanced")
        self.assertEqual(AdaptationMode.CUSTOM.value, "custom")


class TestUserProfile(unittest.TestCase):
    """用户档案数据类测试"""
    
    def test_profile_creation(self):
        """测试用户档案创建"""
        profile = UserProfile(user_id="test_user")
        
        self.assertEqual(profile.user_id, "test_user")
        self.assertEqual(profile.level, UserLevel.BEGINNER)
        self.assertEqual(profile.mode, AdaptationMode.SIMPLE)
        self.assertEqual(profile.login_count, 0)
        self.assertEqual(profile.command_count, 0)
        self.assertIsInstance(profile.preferences, dict)
        self.assertIsInstance(profile.skills, dict)
        
        # 检查默认技能
        expected_skills = ["python", "data_analysis", "quantitative", "cli_usage", "configuration"]
        for skill in expected_skills:
            self.assertIn(skill, profile.skills)
            self.assertEqual(profile.skills[skill], 0.0)
    
    def test_profile_with_custom_values(self):
        """测试自定义值的用户档案"""
        custom_skills = {"python": 0.8, "data_analysis": 0.6}
        profile = UserProfile(
            user_id="advanced_user",
            level=UserLevel.ADVANCED,
            command_count=100,
            success_count=95,
            skills=custom_skills
        )
        
        self.assertEqual(profile.level, UserLevel.ADVANCED)
        self.assertEqual(profile.command_count, 100)
        self.assertEqual(profile.success_count, 95)
        self.assertEqual(profile.skills["python"], 0.8)


class TestAdaptationConfig(unittest.TestCase):
    """适配配置数据类测试"""
    
    def test_config_defaults(self):
        """测试配置默认值"""
        config = AdaptationConfig()
        
        self.assertFalse(config.show_advanced_options)
        self.assertFalse(config.show_debug_info)
        self.assertTrue(config.use_colors)
        self.assertTrue(config.enable_auto_complete)
        self.assertEqual(config.max_concurrent_operations, 1)
        self.assertTrue(config.show_tooltips)
    
    def test_config_custom_values(self):
        """测试自定义配置值"""
        config = AdaptationConfig(
            show_advanced_options=True,
            max_concurrent_operations=4,
            show_tooltips=False
        )
        
        self.assertTrue(config.show_advanced_options)
        self.assertEqual(config.max_concurrent_operations, 4)
        self.assertFalse(config.show_tooltips)


class TestUserAdapter(unittest.TestCase):
    """用户适配器测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
        
        # 模拟依赖
        self.mock_config = MagicMock()
        self.mock_ui = MagicMock()
        self.mock_paths = MagicMock()
        
        # 设置路径
        self.mock_paths.ROOT = Path(self.temp_dir)
        
        with patch('utils.user_adapter.get_config_manager', return_value=self.mock_config), \
             patch('utils.user_adapter.get_ui', return_value=self.mock_ui), \
             patch('utils.user_adapter.Paths', return_value=self.mock_paths):
            self.adapter = UserAdapter()
    
    def test_adapter_initialization(self):
        """测试适配器初始化"""
        self.assertIsNotNone(self.adapter.config)
        self.assertIsNotNone(self.adapter.ui)
        self.assertIsNotNone(self.adapter.paths)
        self.assertIsNone(self.adapter.current_profile)
        
        # 检查适配配置
        self.assertIn(UserLevel.BEGINNER, self.adapter.adaptation_configs)
        self.assertIn(UserLevel.EXPERT, self.adapter.adaptation_configs)
        
        # 检查初学者配置
        beginner_config = self.adapter.adaptation_configs[UserLevel.BEGINNER]
        self.assertFalse(beginner_config.show_advanced_options)
        self.assertTrue(beginner_config.show_tooltips)
        self.assertEqual(beginner_config.max_concurrent_operations, 1)
        
        # 检查专家配置
        expert_config = self.adapter.adaptation_configs[UserLevel.EXPERT]
        self.assertTrue(expert_config.show_advanced_options)
        self.assertFalse(expert_config.show_tooltips)
        self.assertEqual(expert_config.max_concurrent_operations, 8)
    
    @patch('getpass.getuser')
    def test_get_default_user_id(self, mock_getuser):
        """测试获取默认用户ID"""
        mock_getuser.return_value = "testuser"
        
        user_id = self.adapter._get_default_user_id()
        self.assertEqual(user_id, "testuser")
    
    @patch('getpass.getuser')
    def test_get_default_user_id_fallback(self, mock_getuser):
        """测试获取默认用户ID回退"""
        mock_getuser.side_effect = Exception("No user")
        
        user_id = self.adapter._get_default_user_id()
        self.assertEqual(user_id, "default_user")
    
    def test_load_user_profile_new_user(self):
        """测试加载新用户档案"""
        profile = self.adapter.load_user_profile("test_user")
        
        self.assertEqual(profile.user_id, "test_user")
        self.assertEqual(profile.level, UserLevel.BEGINNER)
        self.assertEqual(profile.command_count, 0)
        self.assertEqual(self.adapter.current_profile, profile)
    
    def test_load_user_profile_existing_user(self):
        """测试加载现有用户档案"""
        # 创建用户数据目录
        user_dir = Path(self.temp_dir) / ".aqua" / "user"
        user_dir.mkdir(parents=True, exist_ok=True)
        profile_file = user_dir / "profile.json"
        
        # 创建现有档案数据
        existing_data = {
            "user_id": "existing_user",
            "level": "intermediate",
            "mode": "standard",
            "created_at": "2025-01-01T00:00:00",
            "last_updated": "2025-01-01T00:00:00",
            "login_count": 10,
            "command_count": 50,
            "error_count": 5,
            "success_count": 45,
            "preferences": {},
            "skills": {
                "python": 0.6,
                "data_analysis": 0.4,
                "quantitative": 0.3,
                "cli_usage": 0.5,
                "configuration": 0.2
            },
            "feature_usage": {"data_import": 20},
            "learning_progress": {}
        }
        
        with open(profile_file, 'w') as f:
            json.dump(existing_data, f)
        
        profile = self.adapter.load_user_profile("existing_user")
        
        self.assertEqual(profile.user_id, "existing_user")
        self.assertEqual(profile.level, UserLevel.INTERMEDIATE)
        self.assertEqual(profile.command_count, 50)
        self.assertEqual(profile.skills["python"], 0.6)
    
    def test_assess_user_level_beginner(self):
        """测试评估初学者级别"""
        profile = UserProfile(
            user_id="test",
            command_count=5,
            success_count=3,
            skills={"python": 0.1, "data_analysis": 0.0, "quantitative": 0.0, 
                   "cli_usage": 0.2, "configuration": 0.0}
        )
        
        level = self.adapter.assess_user_level(profile)
        self.assertEqual(level, UserLevel.BEGINNER)
    
    def test_assess_user_level_intermediate(self):
        """测试评估中级用户级别"""
        profile = UserProfile(
            user_id="test",
            command_count=100,
            success_count=90,
            skills={"python": 0.4, "data_analysis": 0.3, "quantitative": 0.2, 
                   "cli_usage": 0.5, "configuration": 0.3},
            feature_usage={"data_import": 20, "config_update": 15, "batch_operations": 5}
        )
        
        level = self.adapter.assess_user_level(profile)
        self.assertEqual(level, UserLevel.INTERMEDIATE)
    
    def test_assess_user_level_advanced(self):
        """测试评估高级用户级别"""
        profile = UserProfile(
            user_id="test",
            command_count=200,
            success_count=180,
            skills={"python": 0.7, "data_analysis": 0.6, "quantitative": 0.5, 
                   "cli_usage": 0.8, "configuration": 0.6},
            feature_usage={"batch_operations": 20, "api_access": 15, "custom_config": 10}
        )
        
        level = self.adapter.assess_user_level(profile)
        self.assertEqual(level, UserLevel.ADVANCED)
    
    def test_assess_user_level_expert(self):
        """测试评估专家级别"""
        profile = UserProfile(
            user_id="test",
            command_count=500,
            success_count=480,
            skills={"python": 0.9, "data_analysis": 0.8, "quantitative": 0.9, 
                   "cli_usage": 0.9, "configuration": 0.8},
            feature_usage={"batch_operations": 50, "api_access": 40, 
                          "custom_config": 30, "scripting": 25}
        )
        
        level = self.adapter.assess_user_level(profile)
        self.assertEqual(level, UserLevel.EXPERT)
    
    def test_get_adaptation_config(self):
        """测试获取适配配置"""
        # 测试默认配置
        config = self.adapter.get_adaptation_config(UserLevel.BEGINNER)
        self.assertFalse(config.show_advanced_options)
        self.assertTrue(config.show_tooltips)
        
        # 测试高级配置
        config = self.adapter.get_adaptation_config(UserLevel.EXPERT)
        self.assertTrue(config.show_advanced_options)
        self.assertFalse(config.show_tooltips)
    
    def test_adapt_ui_display_beginner(self):
        """测试初学者UI适配"""
        self.adapter.current_profile = UserProfile(
            user_id="test",
            level=UserLevel.BEGINNER
        )
        
        content = {
            "basic_option": "value1",
            "debug_mode": "value2",
            "debug_info": "debug",
            "performance_metrics": "perf"
        }
        
        adapted = self.adapter.adapt_ui_display(content)
        
        # 初学者应该看不到调试选项
        self.assertIn("basic_option", adapted)
        self.assertNotIn("debug_mode", adapted)
        self.assertNotIn("debug_info", adapted)
    
    def test_adapt_ui_display_expert(self):
        """测试专家UI适配"""
        self.adapter.current_profile = UserProfile(
            user_id="test",
            level=UserLevel.EXPERT
        )
        
        content = {
            "basic_option": "value1",
            "advanced_option": "value2",
            "debug_info": "debug"
        }
        
        adapted = self.adapter.adapt_ui_display(content)
        
        # 专家应该看到所有选项
        self.assertIn("basic_option", adapted)
        self.assertIn("advanced_option", adapted)
        self.assertIn("debug_info", adapted)
    
    def test_get_personalized_suggestions_beginner(self):
        """测试初学者个性化建议"""
        self.adapter.current_profile = UserProfile(
            user_id="test",
            level=UserLevel.BEGINNER
        )
        
        suggestions = self.adapter.get_personalized_suggestions()
        
        self.assertGreater(len(suggestions), 0)
        # 检查是否有初学者相关建议
        beginner_keywords = ["基础", "示例", "教程", "help"]
        has_beginner_suggestion = any(
            any(keyword in suggestion for keyword in beginner_keywords)
            for suggestion in suggestions
        )
        self.assertTrue(has_beginner_suggestion)
    
    def test_get_personalized_suggestions_expert(self):
        """测试专家个性化建议"""
        self.adapter.current_profile = UserProfile(
            user_id="test",
            level=UserLevel.EXPERT
        )
        
        suggestions = self.adapter.get_personalized_suggestions()
        
        self.assertGreater(len(suggestions), 0)
        # 检查是否有专家相关建议
        expert_keywords = ["脚本", "性能", "插件", "社区"]
        has_expert_suggestion = any(
            any(keyword in suggestion for keyword in expert_keywords)
            for suggestion in suggestions
        )
        self.assertTrue(has_expert_suggestion)
    
    def test_record_user_action_success(self):
        """测试记录成功用户操作"""
        self.adapter.current_profile = UserProfile(user_id="test")
        initial_count = self.adapter.current_profile.command_count
        initial_success = self.adapter.current_profile.success_count
        
        self.adapter.record_user_action("data_import", success=True)
        
        self.assertEqual(self.adapter.current_profile.command_count, initial_count + 1)
        self.assertEqual(self.adapter.current_profile.success_count, initial_success + 1)
        self.assertIn("data_import", self.adapter.current_profile.feature_usage)
        self.assertEqual(self.adapter.current_profile.feature_usage["data_import"], 1)
    
    def test_record_user_action_failure(self):
        """测试记录失败用户操作"""
        self.adapter.current_profile = UserProfile(user_id="test")
        initial_count = self.adapter.current_profile.command_count
        initial_error = self.adapter.current_profile.error_count
        
        self.adapter.record_user_action("api_call", success=False)
        
        self.assertEqual(self.adapter.current_profile.command_count, initial_count + 1)
        self.assertEqual(self.adapter.current_profile.error_count, initial_error + 1)
    
    def test_skill_update_success(self):
        """测试技能更新 - 成功操作"""
        self.adapter.current_profile = UserProfile(user_id="test")
        initial_skill = self.adapter.current_profile.skills["python"]
        
        self.adapter._update_skills_based_on_action("api_call", success=True)
        
        final_skill = self.adapter.current_profile.skills["python"]
        self.assertGreater(final_skill, initial_skill)
    
    def test_skill_update_failure(self):
        """测试技能更新 - 失败操作"""
        self.adapter.current_profile = UserProfile(
            user_id="test",
            skills={"python": 0.5, "data_analysis": 0.5, "quantitative": 0.5, 
                   "cli_usage": 0.5, "configuration": 0.5}
        )
        initial_skill = self.adapter.current_profile.skills["python"]
        
        self.adapter._update_skills_based_on_action("api_call", success=False)
        
        final_skill = self.adapter.current_profile.skills["python"]
        self.assertLess(final_skill, initial_skill)
    
    def test_filter_advanced_options(self):
        """测试过滤高级选项"""
        content = {
            "basic_setting": "value1",
            "debug_mode": "value2",
            "performance_monitor": "value3",
            "internal_config": "value4",
            "expert_feature": "value5"
        }
        
        filtered = self.adapter._filter_advanced_options(content)
        
        self.assertIn("basic_setting", filtered)
        self.assertNotIn("debug_mode", filtered)
        self.assertNotIn("performance_monitor", filtered)
        self.assertNotIn("internal_config", filtered)
        self.assertNotIn("expert_feature", filtered)
    
    def test_add_tooltips(self):
        """测试添加工具提示"""
        content = {"data_import": "import_value", "config": "config_value"}
        
        enhanced = self.adapter._add_tooltips(content, "commands")
        
        self.assertIn("data_import", enhanced)
        self.assertIn("data_import_tooltip", enhanced)
        self.assertIn("config_tooltip", enhanced)
    
    def test_add_examples(self):
        """测试添加示例"""
        content = {"data_import": "import_value", "api": "api_value"}
        
        enhanced = self.adapter._add_examples(content, "commands")
        
        self.assertIn("data_import", enhanced)
        self.assertIn("data_import_example", enhanced)
        self.assertIn("api_example", enhanced)
    
    def test_get_user_dashboard(self):
        """测试获取用户仪表板"""
        self.adapter.current_profile = UserProfile(
            user_id="test_user",
            level=UserLevel.INTERMEDIATE,
            command_count=100,
            success_count=85,
            error_count=15,
            skills={"python": 0.6, "data_analysis": 0.4, "quantitative": 0.3, 
                   "cli_usage": 0.5, "configuration": 0.2},
            feature_usage={"data_import": 30, "config_update": 20}
        )
        
        dashboard = self.adapter.get_user_dashboard()
        
        self.assertIn("user_info", dashboard)
        self.assertIn("usage_stats", dashboard)
        self.assertIn("skill_progress", dashboard)
        self.assertIn("top_features", dashboard)
        self.assertIn("suggestions", dashboard)
        
        # 检查用户信息
        self.assertEqual(dashboard["user_info"]["user_id"], "test_user")
        self.assertEqual(dashboard["user_info"]["level"], "intermediate")
        
        # 检查使用统计
        self.assertEqual(dashboard["usage_stats"]["total_commands"], 100)
        self.assertEqual(dashboard["usage_stats"]["success_rate"], "85.0%")
        
        # 检查技能进度
        self.assertEqual(dashboard["skill_progress"]["python"], "60.0%")
        
        # 检查常用功能
        self.assertEqual(dashboard["top_features"]["data_import"], 30)
    
    def test_calculate_next_level_progress(self):
        """测试计算升级进度"""
        self.adapter.current_profile = UserProfile(
            user_id="test",
            level=UserLevel.BEGINNER,
            skills={"python": 0.2, "data_analysis": 0.1, "quantitative": 0.1, 
                   "cli_usage": 0.2, "configuration": 0.1}
        )
        
        progress = self.adapter._calculate_next_level_progress()
        
        self.assertIn("next_level", progress)
        self.assertIn("progress", progress)
        self.assertEqual(progress["next_level"], "intermediate")
    
    def test_calculate_next_level_progress_max_level(self):
        """测试最高级别的升级进度"""
        self.adapter.current_profile = UserProfile(
            user_id="test",
            level=UserLevel.EXPERT,
            skills={"python": 0.9, "data_analysis": 0.9, "quantitative": 0.9, 
                   "cli_usage": 0.9, "configuration": 0.9}
        )
        
        progress = self.adapter._calculate_next_level_progress()
        
        self.assertIn("message", progress)
        self.assertIn("最高级别", progress["message"])


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
        
        # 模拟依赖
        self.mock_config = MagicMock()
        self.mock_ui = MagicMock()
        self.mock_paths = MagicMock()
        self.mock_paths.ROOT = Path(self.temp_dir)
    
    @patch('utils.user_adapter.get_config_manager')
    @patch('utils.user_adapter.get_ui')
    @patch('utils.user_adapter.Paths')
    def test_full_user_lifecycle(self, mock_paths, mock_get_ui, mock_get_config):
        """测试完整用户生命周期"""
        # 设置模拟
        mock_get_config.return_value = self.mock_config
        mock_get_ui.return_value = self.mock_ui
        mock_paths.return_value = self.mock_paths
        
        # 创建适配器
        adapter = UserAdapter()
        
        # 1. 加载新用户
        profile = adapter.load_user_profile("lifecycle_test")
        self.assertEqual(profile.level, UserLevel.BEGINNER)
        
        # 2. 记录用户操作并观察级别变化
        for _ in range(20):
            adapter.record_user_action("data_import", success=True)
        
        for _ in range(10):
            adapter.record_user_action("config_update", success=True)
        
        # 3. 检查级别提升
        current_level = adapter.current_profile.level
        self.assertIn(current_level, [UserLevel.BEGINNER, UserLevel.INTERMEDIATE])
        
        # 4. 获取适配配置
        config = adapter.get_adaptation_config()
        self.assertIsInstance(config, AdaptationConfig)
        
        # 5. 获取个性化建议
        suggestions = adapter.get_personalized_suggestions()
        self.assertIsInstance(suggestions, list)
        
        # 6. 获取仪表板
        dashboard = adapter.get_user_dashboard()
        self.assertIn("user_info", dashboard)
        self.assertEqual(dashboard["user_info"]["user_id"], "lifecycle_test")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)