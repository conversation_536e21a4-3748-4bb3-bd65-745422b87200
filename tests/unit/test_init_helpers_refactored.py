import sys
import unittest
from unittest.mock import patch, MagicMock, call, ANY
from pathlib import Path
import subprocess
import platform

# 将项目根目录添加到 sys.path，以便正确导入 src.aqua.cli.init_helpers
project_root = Path(__file__).resolve().parent.parent.parent
if str(project_root / 'src') not in sys.path:
    sys.path.insert(0, str(project_root / 'src'))

from aqua.cli import init_helpers

class TestInitHelpers(unittest.TestCase):

    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    @patch('subprocess.run')
    def test_check_uv_installed_success(self, mock_run, mock_exists):
        mock_run.return_value = MagicMock(returncode=0, stdout="uv 0.1.0")
        self.assertTrue(init_helpers.check_uv_installed())
        mock_run.assert_called_once_with(['uv', '--version'], check=True, capture_output=True, text=True)

    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    @patch('subprocess.run')
    def test_check_uv_installed_failure(self, mock_run, mock_exists):
        mock_run.side_effect = FileNotFoundError
        self.assertFalse(init_helpers.check_uv_installed())
        mock_run.assert_called_once_with(['uv', '--version'], check=True, capture_output=True, text=True)

    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    @patch('subprocess.run')
    @patch('sys.exit')
    def test_install_uv_success(self, mock_sys_exit, mock_run, mock_print, mock_exists):
        # 模拟 uv 未安装，以便触发安装逻辑
        mock_exists.side_effect = [False, True] # 第一次 check_uv_installed 返回 False，安装后返回 True
        mock_run.side_effect = [
            MagicMock(side_effect=FileNotFoundError), # 第一次 check_uv_installed 内部调用 uv --version 失败
            MagicMock(returncode=0, stdout="uv 0.1.0") # pip install uv 成功
        ]
        init_helpers.install_uv()
        mock_print.assert_has_calls([
            call("... 正在尝试通过 pip 安装 uv ..."),
            call("✅ uv 工具安装成功。")
        ])
        mock_sys_exit.assert_not_called()

    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    @patch('subprocess.run')
    @patch('sys.exit')
    def test_install_uv_failure(self, mock_sys_exit, mock_run, mock_print, mock_exists):
        # 模拟 uv 未安装，以便触发安装逻辑
        mock_exists.return_value = False # For check_uv_installed() in install_uv
        mock_run.side_effect = [
            MagicMock(side_effect=FileNotFoundError), # 第一次 check_uv_installed 内部调用 uv --version 失败
            MagicMock(side_effect=subprocess.CalledProcessError(1, 'cmd')) # pip install uv 失败
        ]
        init_helpers.install_uv()
        mock_print.assert_has_calls([
            call("... 正在尝试通过 pip 安装 uv ..."),
            call(unittest.mock.ANY), # 匹配 f"❌ uv 安装失败: {e}"
            call("请参考 https://github.com/astral-sh/uv 手动安装。")
        ])
        mock_sys_exit.assert_called_once_with(1)

    @patch('sys.executable', new='/usr/bin/python3') # 模拟不在虚拟环境中
    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    @patch('os.execv')
    @patch('sys.exit')
    def test_reexec_in_venv_success(self, mock_sys_exit, mock_execv, mock_print, mock_exists):
        mock_exists.return_value = True # 虚拟环境解释器存在
        # 模拟当前不在虚拟环境中
        with patch('sys.executable', new=str(init_helpers.VENV_PATH / ('Scripts' if platform.system() == 'Windows' else 'bin') / 'python') + '_mock_outside'):
            init_helpers.reexec_in_venv()
            mock_print.assert_called_once_with(f"... 正在切换到虚拟环境: {init_helpers.VENV_PATH / ('Scripts' if platform.system() == 'Windows' else 'bin') / 'python'} ...")
            if platform.system() == "Windows":
                # Windows 下是 subprocess.Popen，这里简化为不调用 os.execv
                pass
            else:
                mock_execv.assert_called_once()
            mock_sys_exit.assert_not_called()

    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    @patch('sys.exit')
    def test_reexec_in_venv_venv_not_found(self, mock_sys_exit, mock_print, mock_exists):
        mock_exists.return_value = False # venv_py_path does not exist
        init_helpers.reexec_in_venv()
        mock_print.assert_called_once_with(f"❌ 错误：预期的虚拟环境解释器不存在于: {init_helpers.VENV_PATH / ('Scripts' if platform.system() == 'Windows' else 'bin') / 'python'}")
        mock_sys_exit.assert_called_once_with(1)

    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    def test_create_project_venv_exists(self, mock_print, mock_exists):
        mock_exists.return_value = True
        init_helpers.create_project_venv()
        mock_print.assert_called_once_with("✅ .venv 虚拟环境已存在。")

    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    @patch('subprocess.run')
    def test_create_project_venv_success(self, mock_run, mock_print, mock_exists):
        mock_exists.side_effect = [False, True] # 第一次检查返回 False，创建后返回 True
        mock_run.return_value = MagicMock(returncode=0)
        init_helpers.create_project_venv()
        mock_print.assert_has_calls([
            call(f"... 正在创建虚拟环境: {init_helpers.VENV_PATH} ..."),
            call("✅ 虚拟环境创建成功。")
        ])
        mock_run.assert_called_once_with(
            [sys.executable, '-m', 'venv', init_helpers.VENV_PATH],
            check=True
        )

    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    @patch('subprocess.run')
    @patch('sys.exit')
    def test_create_project_venv_failure(self, mock_sys_exit, mock_run, mock_print, mock_exists):
        mock_exists.return_value = False
        mock_run.side_effect = subprocess.CalledProcessError(1, 'cmd')
        init_helpers.create_project_venv()
        mock_print.assert_has_calls([
            call(f"... 正在创建虚拟环境: {init_helpers.VENV_PATH} ..."),
            call(unittest.mock.ANY) # 匹配 f"❌ 创建虚拟环境失败: {e}"
        ])
        mock_sys_exit.assert_called_once_with(1)

    @patch('builtins.print')
    @patch('pathlib.Path.exists')
    def test_sync_dependencies_no_requirements_file(self, mock_exists, mock_print):
        mock_exists.return_value = False
        self.assertTrue(init_helpers.sync_dependencies())
        mock_print.assert_called_once_with("⚠️ 未找到 requirements.txt，跳过依赖同步。")

    @patch('builtins.print')
    @patch('pathlib.Path.exists')
    @patch('subprocess.run')
    @patch('src.aqua.cli.init_helpers.is_china_network', return_value=False)
    def test_sync_dependencies_success(self, mock_is_china_network, mock_run, mock_exists, mock_print):
        mock_exists.return_value = True
        mock_run.return_value = MagicMock(returncode=0)
        self.assertTrue(init_helpers.sync_dependencies())
        mock_print.assert_has_calls([
            call("... 正在同步Python依赖 ..."),
            call("✅ Python依赖同步成功。")
        ])
        mock_run.assert_called_once_with(
            ['uv', 'pip', 'sync', str(init_helpers._PROJECT_ROOT / 'requirements.txt')],
            check=True, capture_output=True, text=True
        )

    @patch('builtins.print')
    @patch('pathlib.Path.exists')
    @patch('subprocess.run')
    @patch('src.aqua.cli.init_helpers.is_china_network', return_value=True)
    def test_sync_dependencies_china_network(self, mock_is_china_network, mock_run, mock_exists, mock_print):
        mock_exists.return_value = True
        mock_run.return_value = MagicMock(returncode=0)
        self.assertTrue(init_helpers.sync_dependencies())
        mock_print.assert_has_calls([
            call("... 正在同步Python依赖 ..."),
            call("检测到中国网络环境，使用镜像: https://pypi.tuna.tsinghua.edu.cn/simple"),
            call("✅ Python依赖同步成功。")
        ])
        mock_run.assert_called_once_with(
            ['uv', 'pip', 'sync', str(init_helpers._PROJECT_ROOT / 'requirements.txt'), '-i', 'https://pypi.tuna.tsinghua.edu.cn/simple'],
            check=True, capture_output=True, text=True
        )

    @patch('builtins.print')
    @patch('pathlib.Path.exists')
    @patch('subprocess.run')
    @patch('src.aqua.cli.init_helpers.is_china_network', return_value=True) # 模拟中国网络环境
    def test_sync_dependencies_failure(self, mock_is_china_network, mock_run, mock_exists, mock_print):
        mock_exists.return_value = True
        mock_run.side_effect = subprocess.CalledProcessError(1, 'cmd', stderr='Error output')
        self.assertFalse(init_helpers.sync_dependencies())
        mock_print.assert_has_calls([
            call("... 正在同步Python依赖 ..."),
            call("检测到中国网络环境，使用镜像: https://pypi.tuna.tsinghua.edu.cn/simple"),
            call("❌ 依赖同步失败。"),
            call("错误详情: Error output")
        ])

    @patch('builtins.print')
    @patch('pathlib.Path.exists')
    @patch('duckdb.connect')
    @patch('sys.exit')
    def test_init_database_exists(self, mock_sys_exit, mock_connect, mock_exists, mock_print):
        mock_exists.side_effect = [True, True] # 模拟 data 目录存在，db 文件也存在
        init_helpers.init_database('test_env')
        mock_print.assert_has_calls([
            call("... 正在为 'test_env' 环境初始化数据库 ..."),
            call("✅ 'test_env' 环境的数据库已存在于: " + str(init_helpers._PROJECT_ROOT / 'data' / 'aqua_test_env.duckdb'))
        ])
        mock_connect.assert_not_called()
        mock_sys_exit.assert_not_called()

    @patch('builtins.print')
    @patch('pathlib.Path.exists')
    @patch('pathlib.Path.mkdir')
    @patch('duckdb.connect')
    @patch('sys.exit')
    def test_init_database_success(self, mock_sys_exit, mock_connect, mock_mkdir, mock_exists, mock_print):
        mock_exists.side_effect = [False, False] # 模拟 data 目录不存在，db 文件也不存在
        mock_connect.return_value = MagicMock()
        init_helpers.init_database('test_env')
        mock_print.assert_has_calls([
            call("... 正在为 'test_env' 环境初始化数据库 ..."),
            call(f"创建数据库目录: {init_helpers._PROJECT_ROOT / 'data'}"),
            call("✅ 'test_env' 环境的数据库创建成功。")
        ])
        mock_mkdir.assert_called_once_with(parents=True, exist_ok=True)
        mock_connect.assert_called_once_with(str(init_helpers._PROJECT_ROOT / 'data' / 'aqua_test_env.duckdb'))
        mock_sys_exit.assert_not_called()

    @patch('builtins.print')
    @patch('pathlib.Path.exists')
    @patch('duckdb.connect')
    @patch('sys.exit')
    def test_init_database_failure(self, mock_sys_exit, mock_connect, mock_exists, mock_print):
        mock_exists.side_effect = [True, False] # 模拟 data 目录存在，db 文件不存在
        mock_connect.side_effect = Exception("DB Error")
        init_helpers.init_database('test_env')
        mock_print.assert_has_calls([
            call("... 正在为 'test_env' 环境初始化数据库 ..."),
            call(unittest.mock.ANY) # 匹配 f"❌ 数据库初始化失败: {e}"
        ])
        mock_sys_exit.assert_called_once_with(1)

    @patch('socket.gethostbyname')
    def test_is_china_network_true(self, mock_gethostbyname):
        mock_gethostbyname.return_value = '*******'
        self.assertTrue(init_helpers.is_china_network())
        mock_gethostbyname.assert_called_once_with('pypi.tuna.tsinghua.edu.cn')

    @patch('socket.gethostbyname')
    def test_is_china_network_false(self, mock_gethostbyname):
        mock_gethostbyname.side_effect = OSError
        self.assertFalse(init_helpers.is_china_network())
        mock_gethostbyname.assert_called_once_with('pypi.tuna.tsinghua.edu.cn')