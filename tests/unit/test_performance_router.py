#!/usr/bin/env python3
"""
性能路由API单元测试

测试高性能API路由的各个端点功能
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import json

from main import app
from src.utils.exceptions import AquaException, CacheException, DatabaseException

client = TestClient(app)


class TestPerformanceRouter:
    """性能路由测试类"""
    
    def test_performance_health_check(self):
        """测试性能健康检查端点"""
        response = client.get("/api/performance/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "cache_status" in data
        assert "database_status" in data
        assert data["status"] == "healthy"
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_get_stock_data_success(self, mock_manager_class, mock_cache):
        """测试获取股票数据成功"""
        # 模拟缓存未命中
        mock_cache.get.return_value = None
        
        # 模拟数据库管理器
        mock_manager = Mock()
        mock_manager.execute_query.return_value = [
            ("000001", "平安银行", "2023-01-01", 10.50, 10.60, 10.40, 10.55, 1000000),
            ("000002", "万科A", "2023-01-01", 15.20, 15.30, 15.10, 15.25, 800000)
        ]
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/performance/stock/000001")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert len(data["data"]) == 2
        assert data["data"][0]["symbol"] == "000001"
        assert data["data"][0]["name"] == "平安银行"
        
        # 验证缓存被调用
        mock_cache.get.assert_called_once()
        mock_cache.put.assert_called_once()
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_get_stock_data_cache_hit(self, mock_manager_class, mock_cache):
        """测试股票数据缓存命中"""
        # 模拟缓存命中
        cached_data = [
            {
                "symbol": "000001",
                "name": "平安银行",
                "date": "2023-01-01",
                "open": 10.50,
                "high": 10.60,
                "low": 10.40,
                "close": 10.55,
                "volume": 1000000
            }
        ]
        mock_cache.get.return_value = cached_data
        
        response = client.get("/api/performance/stock/000001")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert data["data"] == cached_data
        assert data["from_cache"] is True
        
        # 验证数据库未被调用
        mock_manager_class.assert_not_called()
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_get_futures_data_success(self, mock_manager_class, mock_cache):
        """测试获取期货数据成功"""
        # 模拟缓存未命中
        mock_cache.get.return_value = None
        
        # 模拟数据库管理器
        mock_manager = Mock()
        mock_manager.execute_query.return_value = [
            ("IF2301", "沪深300指数期货", "2023-01-01", 4000.0, 4010.0, 3990.0, 4005.0, 50000),
            ("IC2301", "中证500指数期货", "2023-01-01", 5500.0, 5520.0, 5480.0, 5510.0, 30000)
        ]
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/performance/futures/IF2301")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert len(data["data"]) == 2
        assert data["data"][0]["symbol"] == "IF2301"
        assert data["data"][0]["name"] == "沪深300指数期货"
        
        # 验证缓存被调用
        mock_cache.get.assert_called_once()
        mock_cache.put.assert_called_once()
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_get_market_overview_success(self, mock_manager_class, mock_cache):
        """测试获取市场概览成功"""
        # 模拟缓存未命中
        mock_cache.get.return_value = None
        
        # 模拟数据库管理器
        mock_manager = Mock()
        mock_manager.execute_query.return_value = [
            ("股票", 5000, 3000, 2000, 60.0),
            ("期货", 500, 300, 200, 60.0)
        ]
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/performance/market/overview")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert len(data["data"]) == 2
        assert data["data"][0]["market_type"] == "股票"
        assert data["data"][0]["total_count"] == 5000
        
        # 验证缓存被调用
        mock_cache.get.assert_called_once()
        mock_cache.put.assert_called_once()
    
    @patch('src.api.routers.performance_router.cache_manager')
    def test_get_stock_data_cache_error(self, mock_cache):
        """测试缓存错误处理"""
        # 模拟缓存错误
        mock_cache.get.side_effect = CacheException("Cache服务不可用", "CACHE_SERVICE_ERROR")
        
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            mock_manager = Mock()
            mock_manager.execute_query.return_value = [
                ("000001", "平安银行", "2023-01-01", 10.50, 10.60, 10.40, 10.55, 1000000)
            ]
            mock_manager_class.return_value = mock_manager
            
            response = client.get("/api/performance/stock/000001")
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert "data" in data
            assert data["from_cache"] is False
            # 缓存错误时应该直接查询数据库
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_get_stock_data_database_error(self, mock_manager_class, mock_cache):
        """测试数据库错误处理"""
        # 模拟缓存未命中
        mock_cache.get.return_value = None
        
        # 模拟数据库错误
        mock_manager = Mock()
        mock_manager.execute_query.side_effect = DatabaseException("数据库连接失败", "DATABASE_CONNECTION_ERROR")
        mock_manager_class.return_value = mock_manager
        
        with pytest.raises(Exception):
            response = client.get("/api/performance/stock/000001")
    
    def test_get_stock_data_invalid_symbol(self):
        """测试无效股票代码"""
        response = client.get("/api/performance/stock/")
        assert response.status_code == 404
        
        # 测试过长的股票代码
        response = client.get("/api/performance/stock/000001234567890")
        assert response.status_code == 422
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_get_stock_data_with_date_range(self, mock_manager_class, mock_cache):
        """测试指定日期范围查询股票数据"""
        # 模拟缓存未命中
        mock_cache.get.return_value = None
        
        # 模拟数据库管理器
        mock_manager = Mock()
        mock_manager.execute_query.return_value = [
            ("000001", "平安银行", "2023-01-01", 10.50, 10.60, 10.40, 10.55, 1000000),
            ("000001", "平安银行", "2023-01-02", 10.55, 10.65, 10.45, 10.60, 1100000)
        ]
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/performance/stock/000001?start_date=2023-01-01&end_date=2023-01-02")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]) == 2
        
        # 验证SQL查询包含日期范围
        mock_manager.execute_query.assert_called_once()
        call_args = mock_manager.execute_query.call_args[0][0]
        assert "WHERE" in call_args
        assert "date >= ?" in call_args
        assert "date <= ?" in call_args
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_get_stock_data_with_limit(self, mock_manager_class, mock_cache):
        """测试限制返回数量"""
        # 模拟缓存未命中
        mock_cache.get.return_value = None
        
        # 模拟数据库管理器
        mock_manager = Mock()
        mock_manager.execute_query.return_value = [
            ("000001", "平安银行", "2023-01-01", 10.50, 10.60, 10.40, 10.55, 1000000)
        ]
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/performance/stock/000001?limit=10")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        
        # 验证SQL查询包含LIMIT
        mock_manager.execute_query.assert_called_once()
        call_args = mock_manager.execute_query.call_args[0][0]
        assert "LIMIT" in call_args
    
    def test_get_stock_data_invalid_date_format(self):
        """测试无效日期格式"""
        response = client.get("/api/performance/stock/000001?start_date=invalid-date")
        assert response.status_code == 422
        
        response = client.get("/api/performance/stock/000001?end_date=2023-13-01")
        assert response.status_code == 422
    
    def test_get_stock_data_invalid_limit(self):
        """测试无效限制参数"""
        response = client.get("/api/performance/stock/000001?limit=0")
        assert response.status_code == 422
        
        response = client.get("/api/performance/stock/000001?limit=10000")
        assert response.status_code == 422
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_get_futures_data_with_filters(self, mock_manager_class, mock_cache):
        """测试期货数据过滤查询"""
        # 模拟缓存未命中
        mock_cache.get.return_value = None
        
        # 模拟数据库管理器
        mock_manager = Mock()
        mock_manager.execute_query.return_value = [
            ("IF2301", "沪深300指数期货", "2023-01-01", 4000.0, 4010.0, 3990.0, 4005.0, 50000)
        ]
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/performance/futures/IF2301?start_date=2023-01-01&limit=100")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]) == 1
        assert data["data"][0]["symbol"] == "IF2301"
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_cache_performance_decorator(self, mock_manager_class, mock_cache):
        """测试缓存性能装饰器"""
        # 模拟缓存未命中
        mock_cache.get.return_value = None
        
        # 模拟数据库管理器
        mock_manager = Mock()
        mock_manager.execute_query.return_value = [
            ("000001", "平安银行", "2023-01-01", 10.50, 10.60, 10.40, 10.55, 1000000)
        ]
        mock_manager_class.return_value = mock_manager
        
        # 第一次请求 - 缓存未命中
        response1 = client.get("/api/performance/stock/000001")
        assert response1.status_code == 200
        
        data1 = response1.json()
        assert data1["from_cache"] is False
        
        # 验证缓存被设置
        mock_cache.put.assert_called_once()
        
        # 模拟第二次请求 - 缓存命中
        mock_cache.get.return_value = data1["data"]
        response2 = client.get("/api/performance/stock/000001")
        assert response2.status_code == 200
        
        data2 = response2.json()
        assert data2["from_cache"] is True
        
        # 验证数据库只被调用一次
        assert mock_manager.execute_query.call_count == 1
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_response_format_consistency(self, mock_manager_class, mock_cache):
        """测试响应格式一致性"""
        # 模拟缓存未命中
        mock_cache.get.return_value = None
        
        # 模拟数据库管理器
        mock_manager = Mock()
        mock_manager.execute_query.return_value = [
            ("000001", "平安银行", "2023-01-01", 10.50, 10.60, 10.40, 10.55, 1000000)
        ]
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/performance/stock/000001")
        assert response.status_code == 200
        
        data = response.json()
        
        # 验证响应格式
        required_fields = ["success", "data", "message", "timestamp", "from_cache"]
        for field in required_fields:
            assert field in data
        
        assert isinstance(data["success"], bool)
        assert isinstance(data["data"], list)
        assert isinstance(data["message"], str)
        assert isinstance(data["timestamp"], str)
        assert isinstance(data["from_cache"], bool)
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_performance_monitoring(self, mock_manager_class, mock_cache):
        """测试性能监控"""
        # 模拟缓存未命中
        mock_cache.get.return_value = None
        
        # 模拟数据库管理器
        mock_manager = Mock()
        mock_manager.execute_query.return_value = [
            ("000001", "平安银行", "2023-01-01", 10.50, 10.60, 10.40, 10.55, 1000000)
        ]
        mock_manager_class.return_value = mock_manager
        
        import time
        start_time = time.time()
        
        response = client.get("/api/performance/stock/000001")
        
        end_time = time.time()
        response_time = end_time - start_time
        
        assert response.status_code == 200
        assert response_time < 1.0  # 响应时间应该小于1秒
        
        # 验证响应包含性能信息
        data = response.json()
        if "performance" in data:
            assert "response_time" in data["performance"]
            assert "cache_hit" in data["performance"]


class TestPerformanceRouterErrorHandling:
    """性能路由错误处理测试"""
    
    def test_http_exception_handling(self):
        """测试HTTP异常处理"""
        # 测试404错误
        response = client.get("/api/performance/nonexistent")
        assert response.status_code == 404
    
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_database_exception_handling(self, mock_manager_class):
        """测试数据库异常处理"""
        # 模拟数据库异常
        mock_manager = Mock()
        mock_manager.execute_query.side_effect = DatabaseException("查询失败", "DATABASE_QUERY_ERROR")
        mock_manager_class.return_value = mock_manager
        
        with patch('src.api.routers.performance_router.cache_manager') as mock_cache:
            mock_cache.get.return_value = None
            
            response = client.get("/api/performance/stock/000001")
            # 根据实际的错误处理逻辑，这里可能是500或其他状态码
            assert response.status_code in [500, 503]
    
    @patch('src.api.routers.performance_router.cache_manager')
    def test_cache_exception_handling(self, mock_cache):
        """测试缓存异常处理"""
        # 模拟缓存异常
        mock_cache.get.side_effect = CacheException("缓存服务错误", "CACHE_SERVICE_ERROR")
        
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            mock_manager = Mock()
            mock_manager.execute_query.return_value = [
                ("000001", "平安银行", "2023-01-01", 10.50, 10.60, 10.40, 10.55, 1000000)
            ]
            mock_manager_class.return_value = mock_manager
            
            response = client.get("/api/performance/stock/000001")
            assert response.status_code == 200
            
            # 缓存异常时应该降级到数据库查询
            data = response.json()
            assert data["from_cache"] is False


class TestPerformanceRouterIntegration:
    """性能路由集成测试"""
    
    @patch('src.api.routers.performance_router.cache_manager')
    @patch('src.api.routers.performance_router.DuckDBConnectionManager')
    def test_full_request_lifecycle(self, mock_manager_class, mock_cache):
        """测试完整请求生命周期"""
        # 模拟完整的请求处理流程
        mock_cache.get.return_value = None
        
        mock_manager = Mock()
        mock_manager.execute_query.return_value = [
            ("000001", "平安银行", "2023-01-01", 10.50, 10.60, 10.40, 10.55, 1000000)
        ]
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/performance/stock/000001")
        
        # 验证完整的处理流程
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        
        # 验证所有组件都被正确调用
        mock_cache.get.assert_called_once()
        mock_manager.execute_query.assert_called_once()
        mock_cache.put.assert_called_once()
    
    def test_cors_headers(self):
        """测试CORS头"""
        response = client.get("/api/performance/health")
        assert response.status_code == 200
        
        # 验证响应头
        headers = response.headers
        assert "content-type" in headers
        assert "application/json" in headers["content-type"]


@pytest.mark.parametrize("symbol", ["000001", "000002", "600000", "600036"])
def test_different_stock_symbols(symbol):
    """测试不同股票代码"""
    with patch('src.api.routers.performance_router.cache_manager') as mock_cache:
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            mock_cache.get.return_value = None
            
            mock_manager = Mock()
            mock_manager.execute_query.return_value = [
                (symbol, "测试股票", "2023-01-01", 10.0, 10.1, 9.9, 10.05, 1000000)
            ]
            mock_manager_class.return_value = mock_manager
            
            response = client.get(f"/api/performance/stock/{symbol}")
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert data["data"][0]["symbol"] == symbol


@pytest.mark.parametrize("futures_symbol", ["IF2301", "IC2301", "IH2301"])
def test_different_futures_symbols(futures_symbol):
    """测试不同期货代码"""
    with patch('src.api.routers.performance_router.cache_manager') as mock_cache:
        with patch('src.api.routers.performance_router.DuckDBConnectionManager') as mock_manager_class:
            mock_cache.get.return_value = None
            
            mock_manager = Mock()
            mock_manager.execute_query.return_value = [
                (futures_symbol, "测试期货", "2023-01-01", 4000.0, 4010.0, 3990.0, 4005.0, 50000)
            ]
            mock_manager_class.return_value = mock_manager
            
            response = client.get(f"/api/performance/futures/{futures_symbol}")
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert data["data"][0]["symbol"] == futures_symbol


if __name__ == "__main__":
    pytest.main([__file__, "-v"])