#!/usr/bin/env python3
"""
策略优化算法测试

测试覆盖：
1. 多种优化算法实现(遗传算法、粒子群等)
2. 策略评估指标计算和风险约束
3. 参数优化和回测验证
4. 复用PriorityQueueManager和RoutingStrategy模式
5. 并行优化和性能监控
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import tempfile
import json
from typing import Dict, List, Any

# 假设的导入路径 - 基于架构设计
from src.ai_agent.strategy_optimizer import StrategyOptimizer
from src.ai_agent.ai_strategy import OptimizationStrategy
from src.storage.unified_storage_manager import UnifiedStorageManager
from src.storage.priority_queue_manager import PriorityQueueManager, TaskPriority
from src.storage.conflict_resolution_engine import ConflictResolutionEngine
from src.routing.routing_strategy import RoutingStrategy
from src.utils.logger import get_logger


class TestStrategyOptimizer:
    """策略优化器测试类"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        return {
            "database": {
                "environments": {
                    "test": "test_optimizer.db"
                },
                "default_environment": "test"
            },
            "strategy_optimization": {
                "default_algorithm": "genetic_algorithm",
                "max_generations": 100,
                "population_size": 50,
                "mutation_rate": 0.1,
                "crossover_rate": 0.8,
                "elite_ratio": 0.1,
                "convergence_threshold": 0.001,
                "max_optimization_time_hours": 24
            },
            "optimization_algorithms": {
                "genetic_algorithm": {
                    "enabled": True,
                    "max_generations": 100,
                    "population_size": 50
                },
                "particle_swarm": {
                    "enabled": True,
                    "num_particles": 30,
                    "max_iterations": 200
                },
                "simulated_annealing": {
                    "enabled": True,
                    "initial_temperature": 1000,
                    "cooling_rate": 0.95
                }
            },
            "risk_constraints": {
                "max_drawdown": 0.15,
                "min_sharpe_ratio": 1.0,
                "max_var_95": 0.05,
                "max_leverage": 3.0
            },
            "performance_metrics": {
                "primary_objective": "sharpe_ratio",
                "secondary_objectives": ["max_drawdown", "calmar_ratio"],
                "benchmark": "CSI300"
            },
            "priority_queue": {
                "max_queue_size": 1000,
                "worker_threads": 4
            }
        }
    
    @pytest.fixture
    def sample_price_data(self):
        """样本价格数据"""
        dates = pd.date_range("20230101", periods=252, freq="D")  # 一年交易数据
        np.random.seed(42)
        
        # 模拟价格序列
        returns = np.random.normal(0.0008, 0.02, 252)  # 日收益率
        prices = 100 * np.exp(np.cumsum(returns))
        
        return pd.DataFrame({
            "trade_date": dates.strftime("%Y%m%d"),
            "ts_code": ["000001.SZ"] * 252,
            "open": prices * (1 + np.random.normal(0, 0.005, 252)),
            "high": prices * (1 + np.abs(np.random.normal(0, 0.01, 252))),
            "low": prices * (1 - np.abs(np.random.normal(0, 0.01, 252))),
            "close": prices,
            "vol": np.random.uniform(800000, 1200000, 252)
        })
    
    @pytest.fixture
    def sample_strategy_params(self):
        """样本策略参数"""
        return {
            "strategy_name": "mean_reversion",
            "parameters": {
                "lookback_period": {"min": 5, "max": 50, "current": 20},
                "entry_threshold": {"min": 1.0, "max": 3.0, "current": 2.0},
                "exit_threshold": {"min": 0.5, "max": 2.0, "current": 1.0},
                "position_size": {"min": 0.1, "max": 1.0, "current": 0.5},
                "stop_loss": {"min": 0.02, "max": 0.1, "current": 0.05}
            },
            "constraints": {
                "max_positions": 10,
                "max_sector_weight": 0.3,
                "min_liquidity": 1000000
            }
        }
    
    @pytest.fixture
    def strategy_optimizer(self, mock_config):
        """创建策略优化器实例"""
        with patch('src.storage.unified_storage_manager.UnifiedStorageManager'):
            with patch('src.storage.priority_queue_manager.PriorityQueueManager'):
                with patch('src.storage.conflict_resolution_engine.ConflictResolutionEngine'):
                    return StrategyOptimizer(mock_config)
    
    def test_strategy_optimizer_initialization(self, mock_config):
        """测试策略优化器初始化"""
        with patch('src.storage.unified_storage_manager.UnifiedStorageManager') as mock_storage:
            with patch('src.storage.priority_queue_manager.PriorityQueueManager') as mock_queue:
                with patch('src.storage.conflict_resolution_engine.ConflictResolutionEngine') as mock_conflict:
                    optimizer = StrategyOptimizer(mock_config)
                    
                    # 验证组件初始化
                    mock_storage.assert_called_once_with(mock_config)
                    mock_queue.assert_called_once_with(mock_config)
                    mock_conflict.assert_called_once()
                    
                    # 验证优化策略注册
                    assert "genetic_algorithm" in optimizer.optimization_strategies
                    assert "particle_swarm" in optimizer.optimization_strategies
                    assert "simulated_annealing" in optimizer.optimization_strategies
    
    def test_genetic_algorithm_optimization(self, strategy_optimizer, sample_strategy_params, sample_price_data):
        """测试遗传算法优化"""
        # Given: 遗传算法配置
        optimization_config = {
            "algorithm": "genetic_algorithm",
            "strategy_params": sample_strategy_params,
            "optimization_period": "2023-01-01 to 2023-12-31",
            "objective_function": "maximize_sharpe_ratio"
        }
        
        # Mock历史数据查询
        strategy_optimizer.storage_manager.query_data = Mock(return_value=sample_price_data)
        
        # When: 执行遗传算法优化
        with patch.object(strategy_optimizer, '_run_genetic_algorithm') as mock_ga:
            mock_ga.return_value = {
                "best_individual": {
                    "lookback_period": 15,
                    "entry_threshold": 2.5,
                    "exit_threshold": 1.2,
                    "position_size": 0.6,
                    "stop_loss": 0.04
                },
                "best_fitness": 2.15,  # Sharpe ratio
                "generation": 78,
                "convergence_achieved": True,
                "optimization_time_seconds": 1800
            }
            
            result = strategy_optimizer.optimize_strategy(optimization_config)
        
        # Then: 验证遗传算法优化结果
        assert result["success"] is True
        assert result["algorithm"] == "genetic_algorithm"
        assert result["best_fitness"] == 2.15
        assert result["convergence_achieved"] is True
        assert "optimized_parameters" in result
    
    def test_particle_swarm_optimization(self, strategy_optimizer, sample_strategy_params):
        """测试粒子群优化算法"""
        # Given: 粒子群优化配置
        optimization_config = {
            "algorithm": "particle_swarm",
            "strategy_params": sample_strategy_params,
            "swarm_size": 30,
            "max_iterations": 100,
            "inertia_weight": 0.9,
            "cognitive_coefficient": 2.0,
            "social_coefficient": 2.0
        }
        
        # When: 执行粒子群优化
        with patch.object(strategy_optimizer, '_run_particle_swarm_optimization') as mock_pso:
            mock_pso.return_value = {
                "global_best_position": {
                    "lookback_period": 18,
                    "entry_threshold": 2.2,
                    "exit_threshold": 1.1,
                    "position_size": 0.7,
                    "stop_loss": 0.045
                },
                "global_best_fitness": 1.98,
                "iteration": 85,
                "swarm_diversity": 0.12,
                "convergence_speed": "medium"
            }
            
            result = strategy_optimizer.optimize_strategy(optimization_config)
        
        # Then: 验证粒子群优化结果
        assert result["success"] is True
        assert result["algorithm"] == "particle_swarm"
        assert result["global_best_fitness"] == 1.98
        assert "swarm_diversity" in result
    
    def test_simulated_annealing_optimization(self, strategy_optimizer, sample_strategy_params):
        """测试模拟退火优化算法"""
        # Given: 模拟退火配置
        optimization_config = {
            "algorithm": "simulated_annealing",
            "strategy_params": sample_strategy_params,
            "initial_temperature": 1000,
            "final_temperature": 0.1,
            "cooling_rate": 0.95,
            "max_iterations": 500
        }
        
        # When: 执行模拟退火优化
        with patch.object(strategy_optimizer, '_run_simulated_annealing') as mock_sa:
            mock_sa.return_value = {
                "best_solution": {
                    "lookback_period": 22,
                    "entry_threshold": 1.8,
                    "exit_threshold": 0.9,
                    "position_size": 0.55,
                    "stop_loss": 0.06
                },
                "best_energy": 1.85,  # 负的目标函数值
                "final_temperature": 0.1,
                "acceptance_ratio": 0.25,
                "cooling_schedule": "exponential"
            }
            
            result = strategy_optimizer.optimize_strategy(optimization_config)
        
        # Then: 验证模拟退火优化结果
        assert result["success"] is True
        assert result["algorithm"] == "simulated_annealing"
        assert result["best_energy"] == 1.85
        assert "acceptance_ratio" in result
    
    def test_strategy_performance_evaluation(self, strategy_optimizer, sample_price_data):
        """测试策略性能评估"""
        # Given: 策略参数和回测数据
        strategy_params = {
            "lookback_period": 20,
            "entry_threshold": 2.0,
            "exit_threshold": 1.0,
            "position_size": 0.5,
            "stop_loss": 0.05
        }
        
        # Mock回测结果
        backtest_results = pd.DataFrame({
            "trade_date": pd.date_range("20230101", periods=252, freq="D"),
            "position": np.random.choice([0, 1, -1], 252),
            "pnl": np.random.normal(100, 500, 252),
            "cumulative_pnl": np.cumsum(np.random.normal(100, 500, 252))
        })
        
        # When: 评估策略性能
        with patch.object(strategy_optimizer, '_run_backtest') as mock_backtest:
            mock_backtest.return_value = backtest_results
            
            performance = strategy_optimizer.evaluate_strategy_performance(
                strategy_params,
                sample_price_data,
                start_date="20230101",
                end_date="20231231"
            )
        
        # Then: 验证性能评估结果
        assert "sharpe_ratio" in performance
        assert "max_drawdown" in performance
        assert "total_return" in performance
        assert "win_rate" in performance
        assert "calmar_ratio" in performance
        assert performance["sharpe_ratio"] >= 0
    
    def test_risk_constraint_validation(self, strategy_optimizer):
        """测试风险约束验证 - 复用ConflictResolutionEngine"""
        # Given: 策略参数和性能指标
        strategy_params = {
            "position_size": 0.8,
            "leverage": 2.5,
            "stop_loss": 0.03
        }
        
        performance_metrics = {
            "sharpe_ratio": 1.5,
            "max_drawdown": 0.12,
            "var_95": 0.04,
            "leverage": 2.5
        }
        
        # When: 验证风险约束
        with patch.object(strategy_optimizer.conflict_resolver, 'detect_conflicts') as mock_detect:
            mock_detect.return_value = {
                "success": True,
                "has_conflicts": True,
                "conflicts": [
                    {
                        "constraint": "min_sharpe_ratio",
                        "required": 1.0,
                        "actual": 1.5,
                        "status": "satisfied"
                    },
                    {
                        "constraint": "max_drawdown",
                        "required": 0.15,
                        "actual": 0.12,
                        "status": "satisfied"
                    }
                ]
            }
            
            validation_result = strategy_optimizer.validate_risk_constraints(
                strategy_params,
                performance_metrics
            )
        
        # Then: 验证约束检查结果
        assert validation_result["constraints_satisfied"] is True
        assert len(validation_result["constraint_details"]) > 0
    
    def test_multi_objective_optimization(self, strategy_optimizer, sample_strategy_params):
        """测试多目标优化"""
        # Given: 多目标优化配置
        multi_objective_config = {
            "algorithm": "nsga_ii",  # 非支配排序遗传算法
            "strategy_params": sample_strategy_params,
            "objectives": [
                {"name": "sharpe_ratio", "direction": "maximize", "weight": 0.4},
                {"name": "max_drawdown", "direction": "minimize", "weight": 0.3},
                {"name": "calmar_ratio", "direction": "maximize", "weight": 0.3}
            ],
            "population_size": 100,
            "max_generations": 150
        }
        
        # When: 执行多目标优化
        with patch.object(strategy_optimizer, '_run_multi_objective_optimization') as mock_moo:
            mock_moo.return_value = {
                "pareto_front": [
                    {
                        "parameters": {"lookback_period": 15, "entry_threshold": 2.2},
                        "objectives": {"sharpe_ratio": 2.1, "max_drawdown": 0.08, "calmar_ratio": 3.5}
                    },
                    {
                        "parameters": {"lookback_period": 25, "entry_threshold": 1.8},
                        "objectives": {"sharpe_ratio": 1.9, "max_drawdown": 0.06, "calmar_ratio": 4.1}
                    }
                ],
                "hypervolume": 0.85,
                "convergence_generation": 120
            }
            
            result = strategy_optimizer.optimize_multi_objective(multi_objective_config)
        
        # Then: 验证多目标优化结果
        assert result["success"] is True
        assert len(result["pareto_front"]) == 2
        assert result["hypervolume"] > 0
        assert "convergence_generation" in result
    
    def test_parallel_optimization_execution(self, strategy_optimizer, sample_strategy_params):
        """测试并行优化执行"""
        # Given: 并行优化配置
        parallel_config = {
            "optimization_jobs": [
                {
                    "job_id": "job_001",
                    "algorithm": "genetic_algorithm",
                    "strategy_params": sample_strategy_params,
                    "data_period": "2022-01-01 to 2022-12-31"
                },
                {
                    "job_id": "job_002", 
                    "algorithm": "particle_swarm",
                    "strategy_params": sample_strategy_params,
                    "data_period": "2023-01-01 to 2023-12-31"
                }
            ],
            "max_parallel_jobs": 2,
            "priority": "high"
        }
        
        # When: 执行并行优化
        with patch.object(strategy_optimizer.task_manager, 'submit_task') as mock_submit:
            mock_submit.return_value = "task_001"
            
            result = strategy_optimizer.run_parallel_optimization(parallel_config)
        
        # Then: 验证并行任务提交
        assert mock_submit.call_count == 2
        assert result["success"] is True
        assert result["submitted_jobs"] == 2
        assert len(result["task_ids"]) == 2
    
    def test_optimization_task_scheduling(self, strategy_optimizer):
        """测试优化任务调度 - 复用PriorityQueueManager"""
        # Given: 优化任务配置
        task_config = {
            "strategy_name": "momentum_strategy",
            "algorithm": "genetic_algorithm",
            "priority": "high",
            "estimated_duration_hours": 6,
            "resource_requirements": {
                "cpu_cores": 4,
                "memory_gb": 8
            }
        }
        
        # When: 调度优化任务
        with patch.object(strategy_optimizer.task_manager, 'submit_task') as mock_submit:
            mock_submit.return_value = "optimization_task_001"
            
            task_id = strategy_optimizer.schedule_optimization(task_config)
        
        # Then: 验证任务调度
        mock_submit.assert_called_once()
        call_args = mock_submit.call_args
        assert call_args[1]["task_type"] == "strategy_optimization"
        assert call_args[1]["priority"] == TaskPriority.HIGH
        assert task_id == "optimization_task_001"
    
    def test_optimization_progress_monitoring(self, strategy_optimizer):
        """测试优化进度监控"""
        # Given: 正在运行的优化任务
        task_id = "optimization_task_001"
        
        # When: 监控优化进度
        with patch.object(strategy_optimizer.task_manager, 'get_task_status') as mock_status:
            mock_status.return_value = {
                "task_id": task_id,
                "status": "running",
                "progress": {
                    "current_generation": 45,
                    "total_generations": 100,
                    "best_fitness": 1.85,
                    "convergence_rate": 0.02,
                    "estimated_remaining_minutes": 30
                }
            }
            
            progress = strategy_optimizer.get_optimization_progress(task_id)
        
        # Then: 验证进度监控
        assert progress["task_id"] == task_id
        assert progress["status"] == "running"
        assert progress["progress"]["current_generation"] == 45
        assert progress["progress"]["completion_percentage"] == 45.0
    
    def test_optimization_result_validation(self, strategy_optimizer):
        """测试优化结果验证"""
        # Given: 优化结果
        optimization_result = {
            "algorithm": "genetic_algorithm",
            "optimized_parameters": {
                "lookback_period": 18,
                "entry_threshold": 2.3,
                "exit_threshold": 1.1,
                "position_size": 0.65,
                "stop_loss": 0.045
            },
            "performance_metrics": {
                "sharpe_ratio": 2.08,
                "max_drawdown": 0.09,
                "total_return": 0.28,
                "win_rate": 0.62
            },
            "backtest_period": "2023-01-01 to 2023-12-31"
        }
        
        # When: 验证优化结果
        validation_result = strategy_optimizer.validate_optimization_result(optimization_result)
        
        # Then: 验证结果有效性
        assert validation_result["result_valid"] is True
        assert validation_result["parameter_ranges_valid"] is True
        assert validation_result["performance_realistic"] is True
        assert "validation_warnings" in validation_result
    
    def test_walk_forward_optimization(self, strategy_optimizer, sample_price_data, sample_strategy_params):
        """测试走向前优化"""
        # Given: 走向前优化配置
        walk_forward_config = {
            "strategy_params": sample_strategy_params,
            "optimization_window_months": 6,
            "testing_window_months": 3,
            "step_size_months": 1,
            "start_date": "2023-01-01",
            "end_date": "2023-12-31"
        }
        
        # When: 执行走向前优化
        with patch.object(strategy_optimizer, '_run_walk_forward_analysis') as mock_wf:
            mock_wf.return_value = {
                "optimization_periods": 7,
                "average_performance": {
                    "sharpe_ratio": 1.75,
                    "max_drawdown": 0.11,
                    "stability_score": 0.82
                },
                "performance_consistency": 0.78,
                "out_of_sample_results": [
                    {"period": "2023-07-01_2023-09-30", "sharpe_ratio": 1.65},
                    {"period": "2023-08-01_2023-10-31", "sharpe_ratio": 1.82}
                ]
            }
            
            result = strategy_optimizer.run_walk_forward_optimization(walk_forward_config)
        
        # Then: 验证走向前优化结果
        assert result["success"] is True
        assert result["optimization_periods"] == 7
        assert result["performance_consistency"] > 0.7
        assert len(result["out_of_sample_results"]) > 0
    
    def test_optimization_algorithm_comparison(self, strategy_optimizer, sample_strategy_params):
        """测试优化算法对比"""
        # Given: 算法对比配置
        comparison_config = {
            "strategy_params": sample_strategy_params,
            "algorithms": ["genetic_algorithm", "particle_swarm", "simulated_annealing"],
            "evaluation_metrics": ["best_fitness", "convergence_speed", "stability"],
            "runs_per_algorithm": 5
        }
        
        # When: 对比不同算法
        with patch.object(strategy_optimizer, '_run_algorithm_comparison') as mock_compare:
            mock_compare.return_value = {
                "comparison_results": {
                    "genetic_algorithm": {
                        "average_fitness": 1.95,
                        "convergence_generations": 85,
                        "stability_score": 0.88
                    },
                    "particle_swarm": {
                        "average_fitness": 1.88,
                        "convergence_generations": 70,
                        "stability_score": 0.82
                    },
                    "simulated_annealing": {
                        "average_fitness": 1.76,
                        "convergence_generations": 120,
                        "stability_score": 0.75
                    }
                },
                "best_algorithm": "genetic_algorithm",
                "statistical_significance": True
            }
            
            result = strategy_optimizer.compare_optimization_algorithms(comparison_config)
        
        # Then: 验证算法对比结果
        assert result["success"] is True
        assert result["best_algorithm"] == "genetic_algorithm"
        assert result["statistical_significance"] is True
        assert len(result["comparison_results"]) == 3
    
    def test_optimization_parameter_sensitivity_analysis(self, strategy_optimizer):
        """测试参数敏感性分析"""
        # Given: 敏感性分析配置
        sensitivity_config = {
            "base_parameters": {
                "lookback_period": 20,
                "entry_threshold": 2.0,
                "exit_threshold": 1.0
            },
            "parameter_ranges": {
                "lookback_period": {"min": 10, "max": 30, "step": 2},
                "entry_threshold": {"min": 1.5, "max": 2.5, "step": 0.1}
            },
            "performance_metric": "sharpe_ratio"
        }
        
        # When: 执行敏感性分析
        with patch.object(strategy_optimizer, '_run_sensitivity_analysis') as mock_sensitivity:
            mock_sensitivity.return_value = {
                "sensitivity_scores": {
                    "lookback_period": 0.75,  # 高敏感性
                    "entry_threshold": 0.45,  # 中等敏感性
                    "exit_threshold": 0.25    # 低敏感性
                },
                "most_sensitive_parameter": "lookback_period",
                "robust_parameter_ranges": {
                    "lookback_period": {"optimal": 18, "range": [16, 22]},
                    "entry_threshold": {"optimal": 2.1, "range": [1.9, 2.3]}
                }
            }
            
            result = strategy_optimizer.analyze_parameter_sensitivity(sensitivity_config)
        
        # Then: 验证敏感性分析结果
        assert result["success"] is True
        assert result["most_sensitive_parameter"] == "lookback_period"
        assert "robust_parameter_ranges" in result
        assert result["sensitivity_scores"]["lookback_period"] > 0.5


class TestOptimizationStrategy:
    """优化策略测试 - 基于RoutingStrategy模式"""
    
    @pytest.fixture
    def optimization_strategy(self):
        """创建优化策略实例"""
        return OptimizationStrategy()
    
    def test_optimization_strategy_execution(self, optimization_strategy):
        """测试优化策略执行"""
        # Given: 优化配置和上下文
        optimization_config = {
            "algorithm": "genetic_algorithm",
            "objective": "maximize_sharpe_ratio",
            "parameters": {"lookback_period": 20}
        }
        context = {
            "user_id": "quant_001",
            "priority": "high",
            "resource_budget": {"cpu_hours": 10, "memory_gb": 16}
        }
        
        # When: 执行策略
        with patch.object(optimization_strategy, '_execute_optimization') as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "best_parameters": {"lookback_period": 18},
                "best_fitness": 2.05,
                "optimization_time": 3600
            }
            
            result = optimization_strategy.execute(optimization_config, context)
        
        # Then: 验证策略执行结果
        assert result["success"] is True
        assert result["strategy_type"] == "optimization"
        assert result["best_fitness"] == 2.05
    
    def test_optimization_strategy_config_validation(self, optimization_strategy):
        """测试优化策略配置验证"""
        # Given: 有效配置
        valid_config = {
            "supported_algorithms": ["genetic_algorithm", "particle_swarm"],
            "max_optimization_time_hours": 24,
            "resource_limits": {"cpu_cores": 8, "memory_gb": 32}
        }
        
        # When: 验证配置
        result = optimization_strategy.validate_config(valid_config)
        
        # Then: 验证通过
        assert result is True
        
        # Given: 无效配置
        invalid_config = {
            "supported_algorithms": []  # 空算法列表
        }
        
        # When: 验证无效配置
        result = optimization_strategy.validate_config(invalid_config)
        
        # Then: 验证失败
        assert result is False


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])