#!/usr/bin/env python3
"""
冲突解决引擎测试套件

测试功能：
1. 多种冲突解决策略 (覆盖/合并/忽略)
2. 用户自定义冲突处理规则
3. 冲突报告和审计功能
4. 实时冲突通知和交互
5. 跨平台兼容性
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
from datetime import datetime
import asyncio

from src.storage.conflict_resolution_engine import ConflictResolutionEngine
from src.storage.unified_storage_manager import UnifiedStorageManager


class TestConflictResolutionEngine:
    """冲突解决引擎测试类"""

    @pytest.fixture
    def temp_dir(self):
        """创建临时测试目录"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def conflict_config(self):
        """冲突解决配置"""
        return {
            'conflict_resolution': {
                'default_strategy': 'interactive',
                'timeout_seconds': 30,
                'auto_resolve_threshold': 0.95,
                'enable_user_notification': True,
                'audit_all_conflicts': True
            },
            'resolution_strategies': {
                'overwrite': {
                    'priority_rules': {
                        'source_priority': {'tushare': 1, 'csv': 2, 'manual': 0},
                        'freshness_weight': 0.3,
                        'quality_weight': 0.7
                    }
                },
                'merge': {
                    'merge_rules': {
                        'numeric_fields': 'average',
                        'string_fields': 'concatenate',
                        'datetime_fields': 'latest'
                    }
                },
                'skip': {
                    'skip_conditions': {
                        'low_quality_threshold': 0.5,
                        'outdated_threshold_hours': 24
                    }
                }
            }
        }

    @pytest.fixture
    def conflict_engine(self, conflict_config, temp_dir):
        """创建冲突解决引擎实例"""
        storage_manager = Mock(spec=UnifiedStorageManager)
        return ConflictResolutionEngine(conflict_config, storage_manager)

    @pytest.fixture
    def existing_data(self):
        """现有数据"""
        return pd.DataFrame({
            'contract_code': ['FU2403', 'FU2404', 'CU2403'],
            'trade_date': ['2024-01-15', '2024-01-15', '2024-01-15'],
            'open': [3000.0, 3050.0, 70000.0],
            'high': [3020.0, 3080.0, 70500.0],
            'low': [2990.0, 3040.0, 69800.0],
            'close': [3010.0, 3070.0, 70200.0],
            'volume': [125000, 128000, 85000],
            'amount': [3756000000, 3925600000, 5964000000],
            'source': ['tushare', 'tushare', 'tushare'],
            'update_time': ['2024-01-15 15:00:00', '2024-01-15 15:00:00', '2024-01-15 15:00:00']
        })

    @pytest.fixture
    def new_data_with_conflicts(self):
        """有冲突的新数据"""
        return pd.DataFrame({
            'contract_code': ['FU2403', 'FU2404', 'AL2403'],  # FU2403和FU2404冲突，AL2403新增
            'trade_date': ['2024-01-15', '2024-01-15', '2024-01-15'],
            'open': [3005.0, 3055.0, 18500.0],  # 与现有数据不同
            'high': [3025.0, 3085.0, 18650.0],
            'low': [2995.0, 3045.0, 18450.0],
            'close': [3015.0, 3075.0, 18600.0],
            'volume': [126000, 129000, 95000],
            'amount': [3780000000, 3950000000, 1767000000],
            'source': ['csv', 'csv', 'csv'],
            'update_time': ['2024-01-15 16:00:00', '2024-01-15 16:00:00', '2024-01-15 16:00:00']
        })

    def test_init_conflict_engine(self, conflict_engine):
        """测试冲突解决引擎初始化"""
        assert conflict_engine is not None
        assert hasattr(conflict_engine, 'strategies')
        assert hasattr(conflict_engine, 'notification_manager')
        assert hasattr(conflict_engine, 'audit_logger')
        assert hasattr(conflict_engine, 'user_interaction_handler')

    def test_conflict_detection(self, conflict_engine, existing_data, new_data_with_conflicts):
        """测试冲突检测"""
        # 测试基本冲突检测
        conflicts = conflict_engine.detect_conflicts(
            existing_data=existing_data,
            new_data=new_data_with_conflicts,
            match_keys=['contract_code', 'trade_date']
        )
        
        assert conflicts['has_conflicts'] is True
        assert len(conflicts['conflicts']) == 2  # FU2403和FU2404冲突
        assert len(conflicts['new_records']) == 1  # AL2403新增
        
        # 验证冲突详情
        conflict_details = conflicts['conflicts']
        fu2403_conflict = next(c for c in conflict_details if c['match_key'] == ('FU2403', '2024-01-15'))
        
        assert fu2403_conflict['field_conflicts']['open']['existing'] == 3000.0
        assert fu2403_conflict['field_conflicts']['open']['new'] == 3005.0
        assert fu2403_conflict['conflict_type'] == 'value_mismatch'
        
        # 测试复杂匹配键
        complex_conflicts = conflict_engine.detect_conflicts(
            existing_data=existing_data,
            new_data=new_data_with_conflicts,
            match_keys=['contract_code', 'trade_date'],
            ignore_fields=['update_time', 'source']
        )
        
        assert 'ignored_fields' in complex_conflicts
        assert complex_conflicts['ignored_fields'] == ['update_time', 'source']

    def test_conflict_classification(self, conflict_engine, existing_data, new_data_with_conflicts):
        """测试冲突分类"""
        conflicts = conflict_engine.detect_conflicts(
            existing_data=existing_data,
            new_data=new_data_with_conflicts,
            match_keys=['contract_code', 'trade_date']
        )
        
        # 测试冲突严重程度分类
        classified_conflicts = conflict_engine.classify_conflicts(conflicts['conflicts'])
        
        assert 'critical' in classified_conflicts
        assert 'major' in classified_conflicts
        assert 'minor' in classified_conflicts
        
        # 验证分类逻辑
        for severity, conflict_list in classified_conflicts.items():
            for conflict in conflict_list:
                assert 'severity_score' in conflict
                assert 'classification_reason' in conflict
                
                if severity == 'critical':
                    assert conflict['severity_score'] >= 0.8
                elif severity == 'major':
                    assert 0.5 <= conflict['severity_score'] < 0.8
                else:  # minor
                    assert conflict['severity_score'] < 0.5

    def test_overwrite_strategy(self, conflict_engine, existing_data, new_data_with_conflicts):
        """测试覆盖策略"""
        conflicts = conflict_engine.detect_conflicts(
            existing_data=existing_data,
            new_data=new_data_with_conflicts,
            match_keys=['contract_code', 'trade_date']
        )
        
        # 测试基于源优先级的覆盖
        resolution_result = conflict_engine.resolve_conflicts(
            conflicts=conflicts['conflicts'],
            strategy='overwrite',
            priority_rules={
                'source_priority': {'tushare': 1, 'csv': 2},  # tushare优先级更高
                'freshness_weight': 0.3,
                'quality_weight': 0.7
            }
        )
        
        assert resolution_result['strategy'] == 'overwrite'
        assert resolution_result['success'] is True
        
        # 验证覆盖结果：由于tushare优先级更高，应该保留原数据
        resolved_data = resolution_result['resolved_data']
        fu2403_row = resolved_data[resolved_data['contract_code'] == 'FU2403'].iloc[0]
        assert fu2403_row['open'] == 3000.0  # 保留原有tushare数据
        assert fu2403_row['source'] == 'tushare'
        
        # 测试基于时间新鲜度的覆盖
        time_based_result = conflict_engine.resolve_conflicts(
            conflicts=conflicts['conflicts'],
            strategy='overwrite',
            priority_rules={
                'freshness_weight': 0.8,  # 更重视时间新鲜度
                'quality_weight': 0.2
            }
        )
        
        # 新数据时间更新，应该被采用
        time_resolved_data = time_based_result['resolved_data']
        fu2403_time_row = time_resolved_data[time_resolved_data['contract_code'] == 'FU2403'].iloc[0]
        assert fu2403_time_row['open'] == 3005.0  # 使用较新的数据
        assert fu2403_time_row['source'] == 'csv'

    def test_merge_strategy(self, conflict_engine, existing_data, new_data_with_conflicts):
        """测试合并策略"""
        conflicts = conflict_engine.detect_conflicts(
            existing_data=existing_data,
            new_data=new_data_with_conflicts,
            match_keys=['contract_code', 'trade_date']
        )
        
        # 测试数值字段平均合并
        merge_result = conflict_engine.resolve_conflicts(
            conflicts=conflicts['conflicts'],
            strategy='merge',
            merge_rules={
                'numeric_fields': 'average',
                'string_fields': 'concatenate',
                'datetime_fields': 'latest'
            }
        )
        
        assert merge_result['strategy'] == 'merge'
        assert merge_result['success'] is True
        
        # 验证合并结果
        merged_data = merge_result['resolved_data']
        fu2403_merged = merged_data[merged_data['contract_code'] == 'FU2403'].iloc[0]
        
        # 开盘价应该是平均值: (3000.0 + 3005.0) / 2 = 3002.5
        assert fu2403_merged['open'] == 3002.5
        
        # 成交量应该是平均值: (125000 + 126000) / 2 = 125500
        assert fu2403_merged['volume'] == 125500
        
        # 测试不同合并规则
        custom_merge_result = conflict_engine.resolve_conflicts(
            conflicts=conflicts['conflicts'],
            strategy='merge',
            merge_rules={
                'numeric_fields': 'max',  # 取最大值
                'string_fields': 'prefer_existing',
                'datetime_fields': 'latest'
            }
        )
        
        custom_merged_data = custom_merge_result['resolved_data']
        fu2403_custom = custom_merged_data[custom_merged_data['contract_code'] == 'FU2403'].iloc[0]
        
        # 应该取开盘价的最大值
        assert fu2403_custom['open'] == max(3000.0, 3005.0)

    def test_skip_strategy(self, conflict_engine, existing_data, new_data_with_conflicts):
        """测试跳过策略"""
        conflicts = conflict_engine.detect_conflicts(
            existing_data=existing_data,
            new_data=new_data_with_conflicts,
            match_keys=['contract_code', 'trade_date']
        )
        
        # 测试跳过低质量数据
        skip_result = conflict_engine.resolve_conflicts(
            conflicts=conflicts['conflicts'],
            strategy='skip',
            skip_conditions={
                'low_quality_threshold': 0.8,
                'prefer_existing_on_tie': True
            }
        )
        
        assert skip_result['strategy'] == 'skip'
        assert skip_result['success'] is True
        
        # 验证跳过结果：应该保留现有数据
        skipped_data = skip_result['resolved_data']
        fu2403_skipped = skipped_data[skipped_data['contract_code'] == 'FU2403'].iloc[0]
        assert fu2403_skipped['open'] == 3000.0  # 保留原有数据
        assert fu2403_skipped['source'] == 'tushare'
        
        # 验证跳过统计
        assert 'skipped_count' in skip_result
        assert 'retained_count' in skip_result
        assert skip_result['skipped_count'] >= 0

    def test_interactive_strategy(self, conflict_engine, existing_data, new_data_with_conflicts):
        """测试交互式策略"""
        conflicts = conflict_engine.detect_conflicts(
            existing_data=existing_data,
            new_data=new_data_with_conflicts,
            match_keys=['contract_code', 'trade_date']
        )
        
        # 模拟用户交互选择
        user_choices = {
            ('FU2403', '2024-01-15'): {
                'action': 'overwrite',
                'reason': 'Trust CSV source for this record'
            },
            ('FU2404', '2024-01-15'): {
                'action': 'merge',
                'merge_rule': 'average',
                'reason': 'Merge both sources'
            }
        }
        
        # 测试交互式解决
        interactive_result = conflict_engine.resolve_conflicts(
            conflicts=conflicts['conflicts'],
            strategy='interactive',
            user_choices=user_choices,
            timeout_seconds=30
        )
        
        assert interactive_result['strategy'] == 'interactive'
        assert interactive_result['success'] is True
        assert interactive_result['user_interaction_count'] == 2
        
        # 验证用户选择被正确应用
        interactive_data = interactive_result['resolved_data']
        
        # FU2403应该被覆盖
        fu2403_interactive = interactive_data[interactive_data['contract_code'] == 'FU2403'].iloc[0]
        assert fu2403_interactive['open'] == 3005.0  # 使用新数据
        
        # FU2404应该被合并
        fu2404_interactive = interactive_data[interactive_data['contract_code'] == 'FU2404'].iloc[0]
        assert fu2404_interactive['open'] == 3052.5  # 平均值

    def test_custom_resolution_rules(self, conflict_engine):
        """测试用户自定义冲突处理规则"""
        # 定义自定义规则
        custom_rule = {
            'rule_id': 'futures_price_validation',
            'conditions': {
                'data_type': 'futures',
                'fields': ['open', 'high', 'low', 'close'],
                'conflict_type': 'value_mismatch'
            },
            'resolution_logic': {
                'validation_checks': [
                    'high >= max(open, close)',
                    'low <= min(open, close)',
                    'all_prices > 0'
                ],
                'resolution_action': 'choose_valid_record',
                'fallback_action': 'request_user_input'
            }
        }
        
        # 添加自定义规则
        add_result = conflict_engine.add_custom_rule(custom_rule)
        assert add_result['success'] is True
        assert add_result['rule_id'] == 'futures_price_validation'
        
        # 测试自定义规则应用
        test_existing = pd.DataFrame({
            'contract_code': ['FU2403'],
            'trade_date': ['2024-01-15'],
            'open': [3000.0],
            'high': [2990.0],  # 无效：high < open
            'low': [2980.0],
            'close': [3010.0]
        })
        
        test_new = pd.DataFrame({
            'contract_code': ['FU2403'],
            'trade_date': ['2024-01-15'],
            'open': [3005.0],
            'high': [3025.0],  # 有效
            'low': [2995.0],
            'close': [3015.0]
        })
        
        custom_conflicts = conflict_engine.detect_conflicts(
            existing_data=test_existing,
            new_data=test_new,
            match_keys=['contract_code', 'trade_date']
        )
        
        custom_result = conflict_engine.resolve_conflicts(
            conflicts=custom_conflicts['conflicts'],
            strategy='custom_rule',
            rule_id='futures_price_validation'
        )
        
        # 应该选择有效的新记录
        assert custom_result['success'] is True
        resolved_record = custom_result['resolved_data'].iloc[0]
        assert resolved_record['high'] == 3025.0  # 选择了有效的新数据

    def test_conflict_notification(self, conflict_engine, existing_data, new_data_with_conflicts):
        """测试实时冲突通知"""
        # 启用通知
        conflict_engine.enable_notifications(
            channels=['email', 'webhook', 'websocket'],
            severity_threshold='major'
        )
        
        conflicts = conflict_engine.detect_conflicts(
            existing_data=existing_data,
            new_data=new_data_with_conflicts,
            match_keys=['contract_code', 'trade_date']
        )
        
        # 测试通知发送
        with patch.object(conflict_engine.notification_manager, 'send_notification') as mock_notify:
            conflict_engine.send_conflict_notifications(conflicts['conflicts'])
            
            # 验证通知被发送
            assert mock_notify.call_count >= 1
            
            # 检查通知内容
            notification_call = mock_notify.call_args[0][0]
            assert 'conflict_count' in notification_call
            assert 'severity_distribution' in notification_call
            assert 'affected_tables' in notification_call
        
        # 测试通知过滤
        filtered_notifications = conflict_engine.get_pending_notifications(
            severity_filter='critical'
        )
        
        assert isinstance(filtered_notifications, list)
        for notification in filtered_notifications:
            assert notification['severity'] == 'critical'

    def test_conflict_audit_logging(self, conflict_engine, existing_data, new_data_with_conflicts):
        """测试冲突审计和日志"""
        # 启用详细审计
        conflict_engine.enable_audit_logging(
            level='detailed',
            include_data_diff=True
        )
        
        conflicts = conflict_engine.detect_conflicts(
            existing_data=existing_data,
            new_data=new_data_with_conflicts,
            match_keys=['contract_code', 'trade_date']
        )
        
        resolution_result = conflict_engine.resolve_conflicts(
            conflicts=conflicts['conflicts'],
            strategy='overwrite'
        )
        
        # 获取审计日志
        audit_logs = conflict_engine.get_audit_logs(
            operation_type='conflict_resolution',
            time_range='last_hour'
        )
        
        assert len(audit_logs) >= 1
        
        latest_log = audit_logs[0]
        assert 'timestamp' in latest_log
        assert 'operation' in latest_log
        assert 'strategy_used' in latest_log
        assert 'conflicts_resolved' in latest_log
        assert 'data_before' in latest_log
        assert 'data_after' in latest_log
        
        # 测试审计报告生成
        audit_report = conflict_engine.generate_audit_report(
            start_date='2024-01-15',
            end_date='2024-01-16'
        )
        
        assert 'total_conflicts' in audit_report
        assert 'resolution_strategy_distribution' in audit_report
        assert 'success_rate' in audit_report
        assert 'average_resolution_time' in audit_report

    def test_performance_optimization(self, conflict_engine):
        """测试性能优化"""
        # 生成大量测试数据
        large_existing = pd.DataFrame({
            'id': range(10000),
            'value': [f'value_{i}' for i in range(10000)],
            'timestamp': ['2024-01-15'] * 10000
        })
        
        large_new = pd.DataFrame({
            'id': range(5000, 15000),  # 5000个重叠记录
            'value': [f'new_value_{i}' for i in range(5000, 15000)],
            'timestamp': ['2024-01-16'] * 10000
        })
        
        # 测试批量冲突检测性能
        start_time = datetime.now()
        large_conflicts = conflict_engine.detect_conflicts(
            existing_data=large_existing,
            new_data=large_new,
            match_keys=['id'],
            optimize_for_performance=True
        )
        detection_time = (datetime.now() - start_time).total_seconds()
        
        assert detection_time < 10.0  # 应该在10秒内完成
        assert large_conflicts['has_conflicts'] is True
        assert len(large_conflicts['conflicts']) == 5000  # 5000个冲突
        
        # 测试并行冲突解决
        start_time = datetime.now()
        parallel_result = conflict_engine.resolve_conflicts_parallel(
            conflicts=large_conflicts['conflicts'],
            strategy='overwrite',
            batch_size=1000,
            max_workers=4
        )
        resolution_time = (datetime.now() - start_time).total_seconds()
        
        assert parallel_result['success'] is True
        assert resolution_time < detection_time  # 并行处理应该更快
        assert parallel_result['batch_count'] == 5  # 5个批次

    def test_cross_platform_compatibility(self, conflict_engine):
        """测试跨平台兼容性"""
        import platform
        current_platform = platform.system()
        
        # 测试路径处理
        test_paths = {
            'Windows': r'C:\data\conflicts\report.json',
            'Linux': '/data/conflicts/report.json',
            'Darwin': '/data/conflicts/report.json'
        }
        
        normalized_path = conflict_engine.normalize_file_path(
            test_paths.get(current_platform, '/tmp/report.json')
        )
        
        assert isinstance(normalized_path, Path)
        assert normalized_path.is_absolute()
        
        # 测试字符编码处理
        chinese_conflict_data = pd.DataFrame({
            '合约代码': ['螺纹钢2403', '铜2403'],
            '交易日期': ['2024-01-15', '2024-01-15'],
            '开盘价': [3000.0, 70000.0]
        })
        
        encoding_result = conflict_engine.handle_unicode_conflicts(
            data=chinese_conflict_data,
            encoding='utf-8'
        )
        
        assert encoding_result['success'] is True
        assert 'normalized_data' in encoding_result

    def test_memory_management(self, conflict_engine):
        """测试内存管理"""
        # 测试大数据集的内存效率处理
        def generate_large_dataset(size):
            return pd.DataFrame({
                'id': range(size),
                'data': [f'data_{i}' * 100 for i in range(size)],  # 大字符串
                'timestamp': ['2024-01-15'] * size
            })
        
        # 启用内存监控
        conflict_engine.enable_memory_monitoring()
        
        # 处理大数据集
        large_data_1 = generate_large_dataset(50000)
        large_data_2 = generate_large_dataset(50000)
        
        memory_before = conflict_engine.get_memory_usage()
        
        # 使用流式处理模式
        streaming_result = conflict_engine.detect_conflicts_streaming(
            existing_data=large_data_1,
            new_data=large_data_2,
            chunk_size=5000,
            match_keys=['id']
        )
        
        memory_after = conflict_engine.get_memory_usage()
        
        # 验证内存使用控制
        memory_growth = memory_after['used_mb'] - memory_before['used_mb']
        assert memory_growth < 1000  # 内存增长应控制在1GB以内
        
        assert streaming_result['success'] is True
        assert 'processed_chunks' in streaming_result

    def test_error_handling_and_recovery(self, conflict_engine):
        """测试错误处理和恢复"""
        # 测试数据格式错误处理
        invalid_data = pd.DataFrame({
            'invalid_column': [None, 'corrupted', {}]
        })
        
        error_result = conflict_engine.detect_conflicts(
            existing_data=invalid_data,
            new_data=invalid_data,
            match_keys=['nonexistent_key']
        )
        
        assert error_result['success'] is False
        assert 'error_code' in error_result
        assert 'error_message' in error_result
        
        # 测试部分失败的容错处理
        mixed_data = pd.DataFrame({
            'id': [1, 2, None, 4],  # 包含None值
            'value': ['a', 'b', 'c', 'd']
        })
        
        tolerant_result = conflict_engine.detect_conflicts(
            existing_data=mixed_data,
            new_data=mixed_data,
            match_keys=['id'],
            error_tolerance='skip_invalid_rows'
        )
        
        assert tolerant_result['success'] is True
        assert 'skipped_rows' in tolerant_result
        assert tolerant_result['skipped_rows'] >= 1  # 至少跳过了None值行