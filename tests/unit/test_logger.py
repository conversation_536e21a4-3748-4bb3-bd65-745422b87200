#!/usr/bin/env python3
"""
日志管理器单元测试
"""

import pytest
import tempfile
import shutil
import logging
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import os

from src.utils.logger import (
    AquaLoggerManager,
    ColoredFormatter,
    get_logger_manager,
    get_logger,
    setup_logging,
    log_function_call,
    log_performance
)


class TestColoredFormatter:
    """彩色格式化器测试类"""
    
    def test_colored_formatter_creation(self):
        """测试彩色格式化器创建"""
        formatter = ColoredFormatter("%(levelname)s - %(message)s")
        assert formatter is not None
        assert hasattr(formatter, 'COLORS')
        assert 'DEBUG' in formatter.COLORS
        assert 'INFO' in formatter.COLORS
        assert 'ERROR' in formatter.COLORS
    
    def test_colored_format_console(self):
        """测试控制台彩色格式"""
        formatter = ColoredFormatter("%(levelname)s - %(message)s")
        
        # 创建日志记录
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Test message",
            args=(),
            exc_info=None
        )
        
        # 标记为控制台输出
        record.stream = "stdout"
        
        formatted = formatter.format(record)
        
        # 验证包含颜色代码
        assert '\033[32m' in formatted  # 绿色
        assert '\033[0m' in formatted   # 重置
        assert 'INFO - Test message' in formatted
    
    def test_colored_format_file(self):
        """测试文件输出无颜色格式"""
        formatter = ColoredFormatter("%(levelname)s - %(message)s")
        
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Test message",
            args=(),
            exc_info=None
        )
        
        # 不标记为控制台输出
        formatted = formatter.format(record)
        
        # 验证不包含颜色代码
        assert '\033[32m' not in formatted
        assert '\033[0m' not in formatted
        assert formatted == 'INFO - Test message'


class TestAquaLoggerManager:
    """AQUA日志管理器测试类"""
    
    @pytest.fixture
    def temp_config(self):
        """创建临时配置"""
        temp_dir = tempfile.mkdtemp()
        config_path = Path(temp_dir) / "settings.toml"
        
        config_content = f"""
[dev]
[dev.logging]
level = "DEBUG"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file_path = "{temp_dir}/logs/test_{{date}}.log"
max_file_size = "1MB"
backup_count = 3
"""
        
        with open(config_path, "w") as f:
            f.write(config_content)
        
        yield str(config_path), temp_dir
        
        # 清理
        shutil.rmtree(temp_dir)
    
    def test_logger_manager_creation(self, temp_config):
        """测试日志管理器创建"""
        config_path, temp_dir = temp_config
        
        manager = AquaLoggerManager(config_path, "dev")
        
        assert manager.config is not None
        assert manager.environment == "dev"
        assert manager.log_level == logging.DEBUG
        assert manager.loggers == {}
        assert "test_{date}.log" in manager.log_file_path
    
    def test_get_logger(self, temp_config):
        """测试获取logger"""
        config_path, temp_dir = temp_config
        manager = AquaLoggerManager(config_path, "dev")
        
        logger = manager.get_logger("test_logger")
        
        assert logger is not None
        assert logger.name == "test_logger"
        assert logger.level == logging.DEBUG
        assert "test_logger" in manager.loggers
        assert len(logger.handlers) >= 1  # 至少有控制台handler
    
    def test_get_same_logger_twice(self, temp_config):
        """测试重复获取同一logger"""
        config_path, temp_dir = temp_config
        manager = AquaLoggerManager(config_path, "dev")
        
        logger1 = manager.get_logger("test_logger")
        logger2 = manager.get_logger("test_logger")
        
        assert logger1 is logger2
        assert len(manager.loggers) == 1
    
    def test_parse_file_size(self, temp_config):
        """测试文件大小解析"""
        config_path, temp_dir = temp_config
        manager = AquaLoggerManager(config_path, "dev")
        
        assert manager._parse_file_size("1024") == 1024
        assert manager._parse_file_size("1KB") == 1024
        assert manager._parse_file_size("1MB") == 1024 * 1024
        assert manager._parse_file_size("1GB") == 1024 * 1024 * 1024
        assert manager._parse_file_size("2mb") == 2 * 1024 * 1024
    
    def test_create_request_logger(self, temp_config):
        """测试创建请求logger"""
        config_path, temp_dir = temp_config
        manager = AquaLoggerManager(config_path, "dev")
        
        request_id = "test_request_123"
        logger = manager.create_request_logger(request_id)
        
        assert logger is not None
        assert logger.name == f"request_{request_id}"
        assert len(logger.filters) > 0
    
    def test_get_log_statistics(self, temp_config):
        """测试获取日志统计"""
        config_path, temp_dir = temp_config
        manager = AquaLoggerManager(config_path, "dev")
        
        # 创建一些logger
        manager.get_logger("logger1")
        manager.get_logger("logger2")
        
        stats = manager.get_log_statistics()
        
        assert stats["total_loggers"] == 2
        assert stats["log_level"] == "DEBUG"
        assert stats["environment"] == "dev"
        assert "logger1" in stats["loggers"]
        assert "logger2" in stats["loggers"]
        assert "timestamp" in stats
    
    def test_set_log_level(self, temp_config):
        """测试动态设置日志级别"""
        config_path, temp_dir = temp_config
        manager = AquaLoggerManager(config_path, "dev")
        
        logger = manager.get_logger("test_logger")
        
        # 初始级别是DEBUG
        assert logger.level == logging.DEBUG
        
        # 设置为INFO
        manager.set_log_level("INFO")
        assert manager.log_level == logging.INFO
        assert logger.level == logging.INFO
        
        # 设置为ERROR
        manager.set_log_level("ERROR")
        assert manager.log_level == logging.ERROR
        assert logger.level == logging.ERROR
    
    def test_cleanup_old_logs(self, temp_config):
        """测试清理旧日志文件"""
        config_path, temp_dir = temp_config
        manager = AquaLoggerManager(config_path, "dev")
        
        # 创建日志目录
        log_dir = Path(temp_dir) / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # 创建一些测试日志文件
        old_log = log_dir / "old.log"
        new_log = log_dir / "new.log"
        
        old_log.touch()
        new_log.touch()
        
        # 修改文件时间
        old_time = datetime.now().timestamp() - (40 * 24 * 60 * 60)  # 40天前
        os.utime(old_log, (old_time, old_time))
        
        # 清理30天前的日志
        deleted_files = manager.cleanup_old_logs(days=30)
        
        assert len(deleted_files) == 1
        assert str(old_log) in deleted_files
        assert not old_log.exists()
        assert new_log.exists()
    
    def test_logger_with_file_handler(self, temp_config):
        """测试文件日志处理器"""
        config_path, temp_dir = temp_config
        manager = AquaLoggerManager(config_path, "dev")
        
        logger = manager.get_logger("test_logger")
        
        # 写入一些日志
        logger.info("Test info message")
        logger.error("Test error message")
        
        # 检查日志文件是否创建
        log_dir = Path(temp_dir) / "logs"
        log_files = list(log_dir.glob("*.log"))
        
        assert len(log_files) > 0
        
        # 检查日志内容
        log_content = log_files[0].read_text()
        assert "Test info message" in log_content
        assert "Test error message" in log_content
    
    def test_logger_different_environments(self, temp_config):
        """测试不同环境的日志配置"""
        config_path, temp_dir = temp_config
        
        # 开发环境
        dev_manager = AquaLoggerManager(config_path, "dev")
        dev_logger = dev_manager.get_logger("test")
        
        # 生产环境（使用默认配置）
        prod_manager = AquaLoggerManager(config_path, "prod")
        prod_logger = prod_manager.get_logger("test")
        
        assert dev_manager.environment == "dev"
        assert prod_manager.environment == "prod"
        
        # 两个环境的logger应该不同
        assert dev_logger != prod_logger


class TestUtilityFunctions:
    """工具函数测试类"""
    
    def test_get_logger_manager_singleton(self):
        """测试日志管理器单例"""
        manager1 = get_logger_manager()
        manager2 = get_logger_manager()
        
        assert manager1 is manager2
    
    def test_get_logger_function(self):
        """测试获取logger函数"""
        logger = get_logger("test_function_logger")
        
        assert logger is not None
        assert logger.name == "test_function_logger"
        assert isinstance(logger, logging.Logger)
    
    def test_setup_logging_function(self):
        """测试设置日志函数"""
        manager = setup_logging()
        
        assert manager is not None
        assert isinstance(manager, AquaLoggerManager)
        
        # 验证全局管理器被设置
        assert get_logger_manager() is manager
    
    def test_log_function_call_decorator(self):
        """测试函数调用日志装饰器"""
        logger = get_logger("test_decorator")
        
        # 模拟logger的debug方法
        with patch.object(logger, 'debug') as mock_debug:
            @log_function_call(logger)
            def test_function(x, y):
                return x + y
            
            result = test_function(1, 2)
            
            assert result == 3
            assert mock_debug.call_count == 2  # 开始和结束各一次
            
            # 检查日志消息
            calls = mock_debug.call_args_list
            assert "调用函数 test_function 开始" in calls[0][0][0]
            assert "调用函数 test_function 成功" in calls[1][0][0]
    
    def test_log_function_call_decorator_with_exception(self):
        """测试函数调用装饰器异常处理"""
        logger = get_logger("test_decorator_exception")
        
        with patch.object(logger, 'debug') as mock_debug:
            with patch.object(logger, 'error') as mock_error:
                @log_function_call(logger)
                def test_function():
                    raise ValueError("Test error")
                
                with pytest.raises(ValueError):
                    test_function()
                
                # 验证日志调用
                mock_debug.assert_called_once()
                mock_error.assert_called_once()
                
                # 检查错误日志消息
                error_call = mock_error.call_args_list[0]
                assert "调用函数 test_function 失败" in error_call[0][0]
                assert "Test error" in error_call[0][0]
    
    def test_log_performance_context_manager(self):
        """测试性能日志上下文管理器"""
        logger = get_logger("test_performance")
        
        with patch.object(logger, 'info') as mock_info:
            with log_performance(logger, "测试操作"):
                pass
            
            assert mock_info.call_count == 2
            
            # 检查日志消息
            calls = mock_info.call_args_list
            assert "开始执行 测试操作" in calls[0][0][0]
            assert "完成执行 测试操作" in calls[1][0][0]
            assert "耗时" in calls[1][0][0]
    
    def test_log_performance_context_manager_with_exception(self):
        """测试性能日志上下文管理器异常处理"""
        logger = get_logger("test_performance_exception")
        
        with patch.object(logger, 'info') as mock_info:
            with patch.object(logger, 'error') as mock_error:
                with pytest.raises(ValueError):
                    with log_performance(logger, "测试操作"):
                        raise ValueError("Test error")
                
                # 验证开始日志
                mock_info.assert_called_once()
                assert "开始执行 测试操作" in mock_info.call_args[0][0]
                
                # 验证错误日志
                mock_error.assert_called_once()
                error_call = mock_error.call_args[0][0]
                assert "执行失败 测试操作" in error_call
                assert "Test error" in error_call


class TestIntegration:
    """集成测试"""
    
    def test_logger_integration(self):
        """测试日志集成"""
        # 创建管理器
        manager = setup_logging()
        
        # 获取logger
        logger = get_logger("integration_test")
        
        # 测试不同级别的日志
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        
        # 验证logger存在于管理器中
        assert "integration_test" in manager.loggers
        
        # 获取统计信息
        stats = manager.get_log_statistics()
        assert stats["total_loggers"] >= 1
        assert "integration_test" in stats["loggers"]
    
    def test_multiple_loggers(self):
        """测试多个logger"""
        manager = get_logger_manager()
        
        # 创建多个logger
        logger1 = get_logger("module1")
        logger2 = get_logger("module2")
        logger3 = get_logger("module3")
        
        # 验证都是不同的logger
        assert logger1 != logger2
        assert logger2 != logger3
        assert logger1 != logger3
        
        # 验证都在管理器中
        assert "module1" in manager.loggers
        assert "module2" in manager.loggers
        assert "module3" in manager.loggers
    
    def test_logger_with_real_config(self):
        """测试真实配置的logger"""
        try:
            # 尝试使用真实配置文件
            logger = get_logger("real_config_test")
            
            # 测试日志记录
            logger.info("Real config test message")
            
            # 验证logger配置
            assert logger is not None
            assert logger.name == "real_config_test"
            assert len(logger.handlers) > 0
            
        except Exception as e:
            # 如果配置文件不存在，这是正常的
            pytest.skip(f"Real config file not available: {e}")


@pytest.mark.parametrize("log_level", ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
def test_different_log_levels(log_level):
    """测试不同日志级别"""
    logger = get_logger(f"test_level_{log_level}")
    
    # 获取日志级别对应的方法
    log_method = getattr(logger, log_level.lower())
    
    # 测试日志记录
    log_method(f"Test {log_level} message")
    
    # 验证logger存在
    assert logger is not None
    assert logger.name == f"test_level_{log_level}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])