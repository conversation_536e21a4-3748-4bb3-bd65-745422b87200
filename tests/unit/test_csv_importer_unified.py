#!/usr/bin/env python3
"""
CSV导入器统一测试
整合了业务映射和数据验证的完整测试

测试覆盖：
- 统一验证器功能
- 业务表映射功能
- 数据加载器功能
- 重构后的CSV导入器
"""

import pytest
import tempfile
import pandas as pd
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from src.data_import.csv_importer import CSVImporter
from src.data_import.validators.csv_validator import CSVValidator
from src.data_import.mappers.business_table_mapper import BusinessTableMapper
from src.data_import.loaders.data_loader import DataLoader
from tests.fixtures.csv_test_data import CSVTestDataFactory


class TestCSVValidator:
    """CSV验证器测试"""
    
    @pytest.fixture
    def validator(self):
        """创建CSV验证器实例"""
        return CSVValidator(encoding="utf-8", delimiter=",", max_file_size_mb=200)
    
    def test_validate_valid_file(self, validator):
        """测试验证有效文件"""
        # 创建有效的测试文件
        test_data = CSVTestDataFactory.create_futures_15min_data()
        temp_file = CSVTestDataFactory.create_temp_csv_file(test_data, "valid_test")
        
        try:
            result = validator.validate_file_format(temp_file)
            
            assert result["valid"] == True
            assert len(result["errors"]) == 0
            assert "file_info" in result
            
        finally:
            CSVTestDataFactory.cleanup_temp_files([temp_file])
    
    def test_validate_invalid_file(self, validator):
        """测试验证无效文件"""
        # 创建无效的测试文件
        invalid_file = CSVTestDataFactory.create_invalid_csv_file()
        
        try:
            result = validator.validate_file_format(invalid_file)
            
            # 由于内容不一致，可能会有警告但不一定无效
            assert "file_info" in result
            
        finally:
            CSVTestDataFactory.cleanup_temp_files([invalid_file])
    
    def test_validate_nonexistent_file(self, validator):
        """测试验证不存在的文件"""
        nonexistent_file = Path("/tmp/nonexistent_file.csv")
        
        result = validator.validate_file_format(nonexistent_file)
        
        assert result["valid"] == False
        assert "文件不存在" in result["errors"]
    
    def test_validate_futures_data_compliance(self, validator):
        """测试期货数据合规性验证"""
        # 创建合规的期货数据
        valid_data = pd.DataFrame({
            'contract_code': ['AL', 'AL'],
            'trade_datetime': pd.to_datetime(['2024-01-01 09:00:00', '2024-01-01 09:15:00']),
            'open': [3800.0, 3805.0],
            'high': [3810.0, 3815.0],
            'low': [3795.0, 3800.0],
            'close': [3805.0, 3810.0],
            'volume': [1000, 1200],
            'amount': [3800000.0, 4566000.0],
            'open_interest': [50000, 50500]
        })
        
        result = validator.validate_data_compliance(valid_data, "fut_main_contract_kline_15min")
        
        assert result["valid"] == True
        assert len(result["errors"]) == 0
    
    def test_validate_futures_data_with_errors(self, validator):
        """测试包含错误的期货数据验证"""
        # 创建包含错误的期货数据
        invalid_data = pd.DataFrame({
            'contract_code': ['AL', 'INVALID_CODE'],
            'trade_datetime': pd.to_datetime(['2024-01-01 09:00:00', '2024-01-01 09:15:00']),
            'open': [3800.0, -100.0],  # 负价格
            'high': [3810.0, 3815.0],
            'low': [3795.0, 3800.0],
            'close': [3805.0, 3810.0],
            'volume': [1000, 1200],
            'amount': [3800000.0, 4566000.0],
            'open_interest': [50000, 50500]
        })
        
        result = validator.validate_data_compliance(invalid_data, "fut_main_contract_kline_15min")
        
        assert result["valid"] == False
        assert len(result["errors"]) > 0


class TestBusinessTableMapper:
    """业务表映射器测试"""
    
    @pytest.fixture
    def mapper(self):
        """创建业务表映射器实例"""
        return BusinessTableMapper()
    
    def test_map_futures_15min_files(self, mapper):
        """测试期货15分钟K线文件映射"""
        test_cases = [
            ("al_主力合约_15分钟数据.csv", "fut_main_contract_kline_15min"),
            ("ag_主力合约_15分钟数据.csv", "fut_main_contract_kline_15min"),
            ("rb2501_主力合约_15分钟.csv", "fut_main_contract_kline_15min"),
            ("cu_15分钟数据.csv", "fut_main_contract_kline_15min")
        ]
        
        for filename, expected_table in test_cases:
            file_path = Path(filename)
            table_name = mapper.map_file_to_table(file_path)
            assert table_name == expected_table, f"文件 {filename} 映射错误"
    
    def test_map_stock_daily_files(self, mapper):
        """测试股票日K线文件映射"""
        test_cases = [
            ("000001_股票日K线.csv", "stock_kline_daily"),
            ("stock_daily_data.csv", "stock_kline_daily")
        ]
        
        for filename, expected_table in test_cases:
            file_path = Path(filename)
            table_name = mapper.map_file_to_table(file_path)
            assert table_name == expected_table, f"文件 {filename} 映射错误"
    
    def test_map_basic_info_files(self, mapper):
        """测试基础信息文件映射"""
        test_cases = [
            ("期货基础信息.csv", "fut_basic_info"),
            ("股票基础信息.csv", "stock_basic_info")
        ]
        
        for filename, expected_table in test_cases:
            file_path = Path(filename)
            table_name = mapper.map_file_to_table(file_path)
            assert table_name == expected_table, f"文件 {filename} 映射错误"
    
    def test_standardize_futures_columns(self, mapper):
        """测试期货数据字段标准化"""
        # 原始数据（中文字段名）
        raw_data = CSVTestDataFactory.create_futures_15min_data_chinese()
        
        file_path = Path('al_主力合约_15分钟数据.csv')
        standardized_df = mapper.map_columns_to_standard(raw_data, "fut_main_contract_kline_15min", file_path)
        
        # 验证字段映射
        expected_columns = {'contract_code', 'trade_datetime', 'open', 'high', 'low', 'close', 'volume', 'amount', 'open_interest', 'created_at', 'updated_at'}
        actual_columns = set(standardized_df.columns)
        
        assert expected_columns.issubset(actual_columns), f"字段标准化失败，缺少字段: {expected_columns - actual_columns}"
        
        # 验证合约代码
        assert standardized_df['contract_code'].iloc[0] == 'AL', "合约代码生成错误"
        
        # 验证时间字段类型
        assert pd.api.types.is_datetime64_any_dtype(standardized_df['trade_datetime']), "trade_datetime字段类型错误"
    
    def test_standardize_stock_columns(self, mapper):
        """测试股票数据字段标准化"""
        # 原始数据（中文字段名）
        raw_data = CSVTestDataFactory.create_stock_daily_data_chinese()
        
        file_path = Path('000001_股票日K线.csv')
        standardized_df = mapper.map_columns_to_standard(raw_data, "stock_kline_daily", file_path)
        
        # 验证字段映射
        expected_columns = {'symbol', 'trade_date', 'open', 'high', 'low', 'close', 'volume', 'amount', 'created_at', 'updated_at'}
        actual_columns = set(standardized_df.columns)
        
        assert expected_columns.issubset(actual_columns), f"字段标准化失败，缺少字段: {expected_columns - actual_columns}"
    
    def test_is_business_table(self, mapper):
        """测试业务表识别"""
        assert mapper.is_business_table("fut_main_contract_kline_15min") == True
        assert mapper.is_business_table("stock_kline_daily") == True
        assert mapper.is_business_table("random_table") == False
    
    def test_get_supported_table_types(self, mapper):
        """测试获取支持的表类型"""
        supported_types = mapper.get_supported_table_types()
        
        assert "fut_main_contract_kline_15min" in supported_types
        assert "stock_kline_daily" in supported_types
        assert "fut_basic_info" in supported_types
        assert "stock_basic_info" in supported_types


class TestDataLoader:
    """数据加载器测试"""
    
    @pytest.fixture
    def loader(self):
        """创建数据加载器实例"""
        with patch('src.data_import.loaders.data_loader.DuckDBConnectionManager'):
            return DataLoader(environment="test")
    
    def test_load_csv(self, loader):
        """测试CSV文件加载"""
        test_data = CSVTestDataFactory.create_futures_15min_data()
        temp_file = CSVTestDataFactory.create_temp_csv_file(test_data, "load_test")
        
        try:
            df = loader.load_csv(temp_file)
            
            assert len(df) == len(test_data)
            assert list(df.columns) == list(test_data.columns)
            
        finally:
            CSVTestDataFactory.cleanup_temp_files([temp_file])
    
    def test_save_temp_csv(self, loader):
        """测试保存临时CSV文件"""
        test_data = CSVTestDataFactory.create_futures_15min_data()
        
        temp_path = loader.save_temp_csv(test_data, "temp_test_")
        
        try:
            assert temp_path.exists()
            assert temp_path.suffix == '.csv'
            
            # 验证文件内容
            loaded_data = pd.read_csv(temp_path)
            assert len(loaded_data) == len(test_data)
            
        finally:
            loader.cleanup_temp_file(temp_path)
    
    def test_import_to_database_mock(self, loader):
        """测试数据库导入（模拟）"""
        test_data = CSVTestDataFactory.create_futures_15min_data()
        
        # 模拟数据库操作
        loader.connection_manager.table_exists.return_value = False
        loader.connection_manager.create_table_from_csv.return_value = True
        
        result = loader.import_to_database(test_data, "test_table", mode="create")
        
        assert result["success"] == True
        assert result["records_imported"] == len(test_data)
        assert result["table_name"] == "test_table"


class TestCSVImporter:
    """CSV导入器统一测试"""
    
    @pytest.fixture
    def csv_importer(self):
        """创建CSV导入器实例"""
        with patch('src.data_import.csv_importer.DuckDBConnectionManager'), \
             patch('src.data_import.csv_importer.DataLoader') as mock_loader:
            
            importer = CSVImporter(environment="test")
            # 模拟加载器
            mock_loader_instance = Mock()
            mock_loader_instance.load_csv.return_value = CSVTestDataFactory.create_futures_15min_data()
            mock_loader_instance.import_to_database.return_value = {"success": True, "records_imported": 10}
            importer.loader = mock_loader_instance
            
            return importer
    
    def test_detect_table_name(self, csv_importer):
        """测试表名检测"""
        test_cases = [
            ("al_主力合约_15分钟数据.csv", "fut_main_contract_kline_15min"),
            ("000001_股票日K线.csv", "stock_kline_daily"),
            ("期货基础信息.csv", "fut_basic_info"),
            ("unknown_file.csv", "unknown_file")
        ]
        
        for filename, expected_table in test_cases:
            file_path = Path(filename)
            table_name = csv_importer.detect_table_name(file_path)
            assert table_name == expected_table, f"文件 {filename} 表名检测错误"
    
    def test_validate_csv_file(self, csv_importer):
        """测试CSV文件验证"""
        test_data = CSVTestDataFactory.create_futures_15min_data()
        temp_file = CSVTestDataFactory.create_temp_csv_file(test_data, "validation_test")
        
        try:
            result = csv_importer.validate_csv_file(temp_file)
            
            assert result["valid"] == True
            assert len(result["errors"]) == 0
            
        finally:
            CSVTestDataFactory.cleanup_temp_files([temp_file])
    
    def test_import_single_file_business_table(self, csv_importer):
        """测试导入业务表文件"""
        test_data = CSVTestDataFactory.create_futures_15min_data()
        temp_file = CSVTestDataFactory.create_temp_csv_file(test_data, "al_主力合约_15分钟数据")
        
        try:
            # 修改文件名以匹配业务表
            business_file = temp_file.parent / "al_主力合约_15分钟数据.csv"
            temp_file.rename(business_file)
            
            result = csv_importer.import_single_file(business_file)
            
            assert result["success"] == True
            assert result["table_name"] == "fut_main_contract_kline_15min"
            assert result["records_imported"] > 0
            
        finally:
            CSVTestDataFactory.cleanup_temp_files([business_file])
    
    def test_import_single_file_non_business_table(self, csv_importer):
        """测试导入非业务表文件"""
        test_data = pd.DataFrame({
            'col1': [1, 2, 3],
            'col2': ['a', 'b', 'c']
        })
        temp_file = CSVTestDataFactory.create_temp_csv_file(test_data, "unknown_data")
        
        try:
            # 模拟传统数据库操作
            csv_importer.connection_manager.table_exists.return_value = False
            csv_importer.connection_manager.create_table_from_csv.return_value = True
            csv_importer.connection_manager.get_table_count.return_value = 3
            
            result = csv_importer.import_single_file(temp_file)
            
            assert result["success"] == True
            assert result["records_imported"] == 3
            
        finally:
            CSVTestDataFactory.cleanup_temp_files([temp_file])
    
    def test_import_batch_files(self, csv_importer):
        """测试批量导入文件"""
        # 创建测试文件集
        temp_dir = Path(tempfile.mkdtemp())
        test_files = CSVTestDataFactory.create_test_file_set(temp_dir)
        
        try:
            # 模拟数据库操作
            csv_importer.connection_manager.table_exists.return_value = False
            csv_importer.connection_manager.create_table_from_csv.return_value = True
            csv_importer.connection_manager.get_table_count.return_value = 10
            
            file_paths = list(test_files.values())
            result = csv_importer.import_batch_files(file_paths[:3])  # 只测试前3个文件
            
            assert result["success"] == True
            assert result["processed_files"] > 0
            assert result["total_files"] == 3
            
        finally:
            CSVTestDataFactory.cleanup_temp_files(list(test_files.values()))
            # 清理临时目录
            try:
                temp_dir.rmdir()
            except:
                pass
    
    def test_get_component_statistics(self, csv_importer):
        """测试获取组件统计信息"""
        stats = csv_importer.get_component_statistics()
        
        assert "validator_info" in stats
        assert "mapper_info" in stats
        assert "loader_info" in stats
        
        assert "supported_extensions" in stats["validator_info"]
        assert "total_business_tables" in stats["mapper_info"]
    
    def test_compatibility_methods(self, csv_importer):
        """测试兼容性方法"""
        test_data = CSVTestDataFactory.create_futures_15min_data_chinese()
        file_path = Path("al_主力合约_15分钟数据.csv")
        
        # 测试字段标准化兼容性方法
        standardized_df = csv_importer.standardize_columns(test_data, "fut_main_contract_kline_15min", file_path)
        assert 'contract_code' in standardized_df.columns
        
        # 测试数据合规性验证兼容性方法
        compliance = csv_importer.validate_data_compliance(standardized_df, "fut_main_contract_kline_15min")
        assert "valid" in compliance
    
    def test_get_file_preview(self, csv_importer):
        """测试文件预览功能"""
        test_data = CSVTestDataFactory.create_futures_15min_data()
        temp_file = CSVTestDataFactory.create_temp_csv_file(test_data, "preview_test")
        
        try:
            result = csv_importer.get_file_preview(temp_file, max_rows=5)
            
            assert result["success"] == True
            assert len(result["headers"]) > 0
            assert len(result["sample_rows"]) <= 5
            
        finally:
            CSVTestDataFactory.cleanup_temp_files([temp_file])


@pytest.mark.parametrize("filename,expected_table", [
    ("al_主力合约_15分钟数据.csv", "fut_main_contract_kline_15min"),
    ("rb2501_main_contract_15min.csv", "fut_main_contract_kline_15min"),
    ("000001_股票日K线.csv", "stock_kline_daily"),
    ("stock_daily_data.csv", "stock_kline_daily"),
    ("期货基础信息.csv", "fut_basic_info"),
    ("股票基础列表.csv", "stock_basic_info"),
    ("random_file.csv", "random_file")
])
def test_business_table_mapping_patterns(filename, expected_table):
    """测试各种文件名模式的业务表映射"""
    mapper = BusinessTableMapper()
    file_path = Path(filename)
    table_name = mapper.map_file_to_table(file_path)
    assert table_name == expected_table, f"文件 {filename} 映射错误，期望: {expected_table}, 实际: {table_name}"


class TestIntegrationScenarios:
    """集成测试场景"""
    
    def test_complete_import_workflow(self):
        """测试完整的导入工作流"""
        with patch('src.data_import.csv_importer.DuckDBConnectionManager'), \
             patch('src.data_import.csv_importer.DataLoader') as mock_loader:
            
            # 创建CSV导入器
            importer = CSVImporter(environment="test")
            
            # 模拟加载器
            mock_loader_instance = Mock()
            test_data = CSVTestDataFactory.create_futures_15min_data()
            mock_loader_instance.load_csv.return_value = test_data
            mock_loader_instance.import_to_database.return_value = {
                "success": True, 
                "records_imported": len(test_data)
            }
            importer.loader = mock_loader_instance
            
            # 创建测试文件
            temp_file = CSVTestDataFactory.create_temp_csv_file(test_data, "complete_test")
            business_file = temp_file.parent / "al_主力合约_15分钟数据.csv"
            temp_file.rename(business_file)
            
            try:
                # 执行完整导入流程
                result = importer.import_single_file(business_file)
                
                # 验证结果
                assert result["success"] == True
                assert result["table_name"] == "fut_main_contract_kline_15min"
                assert result["records_imported"] == len(test_data)
                
                # 验证组件调用
                mock_loader_instance.load_csv.assert_called_once()
                mock_loader_instance.import_to_database.assert_called_once()
                
            finally:
                CSVTestDataFactory.cleanup_temp_files([business_file])