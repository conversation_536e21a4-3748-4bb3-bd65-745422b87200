#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA依赖管理器单元测试

测试智能依赖管理功能
版本: 1.0.0
创建时间: 2025-08-01
"""

import unittest
from unittest.mock import patch, MagicMock, mock_open
from pathlib import Path
import sys
import json
import tempfile
import shutil
import subprocess

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.dep_manager import (
    DependencyType, InstallMethod, DependencyStatus,
    DependencyInfo, InstallationPlan, DependencyManager
)


class TestEnums(unittest.TestCase):
    """枚举类测试"""
    
    def test_dependency_type_values(self):
        """测试依赖类型枚举值"""
        self.assertEqual(DependencyType.PYTHON.value, "python")
        self.assertEqual(DependencyType.SYSTEM.value, "system")
        self.assertEqual(DependencyType.FRONTEND.value, "frontend")
    
    def test_install_method_values(self):
        """测试安装方法枚举值"""
        self.assertEqual(InstallMethod.PIP.value, "pip")
        self.assertEqual(InstallMethod.UV.value, "uv")
        self.assertEqual(InstallMethod.NPM.value, "npm")
        self.assertEqual(InstallMethod.PNPM.value, "pnpm")
    
    def test_dependency_status_values(self):
        """测试依赖状态枚举值"""
        self.assertEqual(DependencyStatus.INSTALLED.value, "installed")
        self.assertEqual(DependencyStatus.MISSING.value, "missing")
        self.assertEqual(DependencyStatus.OUTDATED.value, "outdated")
        self.assertEqual(DependencyStatus.CONFLICT.value, "conflict")


class TestDataClasses(unittest.TestCase):
    """数据类测试"""
    
    def test_dependency_info_creation(self):
        """测试依赖信息数据类"""
        dep = DependencyInfo(
            name="fastapi",
            type=DependencyType.PYTHON,
            required_version=">=0.100.0",
            installed_version="0.104.1",
            status=DependencyStatus.INSTALLED,
            install_method=InstallMethod.PIP,
            description="高性能Web框架"
        )
        
        self.assertEqual(dep.name, "fastapi")
        self.assertEqual(dep.type, DependencyType.PYTHON)
        self.assertEqual(dep.status, DependencyStatus.INSTALLED)
        self.assertEqual(dep.install_method, InstallMethod.PIP)
        self.assertEqual(dep.conflicts, [])
        self.assertEqual(dep.issues, [])
    
    def test_installation_plan_creation(self):
        """测试安装计划数据类"""
        plan = InstallationPlan()
        
        self.assertEqual(plan.to_install, [])
        self.assertEqual(plan.to_upgrade, [])
        self.assertEqual(plan.conflicts, [])
        self.assertEqual(plan.estimated_time, 0)
        self.assertEqual(plan.total_size, 0.0)


class TestDependencyManager(unittest.TestCase):
    """依赖管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
        
        # 模拟依赖
        self.mock_config = MagicMock()
        self.mock_ui = MagicMock()
        self.mock_paths = MagicMock()
        
        # 设置路径
        self.mock_paths.ROOT = Path(self.temp_dir)
        
        with patch('utils.dep_manager.get_config_manager', return_value=self.mock_config), \
             patch('utils.dep_manager.get_ui', return_value=self.mock_ui), \
             patch('utils.dep_manager.Paths', return_value=self.mock_paths):
            self.manager = DependencyManager()
    
    def test_manager_initialization(self):
        """测试管理器初始化"""
        self.assertIsNotNone(self.manager.config)
        self.assertIsNotNone(self.manager.ui)
        self.assertIsNotNone(self.manager.paths)
        self.assertIsInstance(self.manager.dependency_specs, dict)
        self.assertIsInstance(self.manager.available_installers, dict)
    
    def test_load_default_dependency_specs(self):
        """测试加载默认依赖规格"""
        specs = self.manager.dependency_specs
        
        self.assertIn("python", specs)
        self.assertIn("system", specs)
        self.assertIn("frontend", specs)
        
        # 检查Python依赖
        python_specs = specs["python"]
        self.assertIn("core", python_specs)
        self.assertIn("fastapi", python_specs["core"])
        self.assertIn("uvicorn", python_specs["core"])
        
        # 检查系统工具
        system_specs = specs["system"]
        self.assertIn("required", system_specs)
        self.assertIn("node", system_specs["required"])
        self.assertIn("git", system_specs["required"])
    
    @patch('shutil.which')
    def test_detect_installers(self, mock_which):
        """测试检测安装工具"""
        # 模拟只有pip和npm可用
        mock_which.side_effect = lambda cmd: "/usr/bin/" + cmd if cmd in ["pip", "npm"] else None
        
        installers = self.manager._detect_installers()
        
        self.assertTrue(installers[InstallMethod.PIP])
        self.assertTrue(installers[InstallMethod.NPM])
        self.assertFalse(installers[InstallMethod.UV])
        self.assertFalse(installers[InstallMethod.PNPM])
    
    @patch('shutil.which')
    def test_get_best_python_installer(self, mock_which):
        """测试获取最佳Python安装器"""
        # 测试UV优先
        mock_which.side_effect = lambda cmd: "/usr/bin/" + cmd if cmd == "uv" else None
        self.manager.available_installers = self.manager._detect_installers()
        
        installer = self.manager._get_best_python_installer()
        self.assertEqual(installer, InstallMethod.UV)
        
        # 测试pip回退
        mock_which.side_effect = lambda cmd: "/usr/bin/" + cmd if cmd == "pip" else None
        self.manager.available_installers = self.manager._detect_installers()
        
        installer = self.manager._get_best_python_installer()
        self.assertEqual(installer, InstallMethod.PIP)
    
    def test_get_best_frontend_installer(self):
        """测试获取最佳前端安装器"""
        frontend_path = Path(self.temp_dir) / "frontend"
        frontend_path.mkdir(exist_ok=True)
        
        # 测试pnpm
        (frontend_path / "pnpm-lock.yaml").touch()
        installer = self.manager._get_best_frontend_installer()
        self.assertEqual(installer, InstallMethod.PNPM)
        
        # 测试yarn
        (frontend_path / "pnpm-lock.yaml").unlink()
        (frontend_path / "yarn.lock").touch()
        installer = self.manager._get_best_frontend_installer()
        self.assertEqual(installer, InstallMethod.YARN)
        
        # 测试npm
        (frontend_path / "yarn.lock").unlink()
        installer = self.manager._get_best_frontend_installer()
        self.assertEqual(installer, InstallMethod.NPM)
    
    def test_check_version_compatibility(self):
        """测试版本兼容性检查"""
        # 测试>=要求
        self.assertTrue(self.manager._check_version_compatibility("1.5.0", ">=1.0.0"))
        self.assertFalse(self.manager._check_version_compatibility("0.9.0", ">=1.0.0"))
        
        # 测试^要求（npm风格）
        self.assertTrue(self.manager._check_version_compatibility("1.5.0", "^1.0.0"))
        self.assertFalse(self.manager._check_version_compatibility("2.0.0", "^1.0.0"))
        
        # 测试~要求（npm风格）
        self.assertTrue(self.manager._check_version_compatibility("1.0.5", "~1.0.0"))
        self.assertFalse(self.manager._check_version_compatibility("1.1.0", "~1.0.0"))
        
        # 测试精确匹配
        self.assertTrue(self.manager._check_version_compatibility("1.0.0", "==1.0.0"))
        self.assertFalse(self.manager._check_version_compatibility("1.0.1", "==1.0.0"))
    
    @patch('importlib.util.find_spec')
    @patch('pkg_resources.get_distribution')
    def test_check_python_package_installed(self, mock_get_dist, mock_find_spec):
        """测试检查已安装的Python包"""
        # 模拟包已安装
        mock_find_spec.return_value = MagicMock()
        mock_dist = MagicMock()
        mock_dist.version = "0.104.1"
        mock_get_dist.return_value = mock_dist
        
        dep_info = DependencyInfo(
            name="fastapi",
            type=DependencyType.PYTHON,
            required_version=">=0.100.0"
        )
        
        self.manager._check_python_package(dep_info)
        
        self.assertEqual(dep_info.status, DependencyStatus.INSTALLED)
        self.assertEqual(dep_info.installed_version, "0.104.1")
    
    @patch('importlib.util.find_spec')
    def test_check_python_package_missing(self, mock_find_spec):
        """测试检查缺失的Python包"""
        mock_find_spec.return_value = None
        
        dep_info = DependencyInfo(
            name="nonexistent",
            type=DependencyType.PYTHON
        )
        
        self.manager._check_python_package(dep_info)
        
        self.assertEqual(dep_info.status, DependencyStatus.MISSING)
    
    @patch('shutil.which')
    @patch.object(DependencyManager, '_get_command_version')
    def test_check_system_tool_installed(self, mock_get_version, mock_which):
        """测试检查已安装的系统工具"""
        mock_which.return_value = "/usr/bin/node"
        mock_get_version.return_value = "18.17.0"
        
        dep_info = DependencyInfo(
            name="node",
            type=DependencyType.SYSTEM,
            required_version=">=18.0.0"
        )
        
        self.manager._check_system_tool(dep_info)
        
        self.assertEqual(dep_info.status, DependencyStatus.INSTALLED)
        self.assertEqual(dep_info.installed_version, "18.17.0")
    
    @patch('shutil.which')
    def test_check_system_tool_missing(self, mock_which):
        """测试检查缺失的系统工具"""
        mock_which.return_value = None
        
        dep_info = DependencyInfo(
            name="nonexistent",
            type=DependencyType.SYSTEM
        )
        
        self.manager._check_system_tool(dep_info)
        
        self.assertEqual(dep_info.status, DependencyStatus.MISSING)
    
    @patch('subprocess.run')
    def test_get_command_version_node(self, mock_run):
        """测试获取Node.js版本"""
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "v18.17.0\n"
        mock_run.return_value = mock_result
        
        version = self.manager._get_command_version("node")
        self.assertEqual(version, "18.17.0")
    
    @patch('subprocess.run')
    def test_get_command_version_git(self, mock_run):
        """测试获取Git版本"""
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "git version 2.39.0\n"
        mock_run.return_value = mock_result
        
        version = self.manager._get_command_version("git")
        self.assertEqual(version, "2.39.0")
    
    @patch('subprocess.run')
    def test_get_command_version_failure(self, mock_run):
        """测试获取命令版本失败"""
        mock_run.side_effect = FileNotFoundError()
        
        version = self.manager._get_command_version("nonexistent")
        self.assertIsNone(version)
    
    def test_generate_install_command_python_uv(self):
        """测试生成Python UV安装命令"""
        dep_info = DependencyInfo(
            name="fastapi",
            type=DependencyType.PYTHON,
            install_method=InstallMethod.UV
        )
        
        command = self.manager._generate_install_command(dep_info)
        self.assertEqual(command, "uv add fastapi")
    
    def test_generate_install_command_python_pip(self):
        """测试生成Python pip安装命令"""
        dep_info = DependencyInfo(
            name="fastapi",
            type=DependencyType.PYTHON,
            required_version=">=0.100.0",
            install_method=InstallMethod.PIP
        )
        
        command = self.manager._generate_install_command(dep_info)
        self.assertEqual(command, "pip install fastapi>=0.100.0")
    
    def test_generate_install_command_frontend_pnpm(self):
        """测试生成前端pnpm安装命令"""
        dep_info = DependencyInfo(
            name="vue",
            type=DependencyType.FRONTEND,
            install_method=InstallMethod.PNPM
        )
        
        command = self.manager._generate_install_command(dep_info)
        self.assertEqual(command, "pnpm add vue")
    
    def test_generate_install_command_system(self):
        """测试生成系统工具安装信息"""
        dep_info = DependencyInfo(
            name="node",
            type=DependencyType.SYSTEM
        )
        
        command = self.manager._generate_install_command(dep_info)
        self.assertIn("nodejs.org", command)
    
    def test_detect_conflicts(self):
        """测试检测依赖冲突"""
        dependencies = [
            DependencyInfo(name="test", type=DependencyType.PYTHON, required_version="1.0.0"),
            DependencyInfo(name="test", type=DependencyType.PYTHON, required_version="2.0.0")
        ]
        
        self.manager._detect_conflicts(dependencies)
        
        for dep in dependencies:
            self.assertEqual(dep.status, DependencyStatus.CONFLICT)
            self.assertGreater(len(dep.conflicts), 0)
    
    def test_create_installation_plan(self):
        """测试创建安装计划"""
        dependencies = [
            DependencyInfo(name="missing", type=DependencyType.PYTHON, status=DependencyStatus.MISSING),
            DependencyInfo(name="outdated", type=DependencyType.PYTHON, status=DependencyStatus.OUTDATED),
            DependencyInfo(name="installed", type=DependencyType.PYTHON, status=DependencyStatus.INSTALLED)
        ]
        
        plan = self.manager.create_installation_plan(dependencies, install_missing=True, upgrade_outdated=True)
        
        self.assertEqual(len(plan.to_install), 1)
        self.assertEqual(len(plan.to_upgrade), 1)
        self.assertEqual(plan.to_install[0].name, "missing")
        self.assertEqual(plan.to_upgrade[0].name, "outdated")
        self.assertGreater(plan.estimated_time, 0)
    
    @patch('subprocess.run')
    def test_install_python_dependency_pip(self, mock_run):
        """测试安装Python依赖（pip）"""
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_run.return_value = mock_result
        
        dep_info = DependencyInfo(
            name="fastapi",
            type=DependencyType.PYTHON,
            required_version=">=0.100.0",
            install_method=InstallMethod.PIP
        )
        
        success = self.manager._install_python_dependency(dep_info)
        
        self.assertTrue(success)
        mock_run.assert_called_once_with(
            ["pip", "install", "fastapi>=0.100.0"],
            capture_output=True,
            text=True
        )
    
    @patch('subprocess.run')
    def test_install_python_dependency_uv(self, mock_run):
        """测试安装Python依赖（uv）"""
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_run.return_value = mock_result
        
        dep_info = DependencyInfo(
            name="fastapi",
            type=DependencyType.PYTHON,
            install_method=InstallMethod.UV
        )
        
        success = self.manager._install_python_dependency(dep_info)
        
        self.assertTrue(success)
        mock_run.assert_called_once_with(
            ["uv", "add", "fastapi"],
            capture_output=True,
            text=True
        )
    
    @patch('subprocess.run')
    def test_install_frontend_dependency(self, mock_run):
        """测试安装前端依赖"""
        # 创建前端目录
        frontend_path = Path(self.temp_dir) / "frontend"
        frontend_path.mkdir(exist_ok=True)
        
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_run.return_value = mock_result
        
        dep_info = DependencyInfo(
            name="vue",
            type=DependencyType.FRONTEND,
            install_method=InstallMethod.NPM
        )
        
        success = self.manager._install_frontend_dependency(dep_info)
        
        self.assertTrue(success)
        mock_run.assert_called_once_with(
            ["npm", "install", "vue"],
            cwd=frontend_path,
            capture_output=True,
            text=True
        )
    
    def test_scan_frontend_dependencies_no_frontend(self):
        """测试扫描前端依赖（无前端项目）"""
        dependencies = self.manager._scan_frontend_dependencies()
        self.assertEqual(dependencies, [])
    
    def test_scan_frontend_dependencies_with_package_json(self):
        """测试扫描前端依赖（有package.json）"""
        # 创建前端目录和package.json
        frontend_path = Path(self.temp_dir) / "frontend"
        frontend_path.mkdir(exist_ok=True)
        
        package_json = {
            "dependencies": {
                "vue": "^3.3.0"
            },
            "devDependencies": {
                "vite": "^5.0.0"
            }
        }
        
        package_file = frontend_path / "package.json"
        with open(package_file, 'w') as f:
            json.dump(package_json, f)
        
        dependencies = self.manager._scan_frontend_dependencies()
        
        # 应该找到定义的前端依赖
        dep_names = [dep.name for dep in dependencies]
        self.assertIn("vue", dep_names)
        self.assertIn("vite", dep_names)
        
        # 检查状态
        vue_dep = next(dep for dep in dependencies if dep.name == "vue")
        self.assertEqual(vue_dep.status, DependencyStatus.INSTALLED)
        self.assertEqual(vue_dep.installed_version, "^3.3.0")
    
    def test_print_dependency_statistics(self):
        """测试打印依赖统计"""
        dependencies = [
            DependencyInfo(name="dep1", type=DependencyType.PYTHON, status=DependencyStatus.INSTALLED),
            DependencyInfo(name="dep2", type=DependencyType.PYTHON, status=DependencyStatus.MISSING),
            DependencyInfo(name="dep3", type=DependencyType.PYTHON, status=DependencyStatus.OUTDATED)
        ]
        
        # 这个测试主要确保不会抛出异常
        self.manager._print_dependency_statistics(dependencies)
        
        # 验证UI调用
        self.mock_ui.print_section.assert_called()
        self.mock_ui.print_key_value.assert_called()


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
        
        # 模拟依赖
        self.mock_config = MagicMock()
        self.mock_ui = MagicMock()
        self.mock_paths = MagicMock()
        self.mock_paths.ROOT = Path(self.temp_dir)
    
    @patch('utils.dep_manager.get_config_manager')
    @patch('utils.dep_manager.get_ui')
    @patch('utils.dep_manager.Paths')
    def test_scan_dependencies_integration(self, mock_paths, mock_get_ui, mock_get_config):
        """测试依赖扫描集成"""
        # 设置模拟
        mock_get_config.return_value = self.mock_config
        mock_get_ui.return_value = self.mock_ui
        mock_paths.return_value = self.mock_paths
        
        # 创建管理器
        manager = DependencyManager()
        
        # 模拟检查方法
        with patch.object(manager, '_check_python_package') as mock_check_python, \
             patch.object(manager, '_check_system_tool') as mock_check_system, \
             patch.object(manager, '_scan_frontend_dependencies', return_value=[]):
            
            # 执行扫描
            dependencies = manager.scan_dependencies()
            
            # 验证结果
            self.assertIsInstance(dependencies, list)
            self.assertGreater(len(dependencies), 0)
            
            # 验证调用
            self.assertGreater(mock_check_python.call_count, 0)
            self.assertGreater(mock_check_system.call_count, 0)
    
    @patch('utils.dep_manager.get_config_manager')
    @patch('utils.dep_manager.get_ui')
    @patch('utils.dep_manager.Paths')
    def test_auto_fix_dependencies_integration(self, mock_paths, mock_get_ui, mock_get_config):
        """测试依赖自动修复集成"""
        # 设置模拟
        mock_get_config.return_value = self.mock_config
        mock_get_ui.return_value = self.mock_ui
        mock_paths.return_value = self.mock_paths
        
        # 模拟用户确认
        mock_get_ui.return_value.confirm.return_value = True
        
        # 创建管理器
        manager = DependencyManager()
        
        # 模拟扫描结果（有缺失依赖）
        missing_dep = DependencyInfo(
            name="test_missing",
            type=DependencyType.PYTHON,
            status=DependencyStatus.MISSING,
            install_method=InstallMethod.PIP
        )
        
        with patch.object(manager, 'scan_dependencies', return_value=[missing_dep]), \
             patch.object(manager, 'execute_installation_plan', return_value=True):
            
            result = manager.auto_fix_dependencies()
            
            # 验证结果
            self.assertTrue(result)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)