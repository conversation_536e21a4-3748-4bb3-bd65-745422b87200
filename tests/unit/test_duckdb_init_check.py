#!/usr/bin/env python3
"""
DuckDB初始化检查器单元测试
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os

from src.database.duckdb_init_check import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CheckResult


class TestDuckDBInitChecker:
    """DuckDB初始化检查器测试"""

    def setup_method(self):
        """测试前设置"""
        self.mock_config = {
            'database': {
                'path': 'test.db',
                'auto_create': True
            }
        }
        self.mock_connection_manager = Mock()
        self.data_dict_path = Path('test_data_dict.md')
        self.checker = DuckDBInitChecker(
            config_path="config/settings.toml",
            environment="test",
            dict_path=str(self.data_dict_path)
        )

    def test_init(self):
        """测试初始化"""
        assert self.checker.config == self.mock_config
        assert self.checker.connection_manager == self.mock_connection_manager
        assert self.checker.data_dict_path == self.data_dict_path

    def test_check_result_success(self):
        """测试检查结果成功"""
        result = CheckResult(True, "Success", {"key": "value"})
        assert result.success is True
        assert result.message == "Success"
        assert result.data == {"key": "value"}

    def test_check_result_failure(self):
        """测试检查结果失败"""
        result = CheckResult(False, "Error occurred")
        assert result.success is False
        assert result.message == "Error occurred"
        assert result.data == {}

    def test_check_database_exists_true(self):
        """测试数据库存在检查（存在）"""
        with patch('pathlib.Path.exists', return_value=True):
            result = self.checker.check_database_exists()
            assert result.success is True
            assert "数据库文件已存在" in result.message

    def test_check_database_exists_false(self):
        """测试数据库存在检查（不存在）"""
        with patch('pathlib.Path.exists', return_value=False):
            result = self.checker.check_database_exists()
            assert result.success is False
            assert "数据库文件不存在" in result.message

    def test_check_connection_success(self):
        """测试连接检查成功"""
        self.mock_connection_manager.test_connection.return_value = True
        
        result = self.checker.check_connection()
        
        assert result.success is True
        assert "数据库连接正常" in result.message

    def test_check_connection_failure(self):
        """测试连接检查失败"""
        self.mock_connection_manager.test_connection.return_value = False
        
        result = self.checker.check_connection()
        
        assert result.success is False
        assert "数据库连接失败" in result.message

    def test_check_data_dictionary_exists(self):
        """测试数据字典存在检查"""
        with patch('pathlib.Path.exists', return_value=True):
            result = self.checker.check_data_dictionary()
            assert result.success is True
            assert "数据字典文件已存在" in result.message

    def test_check_data_dictionary_not_exists(self):
        """测试数据字典不存在检查"""
        with patch('pathlib.Path.exists', return_value=False):
            result = self.checker.check_data_dictionary()
            assert result.success is False
            assert "数据字典文件不存在" in result.message

    def test_parse_data_dictionary_valid(self):
        """测试解析有效数据字典"""
        data_dict_content = """
# 数据字典

## 表结构

### stock_basic_info
股票基础信息表

| 字段名 | 类型 | 描述 |
|--------|------|------|
| symbol | VARCHAR(10) | 股票代码 |
| name | VARCHAR(50) | 股票名称 |

### futures_basic_info
期货基础信息表

| 字段名 | 类型 | 描述 |
|--------|------|------|
| symbol | VARCHAR(20) | 期货代码 |
| name | VARCHAR(100) | 期货名称 |
"""
        
        with patch('builtins.open', create=True) as mock_open:
            mock_open.return_value.__enter__.return_value.read.return_value = data_dict_content
            
            result = self.checker.parse_data_dictionary()
            
            assert result.success is True
            assert len(result.data) == 2
            assert result.data[0].name == 'stock_basic_info'
            assert result.data[1].name == 'futures_basic_info'

    def test_parse_data_dictionary_invalid_format(self):
        """测试解析无效格式数据字典"""
        data_dict_content = "Invalid format"
        
        with patch('builtins.open', create=True) as mock_open:
            mock_open.return_value.__enter__.return_value.read.return_value = data_dict_content
            
            result = self.checker.parse_data_dictionary()
            
            assert result.success is False
            assert "解析数据字典失败" in result.message

    def test_parse_data_dictionary_file_not_found(self):
        """测试解析数据字典文件不存在"""
        with patch('builtins.open', side_effect=FileNotFoundError):
            result = self.checker.parse_data_dictionary()
            
            assert result.success is False
            assert "数据字典文件不存在" in result.message

    def test_get_table_schemas_success(self):
        """测试获取表结构成功"""
        with patch.object(self.checker, 'parse_data_dictionary') as mock_parse:
            mock_schema = Mock()
            mock_schema.name = 'test_table'
            mock_parse.return_value = CheckResult(True, "Success", [mock_schema])
            
            schemas = self.checker.get_table_schemas()
            
            assert len(schemas) == 1
            assert schemas[0].name == 'test_table'

    def test_get_table_schemas_failure(self):
        """测试获取表结构失败"""
        with patch.object(self.checker, 'parse_data_dictionary') as mock_parse:
            mock_parse.return_value = CheckResult(False, "Error")
            
            schemas = self.checker.get_table_schemas()
            
            assert len(schemas) == 0

    def test_check_table_exists_true(self):
        """测试检查表存在（存在）"""
        self.mock_connection_manager.table_exists.return_value = True
        
        result = self.checker.check_table_exists('test_table')
        
        assert result.success is True
        assert "表已存在" in result.message

    def test_check_table_exists_false(self):
        """测试检查表存在（不存在）"""
        self.mock_connection_manager.table_exists.return_value = False
        
        result = self.checker.check_table_exists('test_table')
        
        assert result.success is False
        assert "表不存在" in result.message

    def test_check_all_tables_exist_all_exist(self):
        """测试检查所有表存在（全部存在）"""
        mock_schema1 = Mock()
        mock_schema1.name = 'table1'
        mock_schema2 = Mock()
        mock_schema2.name = 'table2'
        
        with patch.object(self.checker, 'get_table_schemas', return_value=[mock_schema1, mock_schema2]):
            self.mock_connection_manager.table_exists.return_value = True
            
            result = self.checker.check_all_tables_exist()
            
            assert result.success is True
            assert "所有表都已存在" in result.message

    def test_check_all_tables_exist_some_missing(self):
        """测试检查所有表存在（部分缺失）"""
        mock_schema1 = Mock()
        mock_schema1.name = 'table1'
        mock_schema2 = Mock()
        mock_schema2.name = 'table2'
        
        with patch.object(self.checker, 'get_table_schemas', return_value=[mock_schema1, mock_schema2]):
            self.mock_connection_manager.table_exists.side_effect = [True, False]
            
            result = self.checker.check_all_tables_exist()
            
            assert result.success is False
            assert "缺失表" in result.message

    def test_initialize_database_success(self):
        """测试初始化数据库成功"""
        mock_schema = Mock()
        mock_schema.name = 'test_table'
        mock_schema.columns = [
            {'name': 'id', 'type': 'INTEGER'},
            {'name': 'name', 'type': 'VARCHAR(50)'}
        ]
        
        with patch.object(self.checker, 'get_table_schemas', return_value=[mock_schema]):
            mock_cursor = Mock()
            self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
            
            result = self.checker.initialize_database()
            
            assert result.success is True
            assert "数据库初始化成功" in result.message
            mock_cursor.execute.assert_called()

    def test_initialize_database_no_tables(self):
        """测试初始化数据库无表定义"""
        with patch.object(self.checker, 'get_table_schemas', return_value=[]):
            result = self.checker.initialize_database()
            
            assert result.success is False
            assert "数据字典中未找到表定义" in result.message

    def test_initialize_database_force_recreate(self):
        """测试强制重新创建数据库"""
        mock_schema = Mock()
        mock_schema.name = 'test_table'
        mock_schema.columns = [
            {'name': 'id', 'type': 'INTEGER'}
        ]
        
        with patch.object(self.checker, 'get_table_schemas', return_value=[mock_schema]):
            mock_cursor = Mock()
            self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
            self.mock_connection_manager.table_exists.return_value = True
            
            result = self.checker.initialize_database(force=True)
            
            assert result.success is True
            mock_cursor.execute.assert_any_call("DROP TABLE IF EXISTS test_table")

    def test_create_table_success(self):
        """测试创建表成功"""
        mock_schema = Mock()
        mock_schema.name = 'test_table'
        mock_schema.columns = [
            {'name': 'id', 'type': 'INTEGER'},
            {'name': 'name', 'type': 'VARCHAR(50)'}
        ]
        
        mock_cursor = Mock()
        self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        result = self.checker.create_table(mock_schema)
        
        assert result.success is True
        assert "表创建成功" in result.message
        mock_cursor.execute.assert_called()

    def test_create_table_failure(self):
        """测试创建表失败"""
        mock_schema = Mock()
        mock_schema.name = 'test_table'
        mock_schema.columns = [
            {'name': 'id', 'type': 'INTEGER'}
        ]
        
        mock_cursor = Mock()
        mock_cursor.execute.side_effect = Exception("SQL error")
        self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        result = self.checker.create_table(mock_schema)
        
        assert result.success is False
        assert "创建表失败" in result.message

    def test_check_table_data_has_data(self):
        """测试检查表数据（有数据）"""
        mock_cursor = Mock()
        mock_cursor.fetchone.return_value = (100,)
        self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        result = self.checker.check_table_data('test_table')
        
        assert result.success is True
        assert "表有数据" in result.message
        assert result.data['row_count'] == 100

    def test_check_table_data_no_data(self):
        """测试检查表数据（无数据）"""
        mock_cursor = Mock()
        mock_cursor.fetchone.return_value = (0,)
        self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        result = self.checker.check_table_data('test_table')
        
        assert result.success is True
        assert "表为空" in result.message
        assert result.data['row_count'] == 0

    def test_check_table_data_error(self):
        """测试检查表数据错误"""
        mock_cursor = Mock()
        mock_cursor.execute.side_effect = Exception("SQL error")
        self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        result = self.checker.check_table_data('test_table')
        
        assert result.success is False
        assert "检查表数据失败" in result.message

    def test_full_health_check_success(self):
        """测试完整健康检查成功"""
        with patch.object(self.checker, 'check_database_exists') as mock_db_check:
            with patch.object(self.checker, 'check_connection') as mock_conn_check:
                with patch.object(self.checker, 'check_data_dictionary') as mock_dict_check:
                    with patch.object(self.checker, 'check_all_tables_exist') as mock_tables_check:
                        
                        mock_db_check.return_value = CheckResult(True, "DB exists")
                        mock_conn_check.return_value = CheckResult(True, "Connection OK")
                        mock_dict_check.return_value = CheckResult(True, "Dict OK")
                        mock_tables_check.return_value = CheckResult(True, "Tables OK")
                        
                        result = self.checker.full_health_check()
                        
                        assert result.success is True
                        assert "健康检查通过" in result.message

    def test_full_health_check_failure(self):
        """测试完整健康检查失败"""
        with patch.object(self.checker, 'check_database_exists') as mock_db_check:
            with patch.object(self.checker, 'check_connection') as mock_conn_check:
                
                mock_db_check.return_value = CheckResult(False, "DB missing")
                mock_conn_check.return_value = CheckResult(False, "Connection failed")
                
                result = self.checker.full_health_check()
                
                assert result.success is False
                assert "健康检查失败" in result.message

    def test_get_database_info(self):
        """测试获取数据库信息"""
        with patch('pathlib.Path.exists', return_value=True):
            with patch('pathlib.Path.stat') as mock_stat:
                mock_stat.return_value.st_size = 1024
                
                info = self.checker.get_database_info()
                
                assert info['path'] == 'test.db'
                assert info['exists'] is True
                assert info['size'] == 1024

    def test_get_database_info_not_exists(self):
        """测试获取数据库信息（不存在）"""
        with patch('pathlib.Path.exists', return_value=False):
            info = self.checker.get_database_info()
            
            assert info['path'] == 'test.db'
            assert info['exists'] is False
            assert info['size'] == 0

    def test_repair_database_success(self):
        """测试修复数据库成功"""
        mock_schema = Mock()
        mock_schema.name = 'test_table'
        mock_schema.columns = [{'name': 'id', 'type': 'INTEGER'}]
        
        with patch.object(self.checker, 'get_table_schemas', return_value=[mock_schema]):
            with patch.object(self.checker, 'check_table_exists') as mock_table_check:
                mock_table_check.return_value = CheckResult(False, "Table missing")
                
                with patch.object(self.checker, 'create_table') as mock_create:
                    mock_create.return_value = CheckResult(True, "Table created")
                    
                    result = self.checker.repair_database()
                    
                    assert result.success is True
                    assert "数据库修复成功" in result.message

    def test_repair_database_no_repair_needed(self):
        """测试修复数据库无需修复"""
        mock_schema = Mock()
        mock_schema.name = 'test_table'
        
        with patch.object(self.checker, 'get_table_schemas', return_value=[mock_schema]):
            with patch.object(self.checker, 'check_table_exists') as mock_table_check:
                mock_table_check.return_value = CheckResult(True, "Table exists")
                
                result = self.checker.repair_database()
                
                assert result.success is True
                assert "数据库无需修复" in result.message

    def test_backup_database_success(self):
        """测试备份数据库成功"""
        with patch('pathlib.Path.exists', return_value=True):
            with patch('shutil.copy2') as mock_copy:
                result = self.checker.backup_database()
                
                assert result.success is True
                assert "数据库备份成功" in result.message
                mock_copy.assert_called_once()

    def test_backup_database_source_not_exists(self):
        """测试备份数据库源文件不存在"""
        with patch('pathlib.Path.exists', return_value=False):
            result = self.checker.backup_database()
            
            assert result.success is False
            assert "数据库文件不存在" in result.message

    def test_restore_database_success(self):
        """测试恢复数据库成功"""
        backup_path = Path('backup.db')
        
        with patch('pathlib.Path.exists', return_value=True):
            with patch('shutil.copy2') as mock_copy:
                result = self.checker.restore_database(backup_path)
                
                assert result.success is True
                assert "数据库恢复成功" in result.message
                mock_copy.assert_called_once()

    def test_restore_database_backup_not_exists(self):
        """测试恢复数据库备份文件不存在"""
        backup_path = Path('backup.db')
        
        with patch('pathlib.Path.exists', return_value=False):
            result = self.checker.restore_database(backup_path)
            
            assert result.success is False
            assert "备份文件不存在" in result.message

    def test_get_table_list(self):
        """测试获取表列表"""
        mock_cursor = Mock()
        mock_cursor.fetchall.return_value = [
            ('table1',),
            ('table2',),
            ('table3',)
        ]
        self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        tables = self.checker.get_table_list()
        
        assert len(tables) == 3
        assert 'table1' in tables
        assert 'table2' in tables
        assert 'table3' in tables

    def test_get_table_list_error(self):
        """测试获取表列表错误"""
        mock_cursor = Mock()
        mock_cursor.execute.side_effect = Exception("SQL error")
        self.mock_connection_manager.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        tables = self.checker.get_table_list()
        
        assert len(tables) == 0

    def test_generate_health_report(self):
        """测试生成健康报告"""
        with patch.object(self.checker, 'full_health_check') as mock_health_check:
            mock_health_check.return_value = CheckResult(True, "All good")
            
            with patch.object(self.checker, 'get_database_info') as mock_db_info:
                mock_db_info.return_value = {
                    'path': 'test.db',
                    'exists': True,
                    'size': 1024
                }
                
                with patch.object(self.checker, 'get_table_list') as mock_table_list:
                    mock_table_list.return_value = ['table1', 'table2']
                    
                    report = self.checker.generate_health_report()
                    
                    assert report['overall_health'] is True
                    assert report['database_info']['exists'] is True
                    assert len(report['tables']) == 2