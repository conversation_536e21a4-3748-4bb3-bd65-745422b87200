#!/usr/bin/env python3
"""
统一异常处理模块单元测试
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from src.utils.exceptions import (
    ErrorCode,
    AquaException,
    DatabaseException,
    CacheException,
    DataValidationException,
    BusinessLogicException,
    ExceptionHandler,
    get_exception_handler,
    handle_exception,
    raise_database_error,
    raise_cache_error,
    raise_validation_error,
    raise_business_error,
    safe_execute,
    validate_not_none,
    validate_type,
    validate_range
)


class TestErrorCode:
    """错误代码测试类"""
    
    def test_error_code_values(self):
        """测试错误代码值"""
        assert ErrorCode.UNKNOWN_ERROR.value == 1000
        assert ErrorCode.DATABASE_CONNECTION_ERROR.value == 2000
        assert ErrorCode.CACHE_CONNECTION_ERROR.value == 3000
        assert ErrorCode.DATA_VALIDATION_ERROR.value == 4000
        assert ErrorCode.NETWORK_ERROR.value == 5000
        assert ErrorCode.BUSINESS_LOGIC_ERROR.value == 6000
    
    def test_error_code_names(self):
        """测试错误代码名称"""
        assert ErrorCode.UNKNOWN_ERROR.name == "UNKNOWN_ERROR"
        assert ErrorCode.DATABASE_CONNECTION_ERROR.name == "DATABASE_CONNECTION_ERROR"
        assert ErrorCode.CACHE_CONNECTION_ERROR.name == "CACHE_CONNECTION_ERROR"


class TestAquaException:
    """AQUA异常测试类"""
    
    def test_basic_exception_creation(self):
        """测试基础异常创建"""
        exc = AquaException("Test error message")
        
        assert exc.message == "Test error message"
        assert exc.error_code == ErrorCode.UNKNOWN_ERROR
        assert exc.details == {}
        assert exc.cause is None
        assert isinstance(exc.timestamp, datetime)
    
    def test_exception_with_all_parameters(self):
        """测试带所有参数的异常创建"""
        cause = ValueError("Original error")
        details = {"key": "value"}
        
        exc = AquaException(
            message="Test error",
            error_code=ErrorCode.DATA_VALIDATION_ERROR,
            details=details,
            cause=cause
        )
        
        assert exc.message == "Test error"
        assert exc.error_code == ErrorCode.DATA_VALIDATION_ERROR
        assert exc.details == details
        assert exc.cause == cause
    
    def test_exception_to_dict(self):
        """测试异常转换为字典"""
        cause = ValueError("Original error")
        details = {"key": "value"}
        
        exc = AquaException(
            message="Test error",
            error_code=ErrorCode.DATA_VALIDATION_ERROR,
            details=details,
            cause=cause
        )
        
        result = exc.to_dict()
        
        assert result["error_code"] == ErrorCode.DATA_VALIDATION_ERROR.value
        assert result["error_name"] == "DATA_VALIDATION_ERROR"
        assert result["message"] == "Test error"
        assert result["details"] == details
        assert result["cause"] == str(cause)
        assert "timestamp" in result
    
    @patch('src.utils.exceptions.get_logger')
    def test_exception_logging(self, mock_get_logger):
        """测试异常日志记录"""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        exc = AquaException("Test error")
        
        # 验证日志记录被调用
        mock_logger.error.assert_called_once()


class TestDatabaseException:
    """数据库异常测试类"""
    
    def test_database_exception_creation(self):
        """测试数据库异常创建"""
        exc = DatabaseException("Database connection failed")
        
        assert exc.message == "Database connection failed"
        assert exc.error_code == ErrorCode.DATABASE_CONNECTION_ERROR
        assert isinstance(exc, AquaException)
    
    def test_database_exception_with_details(self):
        """测试带详细信息的数据库异常"""
        exc = DatabaseException(
            message="Query failed",
            error_code=ErrorCode.DATABASE_QUERY_ERROR,
            table_name="test_table",
            query="SELECT * FROM test_table"
        )
        
        assert exc.details["table_name"] == "test_table"
        assert exc.details["query"] == "SELECT * FROM test_table"
        assert exc.error_code == ErrorCode.DATABASE_QUERY_ERROR
    
    def test_database_exception_query_truncation(self):
        """测试数据库异常查询截断"""
        long_query = "SELECT * FROM table WHERE " + "x = 1 AND " * 100
        
        exc = DatabaseException(
            message="Query failed",
            query=long_query
        )
        
        assert len(exc.details["query"]) <= 500


class TestCacheException:
    """缓存异常测试类"""
    
    def test_cache_exception_creation(self):
        """测试缓存异常创建"""
        exc = CacheException("Cache connection failed")
        
        assert exc.message == "Cache connection failed"
        assert exc.error_code == ErrorCode.CACHE_CONNECTION_ERROR
        assert isinstance(exc, AquaException)
    
    def test_cache_exception_with_details(self):
        """测试带详细信息的缓存异常"""
        exc = CacheException(
            message="Cache operation failed",
            error_code=ErrorCode.CACHE_OPERATION_ERROR,
            cache_key="test_key",
            cache_level="l1_memory"
        )
        
        assert exc.details["cache_key"] == "test_key"
        assert exc.details["cache_level"] == "l1_memory"
        assert exc.error_code == ErrorCode.CACHE_OPERATION_ERROR


class TestDataValidationException:
    """数据验证异常测试类"""
    
    def test_validation_exception_creation(self):
        """测试验证异常创建"""
        exc = DataValidationException("Validation failed")
        
        assert exc.message == "Validation failed"
        assert exc.error_code == ErrorCode.DATA_VALIDATION_ERROR
        assert isinstance(exc, AquaException)
    
    def test_validation_exception_with_details(self):
        """测试带详细信息的验证异常"""
        exc = DataValidationException(
            message="Field validation failed",
            field_name="email",
            field_value="invalid_email",
            validation_rules=["email_format", "not_empty"]
        )
        
        assert exc.details["field_name"] == "email"
        assert exc.details["field_value"] == "invalid_email"
        assert exc.details["validation_rules"] == ["email_format", "not_empty"]


class TestBusinessLogicException:
    """业务逻辑异常测试类"""
    
    def test_business_exception_creation(self):
        """测试业务逻辑异常创建"""
        exc = BusinessLogicException("Business rule violated")
        
        assert exc.message == "Business rule violated"
        assert exc.error_code == ErrorCode.BUSINESS_LOGIC_ERROR
        assert isinstance(exc, AquaException)
    
    def test_business_exception_with_details(self):
        """测试带详细信息的业务逻辑异常"""
        context = {"user_id": 123, "operation": "transfer"}
        
        exc = BusinessLogicException(
            message="Insufficient funds",
            error_code=ErrorCode.INSUFFICIENT_DATA,
            operation="transfer_money",
            context=context
        )
        
        assert exc.details["operation"] == "transfer_money"
        assert exc.details["context"] == context
        assert exc.error_code == ErrorCode.INSUFFICIENT_DATA


class TestExceptionHandler:
    """异常处理器测试类"""
    
    @pytest.fixture
    def exception_handler(self):
        """创建异常处理器实例"""
        return ExceptionHandler()
    
    def test_exception_handler_creation(self, exception_handler):
        """测试异常处理器创建"""
        assert exception_handler.error_counters == {}
        assert exception_handler.logger is not None
    
    def test_handle_aqua_exception(self, exception_handler):
        """测试处理AQUA异常"""
        exc = AquaException("Test error", ErrorCode.DATA_VALIDATION_ERROR)
        
        result = exception_handler.handle_exception(exc)
        
        assert result["error_code"] == ErrorCode.DATA_VALIDATION_ERROR.value
        assert result["error_name"] == "DATA_VALIDATION_ERROR"
        assert result["message"] == "Test error"
        assert "timestamp" in result
        assert exception_handler.error_counters["AquaException"] == 1
    
    def test_handle_standard_exception(self, exception_handler):
        """测试处理标准异常"""
        exc = ValueError("Invalid value")
        
        result = exception_handler.handle_exception(exc)
        
        assert result["error_code"] == ErrorCode.DATA_VALIDATION_ERROR.value
        assert result["message"] == "Invalid value"
        assert result["details"]["exception_type"] == "ValueError"
        assert exception_handler.error_counters["ValueError"] == 1
    
    def test_handle_exception_with_context(self, exception_handler):
        """测试带上下文的异常处理"""
        exc = ValueError("Invalid value")
        context = {"user_id": 123, "operation": "validate"}
        
        result = exception_handler.handle_exception(exc, context)
        
        assert result["details"]["context"] == context
    
    def test_exception_mapping(self, exception_handler):
        """测试异常映射"""
        test_cases = [
            (ConnectionError("Connection failed"), ErrorCode.DATABASE_CONNECTION_ERROR),
            (TimeoutError("Timeout"), ErrorCode.TIMEOUT_ERROR),
            (ValueError("Invalid value"), ErrorCode.DATA_VALIDATION_ERROR),
            (TypeError("Type error"), ErrorCode.DATA_VALIDATION_ERROR),
            (FileNotFoundError("File not found"), ErrorCode.FILE_NOT_FOUND),
            (PermissionError("Permission denied"), ErrorCode.PERMISSION_DENIED),
            (RuntimeError("Runtime error"), ErrorCode.UNKNOWN_ERROR),
        ]
        
        for exc, expected_code in test_cases:
            mapped_code = exception_handler._map_exception_to_code(exc)
            assert mapped_code == expected_code
    
    def test_error_statistics(self, exception_handler):
        """测试错误统计"""
        # 处理一些异常
        exception_handler.handle_exception(ValueError("Error 1"))
        exception_handler.handle_exception(ValueError("Error 2"))
        exception_handler.handle_exception(TypeError("Error 3"))
        
        stats = exception_handler.get_error_statistics()
        
        assert stats["total_errors"] == 3
        assert stats["error_types"]["ValueError"] == 2
        assert stats["error_types"]["TypeError"] == 1
        assert "timestamp" in stats
    
    def test_reset_statistics(self, exception_handler):
        """测试重置统计"""
        exception_handler.handle_exception(ValueError("Error"))
        assert exception_handler.error_counters["ValueError"] == 1
        
        exception_handler.reset_statistics()
        assert exception_handler.error_counters == {}


class TestUtilityFunctions:
    """工具函数测试类"""
    
    def test_get_exception_handler_singleton(self):
        """测试异常处理器单例"""
        handler1 = get_exception_handler()
        handler2 = get_exception_handler()
        
        assert handler1 is handler2
    
    def test_handle_exception_function(self):
        """测试异常处理函数"""
        exc = ValueError("Test error")
        
        result = handle_exception(exc)
        
        assert result["error_code"] == ErrorCode.DATA_VALIDATION_ERROR.value
        assert result["message"] == "Test error"
    
    def test_raise_database_error(self):
        """测试抛出数据库错误"""
        with pytest.raises(DatabaseException) as exc_info:
            raise_database_error("Database error", table_name="test_table")
        
        exc = exc_info.value
        assert exc.message == "Database error"
        assert exc.details["table_name"] == "test_table"
    
    def test_raise_cache_error(self):
        """测试抛出缓存错误"""
        with pytest.raises(CacheException) as exc_info:
            raise_cache_error("Cache error", cache_key="test_key")
        
        exc = exc_info.value
        assert exc.message == "Cache error"
        assert exc.details["cache_key"] == "test_key"
    
    def test_raise_validation_error(self):
        """测试抛出验证错误"""
        with pytest.raises(DataValidationException) as exc_info:
            raise_validation_error("Validation error", field_name="email")
        
        exc = exc_info.value
        assert exc.message == "Validation error"
        assert exc.details["field_name"] == "email"
    
    def test_raise_business_error(self):
        """测试抛出业务错误"""
        with pytest.raises(BusinessLogicException) as exc_info:
            raise_business_error("Business error", operation="transfer")
        
        exc = exc_info.value
        assert exc.message == "Business error"
        assert exc.details["operation"] == "transfer"
    
    def test_safe_execute_success(self):
        """测试安全执行成功"""
        def test_function(x, y):
            return x + y
        
        result = safe_execute(test_function, 1, 2)
        assert result == 3
    
    def test_safe_execute_failure(self):
        """测试安全执行失败"""
        def test_function():
            raise ValueError("Test error")
        
        with pytest.raises(AquaException) as exc_info:
            safe_execute(test_function)
        
        exc = exc_info.value
        assert "执行 test_function 时发生错误" in exc.message
        assert isinstance(exc.cause, ValueError)
    
    def test_validate_not_none_success(self):
        """测试非空验证成功"""
        validate_not_none("test_value", "test_field")
        # 不应该抛出异常
    
    def test_validate_not_none_failure(self):
        """测试非空验证失败"""
        with pytest.raises(DataValidationException) as exc_info:
            validate_not_none(None, "test_field")
        
        exc = exc_info.value
        assert "字段 'test_field' 不能为空" in exc.message
        assert exc.details["field_name"] == "test_field"
    
    def test_validate_type_success(self):
        """测试类型验证成功"""
        validate_type("test_value", str, "test_field")
        validate_type(123, int, "number_field")
        # 不应该抛出异常
    
    def test_validate_type_failure(self):
        """测试类型验证失败"""
        with pytest.raises(DataValidationException) as exc_info:
            validate_type("123", int, "number_field")
        
        exc = exc_info.value
        assert "字段 'number_field' 类型错误" in exc.message
        assert exc.details["field_name"] == "number_field"
    
    def test_validate_range_success(self):
        """测试范围验证成功"""
        validate_range(5, 1, 10, "test_field")
        validate_range(1, 1, 10, "test_field")  # 边界值
        validate_range(10, 1, 10, "test_field")  # 边界值
        # 不应该抛出异常
    
    def test_validate_range_failure(self):
        """测试范围验证失败"""
        with pytest.raises(DataValidationException) as exc_info:
            validate_range(15, 1, 10, "test_field")
        
        exc = exc_info.value
        assert "字段 'test_field' 值超出范围" in exc.message
        assert exc.details["field_name"] == "test_field"
        
        # 测试下界
        with pytest.raises(DataValidationException):
            validate_range(0, 1, 10, "test_field")


class TestExceptionIntegration:
    """异常集成测试类"""
    
    def test_exception_hierarchy(self):
        """测试异常层次结构"""
        # 所有自定义异常都应该继承自AquaException
        assert issubclass(DatabaseException, AquaException)
        assert issubclass(CacheException, AquaException)
        assert issubclass(DataValidationException, AquaException)
        assert issubclass(BusinessLogicException, AquaException)
        
        # AquaException继承自Exception
        assert issubclass(AquaException, Exception)
    
    def test_exception_chaining(self):
        """测试异常链"""
        original_error = ValueError("Original error")
        
        try:
            raise_database_error("Database error", cause=original_error)
        except DatabaseException as e:
            assert e.cause == original_error
            assert str(e.cause) == "Original error"
    
    def test_exception_context_preservation(self):
        """测试异常上下文保留"""
        context = {"user_id": 123, "operation": "test"}
        
        try:
            raise_business_error("Business error", context=context)
        except BusinessLogicException as e:
            assert e.details["context"] == context
    
    @patch('src.utils.exceptions.get_logger')
    def test_exception_logging_integration(self, mock_get_logger):
        """测试异常日志集成"""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        # 测试不同类型的异常都会记录日志
        try:
            raise_database_error("Database error")
        except DatabaseException:
            pass
        
        try:
            raise_cache_error("Cache error")
        except CacheException:
            pass
        
        # 验证日志记录被调用
        assert mock_logger.error.call_count == 2


if __name__ == "__main__":
    pytest.main([__file__, "-v"])