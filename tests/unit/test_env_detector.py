#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA环境检测器单元测试

测试智能环境检测功能
版本: 1.0.0
创建时间: 2025-07-31
"""

import unittest
from unittest.mock import patch, MagicMock, mock_open
from pathlib import Path
import sys
import os
import platform

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.env_detector import (
    EnvironmentType, SystemType, SystemInfo, EnvironmentStatus,
    DependencyStatus, EnvironmentReport, EnvDetector
)


class TestEnums(unittest.TestCase):
    """枚举类测试"""
    
    def test_environment_type_values(self):
        """测试环境类型枚举值"""
        self.assertEqual(EnvironmentType.DEVELOPMENT.value, "development")
        self.assertEqual(EnvironmentType.PRODUCTION.value, "production")
        self.assertEqual(EnvironmentType.TESTING.value, "testing")
        self.assertEqual(EnvironmentType.CI_CD.value, "ci_cd")
    
    def test_system_type_values(self):
        """测试系统类型枚举值"""
        self.assertEqual(SystemType.WINDOWS.value, "windows")
        self.assertEqual(SystemType.MACOS.value, "macos")
        self.assertEqual(SystemType.LINUX.value, "linux")
        self.assertEqual(SystemType.UNKNOWN.value, "unknown")


class TestDataClasses(unittest.TestCase):
    """数据类测试"""
    
    def test_system_info_creation(self):
        """测试系统信息数据类"""
        info = SystemInfo(
            os_type=SystemType.MACOS,
            os_version="macOS 12.0",
            python_version="3.11.3",
            architecture="arm64",
            cpu_count=8,
            memory_gb=16.0,
            available_space_gb=100.0
        )
        
        self.assertEqual(info.os_type, SystemType.MACOS)
        self.assertEqual(info.python_version, "3.11.3")
        self.assertEqual(info.cpu_count, 8)
    
    def test_dependency_status_creation(self):
        """测试依赖状态数据类"""
        dep = DependencyStatus(
            name="fastapi",
            required=True,
            installed=True,
            version="0.104.1"
        )
        
        self.assertEqual(dep.name, "fastapi")
        self.assertTrue(dep.required)
        self.assertTrue(dep.installed)
        self.assertEqual(dep.version, "0.104.1")
        self.assertEqual(dep.issues, [])
    
    def test_environment_report_creation(self):
        """测试环境报告数据类"""
        system_info = SystemInfo(
            os_type=SystemType.LINUX,
            os_version="Ubuntu 20.04",
            python_version="3.9.0",
            architecture="x86_64",
            cpu_count=4,
            memory_gb=8.0,
            available_space_gb=50.0
        )
        
        env_status = EnvironmentStatus(
            env_type=EnvironmentType.DEVELOPMENT,
            is_virtual_env=True
        )
        
        report = EnvironmentReport(
            system_info=system_info,
            env_status=env_status
        )
        
        self.assertEqual(report.system_info.os_type, SystemType.LINUX)
        self.assertEqual(report.env_status.env_type, EnvironmentType.DEVELOPMENT)
        self.assertEqual(report.python_deps, [])
        self.assertEqual(report.system_deps, [])


class TestEnvDetector(unittest.TestCase):
    """环境检测器测试"""
    
    def setUp(self):
        """测试前准备"""
        # 模拟依赖
        self.mock_config = MagicMock()
        self.mock_ui = MagicMock()
        self.mock_paths = MagicMock()
        
        with patch('utils.env_detector.get_config_manager', return_value=self.mock_config), \
             patch('utils.env_detector.get_ui', return_value=self.mock_ui), \
             patch('utils.env_detector.Paths', return_value=self.mock_paths):
            self.detector = EnvDetector()
    
    def test_detector_initialization(self):
        """测试检测器初始化"""
        self.assertIsNotNone(self.detector.config)
        self.assertIsNotNone(self.detector.ui)
        self.assertIsNotNone(self.detector.paths)
        self.assertIsInstance(self.detector.python_requirements, list)
        self.assertIsInstance(self.detector.system_requirements, list)
    
    @patch('platform.system')
    @patch('platform.platform')
    @patch('platform.machine')
    @patch('os.cpu_count')
    def test_detect_system_info_macos(self, mock_cpu_count, mock_machine, mock_platform_info, mock_system):
        """测试检测macOS系统信息"""
        # 设置模拟
        mock_system.return_value = "Darwin"
        mock_platform_info.return_value = "macOS-12.0-arm64"
        mock_machine.return_value = "arm64"
        mock_cpu_count.return_value = 8
        
        with patch.object(self.detector, '_get_memory_info', return_value=16.0), \
             patch.object(self.detector, '_get_disk_space', return_value=100.0):
            
            info = self.detector.detect_system_info()
            
            self.assertEqual(info.os_type, SystemType.MACOS)
            self.assertEqual(info.architecture, "arm64")
            self.assertEqual(info.cpu_count, 8)
            self.assertEqual(info.memory_gb, 16.0)
            self.assertEqual(info.available_space_gb, 100.0)
    
    @patch('platform.system')
    def test_detect_system_info_windows(self, mock_system):
        """测试检测Windows系统信息"""
        mock_system.return_value = "Windows"
        
        with patch.object(self.detector, '_get_memory_info', return_value=8.0), \
             patch.object(self.detector, '_get_disk_space', return_value=50.0):
            
            info = self.detector.detect_system_info()
            self.assertEqual(info.os_type, SystemType.WINDOWS)
    
    @patch('platform.system')
    def test_detect_system_info_linux(self, mock_system):
        """测试检测Linux系统信息"""
        mock_system.return_value = "Linux"
        
        with patch.object(self.detector, '_get_memory_info', return_value=4.0), \
             patch.object(self.detector, '_get_disk_space', return_value=25.0):
            
            info = self.detector.detect_system_info()
            self.assertEqual(info.os_type, SystemType.LINUX)
    
    @patch('os.getenv')
    def test_detect_environment_type_ci_cd(self, mock_getenv):
        """测试检测CI/CD环境"""
        mock_getenv.side_effect = lambda var, default=None: "true" if var == "CI" else default
        
        env_type = self.detector._detect_environment_type()
        self.assertEqual(env_type, EnvironmentType.CI_CD)
    
    @patch('os.getenv')
    def test_detect_environment_type_testing(self, mock_getenv):
        """测试检测测试环境"""
        mock_getenv.side_effect = lambda var, default=None: "test_case" if var == "PYTEST_CURRENT_TEST" else default
        
        env_type = self.detector._detect_environment_type()
        self.assertEqual(env_type, EnvironmentType.TESTING)
    
    def test_detect_environment_type_production(self):
        """测试检测生产环境"""
        self.mock_config.get_environment.return_value = "production"
        
        with patch('os.getenv', return_value=None), \
             patch('sys.modules', {}):  # 清空pytest模块
            env_type = self.detector._detect_environment_type()
            self.assertEqual(env_type, EnvironmentType.PRODUCTION)
    
    def test_detect_environment_type_development(self):
        """测试检测开发环境"""
        self.mock_config.get_environment.return_value = "development"
        
        with patch('os.getenv', return_value=None), \
             patch('sys.modules', {}):  # 清空pytest模块
            env_type = self.detector._detect_environment_type()
            self.assertEqual(env_type, EnvironmentType.DEVELOPMENT)
    
    @patch('os.getenv')
    def test_is_in_virtual_env_virtual_env_var(self, mock_getenv):
        """测试通过VIRTUAL_ENV变量检测虚拟环境"""
        mock_getenv.return_value = "/path/to/venv"
        
        result = self.detector._is_in_virtual_env()
        self.assertTrue(result)
    
    @patch('sys.base_prefix', '/usr')
    @patch('sys.prefix', '/path/to/venv')
    def test_is_in_virtual_env_sys_prefix(self):
        """测试通过sys.prefix检测虚拟环境"""
        with patch('os.getenv', return_value=None):
            result = self.detector._is_in_virtual_env()
            self.assertTrue(result)
    
    @patch('os.getenv')
    def test_get_virtual_env_path_from_env(self, mock_getenv):
        """测试从环境变量获取虚拟环境路径"""
        mock_getenv.return_value = "/path/to/venv"
        
        path = self.detector._get_virtual_env_path()
        self.assertEqual(path, Path("/path/to/venv"))
    
    def test_detect_package_manager_no_frontend(self):
        """测试无前端目录时的包管理器检测"""
        self.mock_paths.ROOT = Path("/test")
        
        with patch('pathlib.Path.exists', return_value=False):
            result = self.detector._detect_package_manager()
            self.assertIsNone(result)
    
    @patch('subprocess.run')
    def test_get_command_version_node(self, mock_run):
        """测试获取Node.js版本"""
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "v18.17.0\n"
        mock_run.return_value = mock_result
        
        version = self.detector._get_command_version("node")
        self.assertEqual(version, "18.17.0")
    
    @patch('subprocess.run')
    def test_get_command_version_git(self, mock_run):
        """测试获取Git版本"""
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "git version 2.39.0\n"
        mock_run.return_value = mock_result
        
        version = self.detector._get_command_version("git")
        self.assertEqual(version, "2.39.0")
    
    @patch('subprocess.run')
    def test_get_command_version_failure(self, mock_run):
        """测试获取命令版本失败"""
        mock_run.side_effect = FileNotFoundError()
        
        version = self.detector._get_command_version("nonexistent")
        self.assertIsNone(version)
    
    def test_get_package_version_fastapi(self):
        """测试获取FastAPI版本"""
        with patch('fastapi.__version__', '0.104.1'):
            version = self.detector._get_package_version("fastapi")
            self.assertEqual(version, '0.104.1')
    
    def test_get_package_version_not_found(self):
        """测试获取不存在包的版本"""
        version = self.detector._get_package_version("nonexistent_package")
        self.assertIsNone(version)
    
    @patch('shutil.which')
    def test_check_system_dependencies_installed(self, mock_which):
        """测试检查已安装的系统依赖"""
        mock_which.side_effect = lambda cmd: "/usr/bin/" + cmd if cmd in ["node", "npm", "git"] else None
        
        with patch.object(self.detector, '_get_command_version', return_value="1.0.0"):
            deps = self.detector.check_system_dependencies()
            
            self.assertEqual(len(deps), 3)  # node, npm, git
            for dep in deps:
                self.assertTrue(dep.installed)
                self.assertEqual(dep.version, "1.0.0")
    
    @patch('shutil.which')
    def test_check_system_dependencies_missing(self, mock_which):
        """测试检查缺失的系统依赖"""
        mock_which.return_value = None  # 所有命令都不存在
        
        deps = self.detector.check_system_dependencies()
        
        self.assertEqual(len(deps), 3)
        for dep in deps:
            self.assertFalse(dep.installed)
            if dep.required:
                self.assertGreater(len(dep.issues), 0)
    
    def test_check_python_dependencies_installed(self):
        """测试检查已安装的Python依赖"""
        # 模拟所有包都已安装
        with patch('builtins.__import__'), \
             patch.object(self.detector, '_get_package_version', return_value="1.0.0"):
            
            deps = self.detector.check_python_dependencies()
            
            self.assertGreater(len(deps), 0)
            for dep in deps:
                self.assertTrue(dep.installed)
                self.assertEqual(dep.version, "1.0.0")
    
    def test_check_python_dependencies_missing(self):
        """测试检查缺失的Python依赖"""
        # 模拟所有包都未安装
        def mock_import(name):
            raise ImportError(f"No module named '{name}'")
        
        with patch('builtins.__import__', side_effect=mock_import):
            deps = self.detector.check_python_dependencies()
            
            self.assertGreater(len(deps), 0)
            for dep in deps:
                self.assertFalse(dep.installed)
                if dep.required:
                    self.assertGreater(len(dep.issues), 0)
    
    def test_get_memory_info_fallback(self):
        """测试内存信息获取回退机制"""
        # 测试异常情况下的回退
        with patch.object(self.detector, '_get_memory_info', return_value=0.0):
            memory = self.detector._get_memory_info()
            self.assertEqual(memory, 0.0)
    
    @patch('shutil.disk_usage')
    def test_get_disk_space_windows(self, mock_disk_usage):
        """测试获取Windows磁盘空间"""
        # 模拟100GB可用空间
        mock_disk_usage.return_value = (500 * 1024**3, 400 * 1024**3, 100 * 1024**3)
        
        with patch('os.statvfs', side_effect=AttributeError()):
            space = self.detector._get_disk_space()
            self.assertAlmostEqual(space, 100.0, places=1)
    
    def test_analyze_report_python_version_warning(self):
        """测试分析报告 - Python版本警告"""
        system_info = SystemInfo(
            os_type=SystemType.LINUX,
            os_version="Ubuntu 20.04",
            python_version="3.8.0",  # 旧版本
            architecture="x86_64",
            cpu_count=4,
            memory_gb=8.0,
            available_space_gb=50.0
        )
        
        env_status = EnvironmentStatus(
            env_type=EnvironmentType.DEVELOPMENT,
            is_virtual_env=True
        )
        
        report = EnvironmentReport(
            system_info=system_info,
            env_status=env_status
        )
        
        self.detector._analyze_report(report)
        
        # 应该有Python版本警告
        python_warnings = [w for w in report.warnings if "Python版本" in w]
        self.assertGreater(len(python_warnings), 0)
    
    def test_analyze_report_no_virtual_env_warning(self):
        """测试分析报告 - 无虚拟环境警告"""
        system_info = SystemInfo(
            os_type=SystemType.LINUX,
            os_version="Ubuntu 20.04",
            python_version="3.11.0",
            architecture="x86_64",
            cpu_count=4,
            memory_gb=8.0,
            available_space_gb=50.0
        )
        
        env_status = EnvironmentStatus(
            env_type=EnvironmentType.DEVELOPMENT,
            is_virtual_env=False  # 不在虚拟环境中
        )
        
        report = EnvironmentReport(
            system_info=system_info,
            env_status=env_status
        )
        
        self.detector._analyze_report(report)
        
        # 应该有虚拟环境警告
        venv_warnings = [w for w in report.warnings if "虚拟环境" in w]
        self.assertGreater(len(venv_warnings), 0)
    
    def test_analyze_report_missing_dependencies(self):
        """测试分析报告 - 缺失依赖错误"""
        system_info = SystemInfo(
            os_type=SystemType.LINUX,
            os_version="Ubuntu 20.04",
            python_version="3.11.0",
            architecture="x86_64",
            cpu_count=4,
            memory_gb=8.0,
            available_space_gb=50.0
        )
        
        env_status = EnvironmentStatus(
            env_type=EnvironmentType.DEVELOPMENT,
            is_virtual_env=True
        )
        
        # 添加缺失的必需依赖
        missing_dep = DependencyStatus(
            name="fastapi",
            required=True,
            installed=False
        )
        
        report = EnvironmentReport(
            system_info=system_info,
            env_status=env_status,
            python_deps=[missing_dep]
        )
        
        self.detector._analyze_report(report)
        
        # 应该有缺失依赖错误
        dep_errors = [e for e in report.errors if "缺少必需的Python依赖" in e]
        self.assertGreater(len(dep_errors), 0)
    
    def test_analyze_report_low_memory_warning(self):
        """测试分析报告 - 低内存警告"""
        system_info = SystemInfo(
            os_type=SystemType.LINUX,
            os_version="Ubuntu 20.04",
            python_version="3.11.0",
            architecture="x86_64",
            cpu_count=4,
            memory_gb=2.0,  # 低内存
            available_space_gb=50.0
        )
        
        env_status = EnvironmentStatus(
            env_type=EnvironmentType.DEVELOPMENT,
            is_virtual_env=True
        )
        
        report = EnvironmentReport(
            system_info=system_info,
            env_status=env_status
        )
        
        self.detector._analyze_report(report)
        
        # 应该有内存警告
        memory_warnings = [w for w in report.warnings if "内存" in w]
        self.assertGreater(len(memory_warnings), 0)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    @patch('utils.env_detector.get_config_manager')
    @patch('utils.env_detector.get_ui')
    @patch('utils.env_detector.Paths')
    def test_generate_report_integration(self, mock_paths, mock_get_ui, mock_get_config):
        """测试生成完整报告的集成"""
        # 设置模拟
        mock_config = MagicMock()
        mock_config.get_environment.return_value = "development"
        mock_get_config.return_value = mock_config
        
        mock_ui = MagicMock()
        mock_get_ui.return_value = mock_ui
        
        mock_paths_instance = MagicMock()
        mock_paths_instance.ROOT = Path("/test")
        mock_paths.return_value = mock_paths_instance
        
        # 创建检测器
        detector = EnvDetector()
        
        # 模拟各种检测方法
        with patch.object(detector, 'detect_system_info') as mock_system, \
             patch.object(detector, 'detect_environment_status') as mock_env, \
             patch.object(detector, 'check_python_dependencies') as mock_python, \
             patch.object(detector, 'check_system_dependencies') as mock_system_deps:
            
            # 设置返回值
            mock_system.return_value = SystemInfo(
                os_type=SystemType.MACOS,
                os_version="macOS 12.0",
                python_version="3.11.0",
                architecture="arm64",
                cpu_count=8,
                memory_gb=16.0,
                available_space_gb=100.0
            )
            
            mock_env.return_value = EnvironmentStatus(
                env_type=EnvironmentType.DEVELOPMENT,
                is_virtual_env=True,
                venv_path=Path("/test/.venv"),
                package_manager="pnpm",
                node_version="18.17.0",
                npm_version="9.6.7"
            )
            
            mock_python.return_value = [
                DependencyStatus(name="fastapi", required=True, installed=True, version="0.104.1")
            ]
            
            mock_system_deps.return_value = [
                DependencyStatus(name="node", required=True, installed=True, version="18.17.0")
            ]
            
            # 生成报告
            report = detector.generate_report()
            
            # 验证报告结构
            self.assertIsInstance(report, EnvironmentReport)
            self.assertEqual(report.system_info.os_type, SystemType.MACOS)
            self.assertEqual(report.env_status.env_type, EnvironmentType.DEVELOPMENT)
            self.assertGreater(len(report.python_deps), 0)
            self.assertGreater(len(report.system_deps), 0)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)