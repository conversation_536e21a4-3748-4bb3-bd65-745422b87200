#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA CLI美化系统单元测试

测试CLI用户界面美化功能
版本: 1.0.0
创建时间: 2025-07-31
"""

import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path
import sys
import io

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.cli_ui import (
    Color, Icon, Theme, CLIUI,
    get_ui, print_header, print_step, print_success,
    print_error, print_warning, print_info
)


class TestColor(unittest.TestCase):
    """颜色枚举测试"""
    
    def test_color_values(self):
        """测试颜色值"""
        self.assertEqual(Color.RESET.value, "\033[0m")
        self.assertEqual(Color.RED.value, "\033[31m")
        self.assertEqual(Color.GREEN.value, "\033[32m")
        self.assertEqual(Color.BRIGHT_BLUE.value, "\033[94m")


class TestIcon(unittest.TestCase):
    """图标枚举测试"""
    
    def test_icon_values(self):
        """测试图标值"""
        self.assertEqual(Icon.SUCCESS.value, "✅")
        self.assertEqual(Icon.ERROR.value, "❌")
        self.assertEqual(Icon.WARNING.value, "⚠️")
        self.assertEqual(Icon.ROCKET.value, "🚀")


class TestTheme(unittest.TestCase):
    """主题测试"""
    
    def test_default_theme(self):
        """测试默认主题"""
        theme = Theme()
        
        self.assertEqual(theme.primary, Color.BRIGHT_BLUE)
        self.assertEqual(theme.success, Color.BRIGHT_GREEN)
        self.assertEqual(theme.warning, Color.BRIGHT_YELLOW)
        self.assertEqual(theme.error, Color.BRIGHT_RED)


class TestCLIUI(unittest.TestCase):
    """CLI UI测试"""
    
    def setUp(self):
        """测试前准备"""
        self.ui = CLIUI()
        # 重定向标准输出以捕获打印内容
        self.output = io.StringIO()
        self.original_stdout = sys.stdout
        sys.stdout = self.output
    
    def tearDown(self):
        """测试后清理"""
        sys.stdout = self.original_stdout
    
    def get_output(self) -> str:
        """获取捕获的输出"""
        return self.output.getvalue()
    
    def test_ui_initialization(self):
        """测试UI初始化"""
        self.assertIsInstance(self.ui.theme, Theme)
        self.assertIsInstance(self.ui.supports_color, bool)
        self.assertIsInstance(self.ui.width, int)
        self.assertGreaterEqual(self.ui.width, 80)
    
    @patch('os.getenv')
    def test_color_support_detection(self, mock_getenv):
        """测试颜色支持检测"""
        # 测试NO_COLOR环境变量
        mock_getenv.side_effect = lambda x, default=None: "1" if x == "NO_COLOR" else default
        ui = CLIUI()
        # 由于debug模式强制启用颜色，这里应该还是True
        self.assertTrue(ui.supports_color)
        
        # 测试FORCE_COLOR环境变量
        mock_getenv.side_effect = lambda x, default=None: "1" if x == "FORCE_COLOR" else default
        ui = CLIUI()
        self.assertTrue(ui.supports_color)
    
    def test_colorize(self):
        """测试颜色化文本"""
        # 测试带颜色支持
        self.ui.supports_color = True
        result = self.ui.colorize("test", Color.RED)
        self.assertIn("\033[31m", result)  # 红色代码
        self.assertIn("\033[0m", result)   # 重置代码
        self.assertIn("test", result)
        
        # 测试无颜色支持
        self.ui.supports_color = False
        result = self.ui.colorize("test", Color.RED)
        self.assertEqual(result, "test")
    
    def test_print_header(self):
        """测试打印标题"""
        self.ui.print_header("测试标题", "副标题")
        output = self.get_output()
        
        self.assertIn("🚀", output)  # 火箭图标
        self.assertIn("测试标题", output)
        self.assertIn("副标题", output)
        self.assertIn("═", output)  # 分隔线
    
    def test_print_section(self):
        """测试打印节标题"""
        self.ui.print_section("节标题")
        output = self.get_output()
        
        self.assertIn("◆", output)  # 钻石图标
        self.assertIn("节标题", output)
    
    def test_print_step(self):
        """测试打印步骤"""
        self.ui.print_step("成功步骤", "success")
        self.ui.print_step("错误步骤", "error")
        self.ui.print_step("警告步骤", "warning")
        
        output = self.get_output()
        
        self.assertIn("✅", output)  # 成功图标
        self.assertIn("❌", output)  # 错误图标
        self.assertIn("⚠️", output)  # 警告图标
        self.assertIn("成功步骤", output)
        self.assertIn("错误步骤", output)
        self.assertIn("警告步骤", output)
    
    def test_print_key_value(self):
        """测试打印键值对"""
        self.ui.print_key_value("键", "值")
        output = self.get_output()
        
        self.assertIn("键:", output)
        self.assertIn("值", output)
    
    def test_print_list(self):
        """测试打印列表"""
        items = ["项目1", "项目2", "项目3"]
        self.ui.print_list(items)
        
        output = self.get_output()
        
        self.assertIn("•", output)  # 默认子弹图标
        for item in items:
            self.assertIn(item, output)
    
    def test_print_table(self):
        """测试打印表格"""
        headers = ["列1", "列2", "列3"]
        rows = [
            ["值1", "值2", "值3"],
            ["值4", "值5", "值6"]
        ]
        
        self.ui.print_table(headers, rows)
        output = self.get_output()
        
        # 检查表头
        for header in headers:
            self.assertIn(header, output)
        
        # 检查表格内容
        for row in rows:
            for cell in row:
                self.assertIn(cell, output)
        
        # 检查分隔线
        self.assertIn("─", output)
    
    def test_print_progress_bar(self):
        """测试打印进度条"""
        self.ui.print_progress_bar(50, 100, "进度")
        output = self.get_output()
        
        self.assertIn("进度", output)
        self.assertIn("50/100", output)
        self.assertIn("50.0%", output)
        self.assertIn("█", output)  # 填充字符
        self.assertIn("░", output)  # 空字符
    
    def test_print_box(self):
        """测试打印边框框"""
        self.ui.print_box("测试内容", "标题", "info")
        output = self.get_output()
        
        self.assertIn("测试内容", output)
        self.assertIn("标题", output)
        self.assertIn("┌", output)  # 左上角
        self.assertIn("┐", output)  # 右上角  
        self.assertIn("└", output)  # 左下角
        self.assertIn("┘", output)  # 右下角
    
    def test_print_banner(self):
        """测试打印横幅"""
        self.ui.print_banner("横幅文本", "primary")
        output = self.get_output()
        
        self.assertIn("横幅文本", output)
        self.assertIn("=", output)  # 边框字符
    
    @patch('time.sleep')
    def test_print_spinner(self, mock_sleep):
        """测试打印旋转器"""
        # 由于旋转器有时间控制，我们模拟sleep
        self.ui.print_spinner("加载中", 0.1)
        output = self.get_output()
        
        self.assertIn("加载中", output)
        # 应该有完成标记
        self.assertIn("✅", output)
    
    @patch('builtins.input', return_value='y')
    def test_confirm_yes(self, mock_input):
        """测试确认对话框 - 是"""
        result = self.ui.confirm("是否继续")
        self.assertTrue(result)
        
        # 检查提示信息
        mock_input.assert_called_once()
        call_args = mock_input.call_args[0][0]
        self.assertIn("❓", call_args)
        self.assertIn("是否继续", call_args)
        self.assertIn("[Y/n]", call_args)
    
    @patch('builtins.input', return_value='n')
    def test_confirm_no(self, mock_input):
        """测试确认对话框 - 否"""
        result = self.ui.confirm("是否继续")
        self.assertFalse(result)
    
    @patch('builtins.input', return_value='')
    def test_confirm_default(self, mock_input):
        """测试确认对话框 - 默认值"""
        result = self.ui.confirm("是否继续", default=True)
        self.assertTrue(result)
        
        result = self.ui.confirm("是否继续", default=False)
        self.assertFalse(result)


class TestConvenienceFunctions(unittest.TestCase):
    """便捷函数测试"""
    
    def setUp(self):
        """测试前准备"""
        # 重定向标准输出
        self.output = io.StringIO()
        self.original_stdout = sys.stdout
        sys.stdout = self.output
        
        # 重置全局UI实例
        import utils.cli_ui
        utils.cli_ui._ui_instance = None
    
    def tearDown(self):
        """测试后清理"""
        sys.stdout = self.original_stdout
    
    def get_output(self) -> str:
        """获取捕获的输出"""
        return self.output.getvalue()
    
    def test_get_ui_singleton(self):
        """测试UI单例模式"""
        ui1 = get_ui()
        ui2 = get_ui()
        
        self.assertIs(ui1, ui2)
        self.assertIsInstance(ui1, CLIUI)
    
    def test_print_header_function(self):
        """测试打印标题便捷函数"""
        print_header("测试", "副标题")
        output = self.get_output()
        
        self.assertIn("🚀", output)
        self.assertIn("测试", output)
        self.assertIn("副标题", output)
    
    def test_print_step_function(self):
        """测试打印步骤便捷函数"""
        print_step("步骤", "info")
        output = self.get_output()
        
        self.assertIn("ℹ️", output)
        self.assertIn("步骤", output)
    
    def test_status_functions(self):
        """测试状态便捷函数"""
        print_success("成功消息")
        print_error("错误消息")
        print_warning("警告消息")
        print_info("信息消息")
        
        output = self.get_output()
        
        self.assertIn("✅", output)  # 成功
        self.assertIn("❌", output)  # 错误
        self.assertIn("⚠️", output)  # 警告
        self.assertIn("ℹ️", output)  # 信息
        
        self.assertIn("成功消息", output)
        self.assertIn("错误消息", output)
        self.assertIn("警告消息", output)
        self.assertIn("信息消息", output)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.ui = CLIUI()
        self.output = io.StringIO()
        self.original_stdout = sys.stdout
        sys.stdout = self.output
    
    def tearDown(self):
        """测试后清理"""
        sys.stdout = self.original_stdout
    
    def get_output(self) -> str:
        """获取捕获的输出"""
        return self.output.getvalue()
    
    def test_complete_ui_workflow(self):
        """测试完整UI工作流程"""
        # 模拟一个完整的AQUA启动流程UI显示
        
        # 1. 横幅和标题
        self.ui.print_banner("AQUA 系统启动", "primary")
        self.ui.print_header("初始化过程", "正在启动所有服务")
        
        # 2. 配置信息
        self.ui.print_section("系统配置")
        self.ui.print_key_value("环境", "开发")
        self.ui.print_key_value("版本", "2.0.0")
        
        # 3. 启动步骤
        self.ui.print_section("服务启动")
        self.ui.print_step("初始化路径管理", "success")
        self.ui.print_step("加载配置文件", "success")
        self.ui.print_step("连接数据库", "success")
        self.ui.print_step("启动缓存服务", "success")
        
        # 4. 服务状态表格
        self.ui.print_section("服务状态")
        headers = ["服务", "状态", "端口"]
        rows = [
            ["API服务", "运行中", "8000"],
            ["前端服务", "运行中", "5173"]
        ]
        self.ui.print_table(headers, rows)
        
        # 5. 完成提示
        self.ui.print_box(
            "所有服务启动成功！\n系统已准备就绪",
            "启动完成",
            "success"
        )
        
        # 验证输出包含所有预期元素
        output = self.get_output()
        
        # 验证各种UI元素都存在
        self.assertIn("AQUA 系统启动", output)
        self.assertIn("🚀", output)  # 标题图标
        self.assertIn("◆", output)  # 节标题图标
        self.assertIn("✅", output)  # 成功图标
        self.assertIn("环境:", output)  # 键值对
        self.assertIn("服务", output)  # 表格头
        self.assertIn("┌", output)  # 边框
        self.assertIn("═", output)  # 分隔线
        
        # 验证内容
        self.assertIn("初始化路径管理", output)
        self.assertIn("API服务", output)
        self.assertIn("所有服务启动成功", output)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)