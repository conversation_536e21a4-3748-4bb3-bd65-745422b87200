#!/usr/bin/env python3
"""
智能路由引擎测试套件

测试功能：
1. 数据类型自动识别和路由决策
2. 路由规则的动态配置和热更新
3. 数据质量检查和路由验证
4. 多目标表的并行路由支持
5. 负载均衡和故障转移
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
from datetime import datetime
import asyncio

from src.routing.smart_routing_engine import SmartRoutingEngine
from src.routing.routing_strategy import RoutingStrategy
from src.data_import.processors.data_quality_controller import DataQualityController


class TestSmartRoutingEngine:
    """智能路由引擎测试类"""

    @pytest.fixture
    def temp_dir(self):
        """创建临时测试目录"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def routing_config(self):
        """路由配置"""
        return {
            'routing': {
                'default_strategy': 'smart_detection',
                'enable_load_balancing': True,
                'enable_parallel_routing': True,
                'conflict_resolution_timeout': 30,
                'max_parallel_routes': 5
            },
            'data_sources': {
                'tushare': {
                    'priority': 1,
                    'timeout': 30
                },
                'csv': {
                    'priority': 2,
                    'timeout': 60
                }
            },
            'target_tables': {
                'futures_daily': {
                    'schema': 'v4_business',
                    'partition_key': 'trade_date',
                    'indexes': ['contract_code', 'trade_date']
                },
                'stock_daily': {
                    'schema': 'v4_business',
                    'partition_key': 'trade_date',
                    'indexes': ['symbol', 'trade_date']
                }
            }
        }

    @pytest.fixture
    def routing_engine(self, routing_config):
        """创建智能路由引擎实例"""
        return SmartRoutingEngine(routing_config)

    @pytest.fixture
    def sample_futures_data(self):
        """期货样本数据"""
        return pd.DataFrame({
            'ts_code': ['FU2403.SHF', 'FU2404.SHF'],
            'trade_date': ['********', '********'],
            'open': [3000.0, 3050.0],
            'high': [3020.0, 3080.0],
            'low': [2990.0, 3040.0],
            'close': [3010.0, 3070.0],
            'vol': [125000, 128000],
            'amount': [**********, **********]
        })

    @pytest.fixture
    def sample_stock_data(self):
        """股票样本数据"""
        return pd.DataFrame({
            'ts_code': ['000001.SZ', '000002.SZ'],
            'trade_date': ['********', '********'],
            'open': [12.50, 12.80],
            'high': [12.65, 12.95],
            'low': [12.45, 12.75],
            'close': [12.60, 12.90],
            'vol': [5000000, 5200000],
            'amount': [********, ********]
        })

    def test_init_routing_engine(self, routing_engine):
        """测试路由引擎初始化"""
        assert routing_engine is not None
        assert hasattr(routing_engine, 'routing_strategies')
        assert hasattr(routing_engine, 'quality_controller')
        assert hasattr(routing_engine, 'load_balancer')
        assert hasattr(routing_engine, 'rule_manager')

    def test_data_type_auto_detection(self, routing_engine, sample_futures_data, sample_stock_data):
        """测试数据类型自动识别"""
        # 测试期货数据识别
        futures_detection = routing_engine.detect_data_type(sample_futures_data)
        assert futures_detection['data_type'] == 'futures'
        assert futures_detection['confidence'] >= 0.9
        assert futures_detection['frequency'] == 'daily'
        assert 'detection_reasons' in futures_detection
        
        # 测试股票数据识别
        stock_detection = routing_engine.detect_data_type(sample_stock_data)
        assert stock_detection['data_type'] == 'stock'
        assert stock_detection['confidence'] >= 0.9
        assert stock_detection['frequency'] == 'daily'
        
        # 测试未知数据类型
        unknown_data = pd.DataFrame({
            'col1': [1, 2, 3],
            'col2': ['a', 'b', 'c']
        })
        unknown_detection = routing_engine.detect_data_type(unknown_data)
        assert unknown_detection['data_type'] == 'unknown'
        assert unknown_detection['confidence'] < 0.5

    def test_frequency_detection(self, routing_engine):
        """测试数据频率识别"""
        # 测试1分钟数据
        min1_data = pd.DataFrame({
            'ts_code': ['FU2403.SHF'] * 240,
            'trade_date': ['********'] * 240,
            'trade_time': [f'09:{30 + i // 4:02d}:{(i % 4) * 15:02d}' for i in range(240)],
            'open': [3000.0 + i for i in range(240)]
        })
        
        freq_result = routing_engine.detect_frequency(min1_data)
        assert freq_result['frequency'] == '1min'
        assert freq_result['confidence'] >= 0.9
        
        # 测试5分钟数据
        min5_data = pd.DataFrame({
            'ts_code': ['FU2403.SHF'] * 48,
            'trade_date': ['********'] * 48,
            'trade_time': [f'09:{30 + i * 5:02d}:00' for i in range(48)],
            'open': [3000.0 + i for i in range(48)]
        })
        
        freq_result = routing_engine.detect_frequency(min5_data)
        assert freq_result['frequency'] == '5min'
        
        # 测试日线数据
        daily_data = pd.DataFrame({
            'ts_code': ['FU2403.SHF'] * 30,
            'trade_date': [f'2024{1:02d}{i+1:02d}' for i in range(30)],
            'open': [3000.0 + i for i in range(30)]
        })
        
        freq_result = routing_engine.detect_frequency(daily_data)
        assert freq_result['frequency'] == 'daily'

    def test_routing_decision_making(self, routing_engine, sample_futures_data):
        """测试路由决策制定"""
        # 测试基本路由决策
        routing_decision = routing_engine.make_routing_decision(
            data=sample_futures_data,
            source_type='tushare',
            quality_check=True
        )
        
        assert routing_decision['target_table'] == 'futures_daily'
        assert routing_decision['routing_strategy'] == 'smart_detection'
        assert routing_decision['data_transformations'] is not None
        assert routing_decision['quality_passed'] is True
        
        # 测试多目标路由
        multi_target_decision = routing_engine.make_routing_decision(
            data=sample_futures_data,
            source_type='tushare',
            enable_multi_target=True
        )
        
        assert len(multi_target_decision['target_tables']) >= 1
        assert 'futures_daily' in multi_target_decision['target_tables']
        
        # 测试路由策略选择
        custom_decision = routing_engine.make_routing_decision(
            data=sample_futures_data,
            source_type='tushare',
            strategy='performance_optimized'
        )
        
        assert custom_decision['routing_strategy'] == 'performance_optimized'

    def test_dynamic_rule_configuration(self, routing_engine):
        """测试路由规则的动态配置"""
        # 添加新的路由规则
        new_rule = {
            'rule_id': 'custom_futures_rule',
            'conditions': {
                'data_type': 'futures',
                'source_type': 'custom_api',
                'min_confidence': 0.8
            },
            'actions': {
                'target_table': 'custom_futures_daily',
                'transformations': ['normalize_contract_code', 'convert_timezone'],
                'priority': 1
            }
        }
        
        result = routing_engine.add_routing_rule(new_rule)
        assert result['success'] is True
        assert result['rule_id'] == 'custom_futures_rule'
        
        # 测试规则应用
        custom_data = pd.DataFrame({
            'contract_code': ['CU2403', 'CU2404'],
            'trade_date': ['********', '********'],
            'open': [70000, 70500]
        })
        
        decision = routing_engine.make_routing_decision(
            data=custom_data,
            source_type='custom_api'
        )
        
        # 验证新规则被应用
        assert decision['matched_rule'] == 'custom_futures_rule'
        
        # 测试规则热更新
        updated_rule = new_rule.copy()
        updated_rule['actions']['priority'] = 2
        
        update_result = routing_engine.update_routing_rule('custom_futures_rule', updated_rule)
        assert update_result['success'] is True
        
        # 测试规则删除
        delete_result = routing_engine.remove_routing_rule('custom_futures_rule')
        assert delete_result['success'] is True

    def test_data_quality_integration(self, routing_engine, sample_futures_data):
        """测试数据质量检查集成"""
        # 测试质量检查通过的情况
        quality_result = routing_engine.check_data_quality(
            data=sample_futures_data,
            data_type='futures'
        )
        
        assert quality_result['quality_passed'] is True
        assert quality_result['quality_score'] >= 0.8
        assert 'quality_issues' in quality_result
        
        # 测试质量检查失败的情况
        bad_data = sample_futures_data.copy()
        bad_data.loc[0, 'high'] = -100.0  # 无效的高价
        bad_data.loc[1, 'open'] = None    # 缺失的开盘价
        
        bad_quality_result = routing_engine.check_data_quality(
            data=bad_data,
            data_type='futures'
        )
        
        assert bad_quality_result['quality_passed'] is False
        assert len(bad_quality_result['quality_issues']) > 0
        
        # 测试质量阈值配置
        routing_engine.set_quality_threshold(0.95)
        
        threshold_result = routing_engine.check_data_quality(
            data=sample_futures_data,
            data_type='futures'
        )
        
        # 根据新阈值判断是否通过
        expected_pass = threshold_result['quality_score'] >= 0.95
        assert threshold_result['quality_passed'] == expected_pass

    def test_parallel_routing(self, routing_engine, sample_futures_data):
        """测试多目标表的并行路由"""
        # 准备多个目标表配置
        multi_targets = [
            'futures_daily',
            'futures_1min',
            'futures_5min'
        ]
        
        # 测试并行路由
        parallel_result = routing_engine.route_to_multiple_targets(
            data=sample_futures_data,
            target_tables=multi_targets,
            parallel=True
        )
        
        assert parallel_result['success'] is True
        assert len(parallel_result['results']) == len(multi_targets)
        assert parallel_result['execution_mode'] == 'parallel'
        assert parallel_result['total_time'] < parallel_result['sequential_estimate']
        
        # 验证每个目标的路由结果
        for target, result in parallel_result['results'].items():
            assert target in multi_targets
            assert result['status'] in ['success', 'failed']
            if result['status'] == 'success':
                assert 'transformed_data' in result
                assert 'execution_time' in result

    def test_load_balancing(self, routing_engine):
        """测试负载均衡"""
        # 模拟多个存储后端
        storage_backends = ['backend1', 'backend2', 'backend3']
        routing_engine.register_storage_backends(storage_backends)
        
        # 测试负载均衡策略
        for strategy in ['round_robin', 'least_connections', 'random']:
            routing_engine.set_load_balancing_strategy(strategy)
            
            selected_backends = []
            for i in range(10):
                backend = routing_engine.select_storage_backend()
                selected_backends.append(backend)
            
            # 验证负载分布
            if strategy == 'round_robin':
                # 轮询策略应该均匀分布
                assert len(set(selected_backends)) == len(storage_backends)
            
            assert all(backend in storage_backends for backend in selected_backends)
        
        # 测试后端健康检查
        health_status = routing_engine.check_backend_health()
        assert 'healthy_backends' in health_status
        assert 'unhealthy_backends' in health_status
        
        # 测试故障后端的自动排除
        routing_engine.mark_backend_unhealthy('backend2')
        
        excluded_selections = []
        for i in range(20):
            backend = routing_engine.select_storage_backend()
            excluded_selections.append(backend)
        
        # 验证故障后端被排除
        assert 'backend2' not in excluded_selections

    def test_routing_performance_optimization(self, routing_engine):
        """测试路由性能优化"""
        # 准备大量测试数据
        large_data = pd.DataFrame({
            'ts_code': [f'FU24{i:02d}.SHF' for i in range(1000)],
            'trade_date': ['********'] * 1000,
            'open': [3000.0 + i for i in range(1000)],
            'close': [3020.0 + i for i in range(1000)]
        })
        
        # 测试路由缓存
        routing_engine.enable_routing_cache(cache_size=100)
        
        # 第一次路由（无缓存）
        start_time = datetime.now()
        first_result = routing_engine.make_routing_decision(
            data=large_data,
            source_type='tushare'
        )
        first_time = (datetime.now() - start_time).total_seconds()
        
        # 第二次路由（有缓存）
        start_time = datetime.now()
        cached_result = routing_engine.make_routing_decision(
            data=large_data,
            source_type='tushare'
        )
        cached_time = (datetime.now() - start_time).total_seconds()
        
        # 验证缓存效果
        assert cached_time < first_time * 0.5  # 缓存应该显著提高性能
        assert cached_result['cache_hit'] is True
        
        # 测试批量路由优化
        batch_data = [large_data[i:i+100] for i in range(0, 1000, 100)]
        
        batch_result = routing_engine.route_batch(
            batch_data=batch_data,
            optimize_for_throughput=True
        )
        
        assert batch_result['success'] is True
        assert batch_result['total_records'] == 1000
        assert batch_result['batch_count'] == 10
        assert batch_result['throughput_records_per_second'] > 100

    def test_conflict_resolution(self, routing_engine, sample_futures_data):
        """测试冲突解决"""
        # 模拟数据冲突情况
        conflicting_data = sample_futures_data.copy()
        conflicting_data.loc[0, 'close'] = 9999.0  # 与现有数据冲突
        
        # 测试冲突检测
        conflict_result = routing_engine.detect_conflicts(
            new_data=conflicting_data,
            target_table='futures_daily',
            match_keys=['ts_code', 'trade_date']
        )
        
        assert conflict_result['has_conflicts'] is True
        assert len(conflict_result['conflicts']) > 0
        assert 'conflict_details' in conflict_result
        
        # 测试不同的冲突解决策略
        strategies = ['overwrite', 'skip', 'merge', 'interactive']
        
        for strategy in strategies:
            resolution_result = routing_engine.resolve_conflicts(
                conflicts=conflict_result['conflicts'],
                strategy=strategy,
                priority_rules={'source_priority': {'tushare': 1, 'csv': 2}}
            )
            
            assert resolution_result['strategy'] == strategy
            assert 'resolved_data' in resolution_result
            
            if strategy == 'interactive':
                # 模拟用户交互选择
                assert 'user_choices_required' in resolution_result
            else:
                assert resolution_result['auto_resolved'] is True

    def test_routing_monitoring_and_metrics(self, routing_engine):
        """测试路由监控和指标"""
        # 启用路由监控
        routing_engine.enable_monitoring(
            metrics=['throughput', 'latency', 'error_rate', 'queue_depth']
        )
        
        # 执行一些路由操作来生成指标
        test_data = pd.DataFrame({
            'ts_code': ['TEST001'],
            'trade_date': ['********'],
            'open': [100.0]
        })
        
        for i in range(10):
            routing_engine.make_routing_decision(
                data=test_data,
                source_type='tushare'
            )
        
        # 获取监控指标
        metrics = routing_engine.get_monitoring_metrics()
        
        assert 'throughput' in metrics
        assert 'average_latency' in metrics
        assert 'error_rate' in metrics
        assert 'total_routes' in metrics
        assert metrics['total_routes'] >= 10
        
        # 测试性能报告生成
        performance_report = routing_engine.generate_performance_report(
            time_range='last_hour'
        )
        
        assert 'summary' in performance_report
        assert 'detailed_metrics' in performance_report
        assert 'recommendations' in performance_report

    def test_error_handling_and_recovery(self, routing_engine):
        """测试错误处理和恢复"""
        # 测试无效数据处理
        invalid_data = pd.DataFrame({'invalid': [None, None]})
        
        error_result = routing_engine.make_routing_decision(
            data=invalid_data,
            source_type='unknown'
        )
        
        assert error_result['success'] is False
        assert 'error_code' in error_result
        assert 'error_message' in error_result
        
        # 测试路由重试机制
        with patch.object(routing_engine, '_execute_routing') as mock_execute:
            mock_execute.side_effect = [
                ConnectionError("Connection failed"),
                ConnectionError("Connection failed"),
                {'success': True}  # 第三次成功
            ]
            
            retry_result = routing_engine.make_routing_decision_with_retry(
                data=sample_futures_data,
                source_type='tushare',
                max_retries=3
            )
            
            assert retry_result['success'] is True
            assert retry_result['retry_count'] == 2
        
        # 测试路由队列容错
        routing_engine.set_queue_mode('failsafe')
        
        queue_result = routing_engine.queue_routing_request(
            data=sample_futures_data,
            priority='high',
            timeout=30
        )
        
        assert queue_result['queued'] is True
        assert 'queue_position' in queue_result

    def test_routing_audit_and_logging(self, routing_engine, sample_futures_data):
        """测试路由审计和日志"""
        # 启用详细审计
        routing_engine.enable_audit_logging(
            level='detailed',
            include_data_samples=True
        )
        
        # 执行路由操作
        routing_result = routing_engine.make_routing_decision(
            data=sample_futures_data,
            source_type='tushare'
        )
        
        # 获取审计日志
        audit_logs = routing_engine.get_audit_logs(
            time_range='last_minute'
        )
        
        assert len(audit_logs) >= 1
        
        latest_log = audit_logs[0]
        assert 'timestamp' in latest_log
        assert 'operation' in latest_log
        assert 'source_type' in latest_log
        assert 'target_table' in latest_log
        assert 'data_hash' in latest_log
        
        # 测试审计报告生成
        audit_report = routing_engine.generate_audit_report(
            start_date='2024-01-15',
            end_date='2024-01-16'
        )
        
        assert 'total_operations' in audit_report
        assert 'success_rate' in audit_report
        assert 'data_volume_processed' in audit_report
        assert 'top_source_types' in audit_report