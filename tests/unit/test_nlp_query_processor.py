#!/usr/bin/env python3
"""
自然语言查询处理器测试

测试覆盖：
1. NLP查询解析和SQL生成
2. 数据库查询执行和结果处理
3. 查询缓存和性能优化
4. 错误处理和异常情况
5. 复用UnifiedStorageManager功能
"""

import pytest
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import tempfile
import json

# 假设的导入路径 - 基于架构设计
from src.ai_agent.nlp_query_processor import NLPQueryProcessor
from src.ai_agent.ai_strategy import NLPQueryStrategy
from src.storage.unified_storage_manager import UnifiedStorageManager
from src.utils.logger import get_logger


class TestNLPQueryProcessor:
    """NLP查询处理器测试类"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        return {
            "database": {
                "environments": {
                    "test": "test_ai.db"
                },
                "default_environment": "test",
                "connection_pool_size": 3
            },
            "ai_components": {
                "nlp_model_path": "models/test_nlp.pkl",
                "query_cache_size": 100,
                "max_query_length": 1000,
                "supported_languages": ["zh", "en"]
            },
            "nlp_query": {
                "confidence_threshold": 0.8,
                "max_results": 1000,
                "enable_cache": True,
                "cache_ttl_seconds": 3600
            }
        }
    
    @pytest.fixture
    def sample_market_data(self):
        """样本市场数据"""
        return pd.DataFrame({
            "ts_code": ["000001.SZ", "000002.SZ", "600000.SH"],
            "trade_date": ["20240115", "20240115", "20240115"],
            "open": [10.50, 25.30, 8.80],
            "high": [10.80, 25.50, 9.10],
            "low": [10.30, 25.10, 8.70],
            "close": [10.65, 25.40, 8.95],
            "vol": [1000000, 800000, 1200000]
        })
    
    @pytest.fixture
    def nlp_processor(self, mock_config):
        """创建NLP查询处理器实例"""
        with patch('src.storage.unified_storage_manager.UnifiedStorageManager'):
            return NLPQueryProcessor(mock_config)
    
    def test_nlp_processor_initialization(self, mock_config):
        """测试NLP处理器初始化"""
        with patch('src.storage.unified_storage_manager.UnifiedStorageManager') as mock_storage:
            processor = NLPQueryProcessor(mock_config)
            
            # 验证存储管理器被正确初始化
            mock_storage.assert_called_once_with(mock_config)
            assert processor.config == mock_config
            assert processor.nlp_config == mock_config["nlp_query"]
            assert processor.query_cache == {}
            assert processor.performance_metrics == {}
    
    def test_parse_natural_query_basic_stock_query(self, nlp_processor):
        """测试基础股票查询解析"""
        # Given: 自然语言股票查询
        natural_query = "查询平安银行最近5天的股价数据"
        
        # When: 解析查询
        with patch.object(nlp_processor, '_extract_entities') as mock_extract:
            mock_extract.return_value = {
                "stock_name": "平安银行",
                "time_range": "5天",
                "data_type": "股价数据",
                "ts_code": "000001.SZ"
            }
            
            result = nlp_processor.parse_natural_query(natural_query)
        
        # Then: 验证解析结果
        assert result["success"] is True
        assert result["parsed_intent"] == "query_stock_data"
        assert result["entities"]["stock_name"] == "平安银行"
        assert result["entities"]["time_range"] == "5天"
        assert result["confidence"] >= 0.8
    
    def test_parse_natural_query_complex_analytical_query(self, nlp_processor):
        """测试复杂分析查询解析"""
        # Given: 复杂的分析性查询
        natural_query = "分析科技股在过去一个月的平均涨幅，按市值排序"
        
        # When: 解析复杂查询
        with patch.object(nlp_processor, '_extract_entities') as mock_extract:
            mock_extract.return_value = {
                "sector": "科技股",
                "time_range": "一个月",
                "analysis_type": "平均涨幅",
                "sort_criteria": "市值",
                "sort_order": "desc"
            }
            
            result = nlp_processor.parse_natural_query(natural_query)
        
        # Then: 验证复杂查询解析
        assert result["success"] is True
        assert result["parsed_intent"] == "analytical_query"
        assert result["entities"]["analysis_type"] == "平均涨幅"
        assert result["query_complexity"] == "high"
    
    def test_generate_sql_from_parsed_query(self, nlp_processor):
        """测试从解析结果生成SQL"""
        # Given: 解析后的查询结构
        parsed_query = {
            "intent": "query_stock_data",
            "entities": {
                "ts_code": "000001.SZ",
                "time_range": "5天",
                "fields": ["open", "high", "low", "close", "vol"]
            },
            "filters": {
                "start_date": "20240110",
                "end_date": "20240115"
            }
        }
        
        # When: 生成SQL
        sql = nlp_processor.generate_sql(parsed_query)
        
        # Then: 验证生成的SQL
        assert "SELECT" in sql.upper()
        assert "FROM stock_daily" in sql
        assert "WHERE ts_code = '000001.SZ'" in sql
        assert "trade_date BETWEEN '20240110' AND '20240115'" in sql
        assert all(field in sql for field in ["open", "high", "low", "close", "vol"])
    
    def test_execute_nlp_query_success(self, nlp_processor, sample_market_data):
        """测试成功执行NLP查询"""
        # Given: 自然语言查询
        natural_query = "查询平安银行今日股价"
        
        # Mock storage manager查询
        nlp_processor.storage_manager.query_data = Mock(return_value=sample_market_data)
        
        # When: 执行查询
        with patch.object(nlp_processor, 'parse_natural_query') as mock_parse:
            mock_parse.return_value = {
                "success": True,
                "parsed_intent": "query_stock_data",
                "entities": {"ts_code": "000001.SZ"},
                "confidence": 0.9
            }
            
            with patch.object(nlp_processor, 'generate_sql') as mock_sql:
                mock_sql.return_value = "SELECT * FROM stock_daily WHERE ts_code = '000001.SZ'"
                
                result = nlp_processor.execute_nlp_query(natural_query)
        
        # Then: 验证查询结果
        assert result["success"] is True
        assert not result["data"].empty
        assert result["query_confidence"] == 0.9
        assert result["execution_time"] > 0
        assert "cache_key" in result
    
    def test_execute_nlp_query_with_cache(self, nlp_processor, sample_market_data):
        """测试查询缓存功能"""
        # Given: 已缓存的查询
        natural_query = "查询平安银行股价"
        cache_key = nlp_processor._generate_cache_key(natural_query)
        cached_result = {
            "data": sample_market_data,
            "cached_at": datetime.now(),
            "query_confidence": 0.9
        }
        nlp_processor.query_cache[cache_key] = cached_result
        
        # When: 再次执行相同查询
        result = nlp_processor.execute_nlp_query(natural_query)
        
        # Then: 验证返回缓存结果
        assert result["success"] is True
        assert result["cache_hit"] is True
        assert result["query_confidence"] == 0.9
        pd.testing.assert_frame_equal(result["data"], sample_market_data)
    
    def test_execute_nlp_query_cache_expiry(self, nlp_processor):
        """测试缓存过期处理"""
        # Given: 过期的缓存条目
        natural_query = "查询股价数据"
        cache_key = nlp_processor._generate_cache_key(natural_query)
        expired_time = datetime.now() - timedelta(hours=2)
        nlp_processor.query_cache[cache_key] = {
            "data": pd.DataFrame(),
            "cached_at": expired_time,
            "query_confidence": 0.8
        }
        
        # When: 执行查询
        with patch.object(nlp_processor, '_is_cache_valid') as mock_valid:
            mock_valid.return_value = False
            
            with patch.object(nlp_processor, 'parse_natural_query') as mock_parse:
                mock_parse.return_value = {"success": False, "error": "解析失败"}
                
                result = nlp_processor.execute_nlp_query(natural_query)
        
        # Then: 验证缓存已清理
        assert cache_key not in nlp_processor.query_cache
        assert result["cache_hit"] is False
    
    def test_handle_query_parsing_failure(self, nlp_processor):
        """测试查询解析失败处理"""
        # Given: 无法解析的查询
        invalid_query = "这是一个无效的查询内容###@@@"
        
        # When: 尝试解析
        with patch.object(nlp_processor, '_extract_entities') as mock_extract:
            mock_extract.side_effect = Exception("NLP模型解析失败")
            
            result = nlp_processor.parse_natural_query(invalid_query)
        
        # Then: 验证错误处理
        assert result["success"] is False
        assert result["error_type"] == "parsing_failed"
        assert "NLP模型解析失败" in result["error_message"]
        assert result["confidence"] == 0.0
    
    def test_handle_sql_generation_failure(self, nlp_processor):
        """测试SQL生成失败处理"""
        # Given: 无法转换为SQL的解析结果
        invalid_parsed = {
            "intent": "unsupported_operation",
            "entities": {"invalid_field": "unknown"}
        }
        
        # When: 尝试生成SQL
        with patch.object(nlp_processor, '_validate_sql_generation') as mock_validate:
            mock_validate.return_value = False
            
            with pytest.raises(ValueError, match="无法生成有效SQL"):
                nlp_processor.generate_sql(invalid_parsed)
    
    def test_handle_database_query_failure(self, nlp_processor):
        """测试数据库查询失败处理"""
        # Given: 会导致数据库错误的SQL
        natural_query = "查询数据"
        
        # Mock数据库连接错误
        nlp_processor.storage_manager.query_data = Mock(
            side_effect=Exception("数据库连接失败")
        )
        
        # When: 执行查询
        with patch.object(nlp_processor, 'parse_natural_query') as mock_parse:
            mock_parse.return_value = {
                "success": True,
                "parsed_intent": "query_stock_data",
                "confidence": 0.8
            }
            
            with patch.object(nlp_processor, 'generate_sql') as mock_sql:
                mock_sql.return_value = "SELECT * FROM non_existent_table"
                
                result = nlp_processor.execute_nlp_query(natural_query)
        
        # Then: 验证错误处理
        assert result["success"] is False
        assert result["error_type"] == "database_error"
        assert "数据库连接失败" in result["error_message"]
    
    def test_query_performance_monitoring(self, nlp_processor, sample_market_data):
        """测试查询性能监控"""
        # Given: 启用性能监控
        nlp_processor.enable_performance_monitoring = True
        nlp_processor.storage_manager.query_data = Mock(return_value=sample_market_data)
        
        # When: 执行多个查询
        queries = [
            "查询平安银行股价",
            "查询万科A股价",
            "查询招商银行股价"
        ]
        
        for query in queries:
            with patch.object(nlp_processor, 'parse_natural_query') as mock_parse:
                mock_parse.return_value = {
                    "success": True,
                    "parsed_intent": "query_stock_data",
                    "confidence": 0.9
                }
                
                with patch.object(nlp_processor, 'generate_sql') as mock_sql:
                    mock_sql.return_value = "SELECT * FROM stock_daily"
                    
                    nlp_processor.execute_nlp_query(query)
        
        # Then: 验证性能指标收集
        metrics = nlp_processor.get_performance_metrics()
        assert metrics["total_queries"] == 3
        assert metrics["average_execution_time"] > 0
        assert metrics["cache_hit_rate"] >= 0
        assert "query_distribution" in metrics
    
    def test_multilingual_query_support(self, nlp_processor):
        """测试多语言查询支持"""
        # Given: 英文查询
        english_query = "Show me Apple stock price for last week"
        
        # When: 解析英文查询
        with patch.object(nlp_processor, '_detect_language') as mock_detect:
            mock_detect.return_value = "en"
            
            with patch.object(nlp_processor, '_extract_entities') as mock_extract:
                mock_extract.return_value = {
                    "stock_name": "Apple",
                    "time_range": "last week",
                    "ts_code": "AAPL.US"
                }
                
                result = nlp_processor.parse_natural_query(english_query)
        
        # Then: 验证多语言支持
        assert result["success"] is True
        assert result["detected_language"] == "en"
        assert result["entities"]["stock_name"] == "Apple"
    
    def test_query_optimization_suggestions(self, nlp_processor):
        """测试查询优化建议"""
        # Given: 可优化的查询
        inefficient_query = "查询所有股票过去一年每天的详细数据包括成交量价格等所有信息"
        
        # When: 解析查询并生成优化建议
        with patch.object(nlp_processor, '_analyze_query_complexity') as mock_analyze:
            mock_analyze.return_value = {
                "complexity_score": 0.9,
                "estimated_records": 100000,
                "performance_risk": "high"
            }
            
            result = nlp_processor.parse_natural_query(inefficient_query)
        
        # Then: 验证优化建议
        assert "optimization_suggestions" in result
        suggestions = result["optimization_suggestions"]
        assert "consider_date_range_filter" in suggestions
        assert "consider_specific_stocks" in suggestions
        assert result["performance_warning"] is True
    
    def test_concurrent_query_execution(self, nlp_processor, sample_market_data):
        """测试并发查询执行"""
        # Given: 多个并发查询
        nlp_processor.storage_manager.query_data = Mock(return_value=sample_market_data)
        
        # When: 模拟并发执行
        with patch.object(nlp_processor, 'parse_natural_query') as mock_parse:
            mock_parse.return_value = {
                "success": True,
                "parsed_intent": "query_stock_data",
                "confidence": 0.9
            }
            
            with patch.object(nlp_processor, 'generate_sql') as mock_sql:
                mock_sql.return_value = "SELECT * FROM stock_daily"
                
                import threading
                results = []
                
                def execute_query(query_id):
                    result = nlp_processor.execute_nlp_query(f"查询股票{query_id}")
                    results.append(result)
                
                threads = []
                for i in range(5):
                    thread = threading.Thread(target=execute_query, args=(i,))
                    threads.append(thread)
                    thread.start()
                
                for thread in threads:
                    thread.join()
        
        # Then: 验证并发执行结果
        assert len(results) == 5
        assert all(result["success"] for result in results)
    
    def test_query_result_formatting(self, nlp_processor, sample_market_data):
        """测试查询结果格式化"""
        # Given: 查询结果需要格式化
        nlp_processor.storage_manager.query_data = Mock(return_value=sample_market_data)
        
        # When: 执行查询并格式化结果
        with patch.object(nlp_processor, 'parse_natural_query') as mock_parse:
            mock_parse.return_value = {
                "success": True,
                "parsed_intent": "query_stock_data",
                "confidence": 0.9,
                "output_format": "summary"
            }
            
            with patch.object(nlp_processor, 'generate_sql') as mock_sql:
                mock_sql.return_value = "SELECT * FROM stock_daily"
                
                result = nlp_processor.execute_nlp_query(
                    "给我看看平安银行的股价摘要", 
                    output_format="summary"
                )
        
        # Then: 验证结果格式化
        assert result["success"] is True
        assert "formatted_result" in result
        assert result["output_format"] == "summary"
        assert "data_summary" in result["formatted_result"]


class TestNLPQueryStrategy:
    """NLP查询策略测试"""
    
    @pytest.fixture
    def nlp_strategy(self):
        """创建NLP查询策略实例"""
        return NLPQueryStrategy()
    
    def test_nlp_strategy_execution(self, nlp_strategy):
        """测试NLP策略执行"""
        # Given: 查询和上下文
        query = "查询股价数据"
        context = {
            "user_id": "test_user",
            "session_id": "test_session",
            "preferences": {"language": "zh"}
        }
        
        # When: 执行策略
        with patch.object(nlp_strategy, '_process_query') as mock_process:
            mock_process.return_value = {
                "success": True,
                "data": pd.DataFrame({"result": [1, 2, 3]}),
                "confidence": 0.9
            }
            
            result = nlp_strategy.execute(query, context)
        
        # Then: 验证策略执行结果
        assert result["success"] is True
        assert result["strategy_type"] == "nlp_query"
        assert result["confidence"] == 0.9
    
    def test_nlp_strategy_config_validation(self, nlp_strategy):
        """测试NLP策略配置验证"""
        # Given: 有效配置
        valid_config = {
            "nlp_model_path": "models/nlp.pkl",
            "confidence_threshold": 0.8,
            "max_query_length": 1000
        }
        
        # When: 验证配置
        result = nlp_strategy.validate_config(valid_config)
        
        # Then: 验证通过
        assert result is True
        
        # Given: 无效配置
        invalid_config = {
            "nlp_model_path": "models/nlp.pkl"
            # 缺少必需的配置项
        }
        
        # When: 验证无效配置
        result = nlp_strategy.validate_config(invalid_config)
        
        # Then: 验证失败
        assert result is False


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])