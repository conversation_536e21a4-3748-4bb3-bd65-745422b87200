import unittest
from unittest.mock import patch, MagicMock
import sys
import os
import platform

# 将项目根目录添加到sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from src.aqua.cli import init_helpers

class TestInitHelpers(unittest.TestCase):

    @patch('subprocess.run')
    def test_check_uv_installed_success(self, mock_run):
        """测试当uv已安装时，函数返回True"""
        mock_run.return_value = MagicMock(returncode=0, stdout="uv 0.1.0")
        self.assertTrue(init_helpers.check_uv_installed())
        mock_run.assert_called_once_with(['uv', '--version'], check=True, capture_output=True, text=True)

    @patch('subprocess.run', side_effect=FileNotFoundError)
    def test_check_uv_installed_fail(self, mock_run):
        """测试当uv未安装时，函数返回False"""
        self.assertFalse(init_helpers.check_uv_installed())
        mock_run.assert_called_once_with(['uv', '--version'], check=True, capture_output=True, text=True)

    @patch('sys.executable', new='python_path')
    @patch('subprocess.run')
    @patch('src.aqua.cli.init_helpers.check_uv_installed', side_effect=[False, True])
    def test_install_uv_success(self, mock_check_uv, mock_run):
        """测试 install_uv 函数能否成功调用pip安装uv"""
        init_helpers.install_uv()
        # 验证pip安装命令被调用
        mock_run.assert_called_once_with(
            ['python_path', '-m', 'pip', 'install', 'uv'],
            check=True, capture_output=True, text=True
        )
        # 验证 check_uv_installed 被调用了两次
        self.assertEqual(mock_check_uv.call_count, 2)

    @patch('shutil.rmtree')
    @patch('pathlib.Path.resolve')
    @patch('pathlib.Path.is_dir', return_value=True)
    def test_clean_other_venvs(self, mock_is_dir, mock_resolve, mock_rmtree):
        """测试清理多余虚拟环境的逻辑"""
        # 模拟检测到两个venv
        venvs = [MagicMock(), MagicMock()]
        project_venv_path = MagicMock()
        
        # 确保两个模拟的venv路径与主venv路径不相等
        venvs[0].resolve.return_value = 'path/to/other/venv'
        venvs[1].resolve.return_value = 'path/to/another/venv'
        project_venv_path.resolve.return_value = '/path/to/project/.venv'

        init_helpers.clean_other_venvs(venvs, project_venv_path)
        
        # 验证rmtree被调用了两次
        self.assertEqual(mock_rmtree.call_count, 2)

    @patch('os.execv')
    def test_reexec_in_venv_posix(self, mock_execv):
        """测试在非Windows系统上，reexec_in_venv 调用 os.execv"""
        if platform.system() == "Windows":
            self.skipTest("This test is for non-Windows systems.")
            
        with patch('platform.system', return_value='Linux'), \
             patch('sys.executable', new='/usr/bin/python'), \
             patch('pathlib.Path.exists', return_value=True):
            
            # 模拟不在venv中
            init_helpers.VENV_PATH = MagicMock()
            init_helpers.VENV_PATH.resolve.return_value = '/project/.venv'
            
            init_helpers.reexec_in_venv()
            mock_execv.assert_called_once()

    @patch('subprocess.Popen')
    def test_reexec_in_venv_windows(self, mock_popen):
        """测试在Windows系统上，reexec_in_venv 调用 subprocess.Popen"""
        # This test is designed to run on non-Windows systems by mocking the platform
        with patch('platform.system', return_value='Windows'), \
             patch('sys.executable', new='C:\\Python\\python.exe'), \
             patch('pathlib.Path.exists', return_value=True), \
             patch('src.aqua.cli.init_helpers.VENV_PATH', new=MagicMock()):

            init_helpers.VENV_PATH.resolve.return_value = 'C:\\project\\.venv'
            
            mock_process = MagicMock()
            mock_popen.return_value = mock_process
            
            with self.assertRaises(SystemExit):
                 init_helpers.reexec_in_venv()

            mock_popen.assert_called_once()
            mock_process.wait.assert_called_once()

    @patch('subprocess.run')
    def test_create_project_venv(self, mock_run):
        """测试创建项目虚拟环境的函数"""
        with patch('pathlib.Path.exists', return_value=False):
            init_helpers.create_project_venv()
            mock_run.assert_called_once_with(
                [sys.executable, '-m', 'venv', init_helpers.VENV_PATH],
                check=True
            )

    @patch('subprocess.run')
    def test_sync_dependencies(self, mock_run):
        """测试同步依赖的函数"""
        with patch('pathlib.Path.exists', return_value=True):
            # 模拟中国网络环境以测试镜像源逻辑
            with patch('src.aqua.cli.init_helpers.is_china_network', return_value=True):
                 self.assertTrue(init_helpers.sync_dependencies())
                 # 验证是否使用了镜像
                 self.assertIn('-i', mock_run.call_args[0][0])
                 self.assertIn('https://pypi.tuna.tsinghua.edu.cn/simple', mock_run.call_args[0][0])

    @patch('duckdb.connect')
    def test_init_database(self, mock_connect):
        """测试数据库初始化函数"""
        with patch('pathlib.Path.exists', return_value=False), \
             patch('pathlib.Path.mkdir'):
            init_helpers.init_database(env='dev')
            mock_connect.assert_called_once()
            # 验证数据库路径是否正确
            self.assertIn('aqua_dev.duckdb', str(mock_connect.call_args[0][0]))

if __name__ == '__main__':
    unittest.main()
