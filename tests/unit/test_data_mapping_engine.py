#!/usr/bin/env python3
"""
数据映射引擎测试模块
基于TDD原则的测试驱动开发

测试覆盖：
1. TUSHARE字段到V4.0业务表的完整映射
2. 多种数据源格式转换
3. 数据类型标准化
"""

import pytest
import pandas as pd
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, List, Any

# 导入被测试的模块
try:
    from src.data_import.processors.data_mapping_engine import DataMappingEngine
    from src.data_import.processors.field_mapper import FieldMapper
    from src.data_import.processors.type_converter import TypeConverter
except ImportError:
    # 测试时的导入路径
    import sys
    sys.path.append('/Users/<USER>/Documents/AQUA/Dev/AQUA/src')
    from data_import.processors.data_mapping_engine import DataMappingEngine
    from data_import.processors.field_mapper import FieldMapper
    from data_import.processors.type_converter import TypeConverter


class TestDataMappingEngine:
    """数据映射引擎测试类"""
    
    @pytest.fixture
    def mapping_engine(self):
        """创建映射引擎实例"""
        return DataMappingEngine()
    
    @pytest.fixture
    def tushare_futures_data(self):
        """模拟TUSHARE期货数据"""
        return pd.DataFrame({
            'ts_code': ['AL2501.SHF', 'RB2501.SHF'],
            'trade_date': ['20250129', '20250129'],
            'open': [19750.0, 4200.0],
            'high': [19850.0, 4250.0],
            'low': [19700.0, 4180.0],
            'close': [19800.0, 4220.0],
            'vol': [123456, 234567],
            'amount': [2450000000.0, 9876000000.0],
            'oi': [789012, 890123]
        })
    
    @pytest.fixture
    def tushare_stock_data(self):
        """模拟TUSHARE股票数据"""
        return pd.DataFrame({
            'ts_code': ['000001.SZ', '600000.SH'],
            'trade_date': ['20250129', '20250129'],
            'open': [12.50, 8.90],
            'high': [12.80, 9.20],
            'low': [12.30, 8.75],
            'close': [12.65, 9.05],
            'vol': [1234567, 2345678],
            'amount': [15678900.0, 21234567.0],
            'turnover_rate': [1.23, 2.45]
        })
    
    def test_engine_initialization(self, mapping_engine):
        """测试映射引擎初始化"""
        assert mapping_engine is not None
        assert hasattr(mapping_engine, 'field_mapper')
        assert hasattr(mapping_engine, 'type_converter')
        assert isinstance(mapping_engine.field_mapper, FieldMapper)
        assert isinstance(mapping_engine.type_converter, TypeConverter)
    
    def test_tushare_futures_field_mapping(self, mapping_engine, tushare_futures_data):
        """测试TUSHARE期货字段映射"""
        # 执行字段映射
        mapped_data = mapping_engine.map_fields(
            data=tushare_futures_data,
            source_type='tushare',
            target_type='v4_business_table',
            data_category='futures',
            frequency='daily'
        )
        
        # 验证目标字段名
        expected_fields = [
            'contract_code', 'trade_date', 'open', 'high', 'low', 
            'close', 'volume', 'amount', 'open_interest'
        ]
        
        for field in expected_fields:
            assert field in mapped_data.columns, f"Missing field: {field}"
        
        # 验证字段值映射正确性
        assert mapped_data['contract_code'].iloc[0] == 'AL2501.SHF'
        assert mapped_data['volume'].iloc[0] == 123456
        assert mapped_data['open_interest'].iloc[0] == 789012
    
    def test_tushare_stock_field_mapping(self, mapping_engine, tushare_stock_data):
        """测试TUSHARE股票字段映射"""
        mapped_data = mapping_engine.map_fields(
            data=tushare_stock_data,
            source_type='tushare',
            target_type='v4_business_table',
            data_category='stock',
            frequency='daily'
        )
        
        # 验证目标字段名
        expected_fields = [
            'symbol', 'trade_date', 'open', 'high', 'low', 
            'close', 'volume', 'amount', 'turnover_rate'
        ]
        
        for field in expected_fields:
            assert field in mapped_data.columns, f"Missing field: {field}"
        
        # 验证字段值映射正确性
        assert mapped_data['symbol'].iloc[0] == '000001.SZ'
        assert mapped_data['turnover_rate'].iloc[0] == 1.23
    
    def test_data_type_conversion(self, mapping_engine, tushare_futures_data):
        """测试数据类型转换"""
        # 执行类型转换
        converted_data = mapping_engine.convert_types(
            data=tushare_futures_data,
            source_type='tushare',
            target_type='v4_business_table',
            data_category='futures'
        )
        
        # 验证数据类型
        assert converted_data['ts_code'].dtype == 'object'  # string
        assert pd.api.types.is_datetime64_any_dtype(converted_data['trade_date']) or converted_data['trade_date'].dtype == 'object'
        assert pd.api.types.is_numeric_dtype(converted_data['open'])
        assert pd.api.types.is_integer_dtype(converted_data['vol'])
        assert pd.api.types.is_numeric_dtype(converted_data['amount'])
    
    def test_batch_mapping(self, mapping_engine):
        """测试批量数据映射"""
        # 准备多批次数据
        batch_data = {
            'futures_batch_1': pd.DataFrame({
                'ts_code': ['AL2501.SHF'],
                'trade_date': ['20250129'],
                'open': [19750.0],
                'high': [19850.0],
                'low': [19700.0],
                'close': [19800.0],
                'vol': [123456],
                'amount': [2450000000.0],
                'oi': [789012]
            }),
            'stock_batch_1': pd.DataFrame({
                'ts_code': ['000001.SZ'],
                'trade_date': ['20250129'],
                'open': [12.50],
                'high': [12.80],
                'low': [12.30],
                'close': [12.65],
                'vol': [1234567],
                'amount': [15678900.0],
                'turnover_rate': [1.23]
            })
        }
        
        mapping_config = {
            'futures_batch_1': {'source_type': 'tushare', 'data_category': 'futures', 'frequency': 'daily'},
            'stock_batch_1': {'source_type': 'tushare', 'data_category': 'stock', 'frequency': 'daily'}
        }
        
        # 执行批量映射
        results = mapping_engine.process_batch(
            batch_data=batch_data,
            mapping_config=mapping_config,
            target_type='v4_business_table'
        )
        
        # 验证批量处理结果
        assert len(results) == 2
        assert 'futures_batch_1' in results
        assert 'stock_batch_1' in results
        
        # 验证期货批次结果
        futures_result = results['futures_batch_1']
        assert 'contract_code' in futures_result.columns
        assert 'open_interest' in futures_result.columns
        
        # 验证股票批次结果
        stock_result = results['stock_batch_1']
        assert 'symbol' in stock_result.columns
        assert 'turnover_rate' in stock_result.columns
    
    def test_custom_mapping_rules(self, mapping_engine):
        """测试自定义映射规则"""
        # 定义自定义映射规则
        custom_rules = {
            'field_mapping': {
                'custom_code': 'contract_code',
                'custom_date': 'trade_date',
                'custom_vol': 'volume'
            },
            'type_conversion': {
                'custom_code': 'string',
                'custom_date': 'date',
                'custom_vol': 'integer'
            }
        }
        
        # 注册自定义规则
        mapping_engine.register_custom_rules('custom_source', custom_rules)
        
        # 测试数据
        test_data = pd.DataFrame({
            'custom_code': ['TEST001'],
            'custom_date': ['20250129'],
            'custom_vol': [100000]
        })
        
        # 执行自定义映射
        mapped_data = mapping_engine.map_fields(
            data=test_data,
            source_type='custom_source',
            target_type='v4_business_table',
            data_category='custom',
            frequency='daily'
        )
        
        # 验证自定义映射结果
        assert 'contract_code' in mapped_data.columns
        assert 'trade_date' in mapped_data.columns
        assert 'volume' in mapped_data.columns
        assert mapped_data['contract_code'].iloc[0] == 'TEST001'
    
    def test_validation_and_error_handling(self, mapping_engine):
        """测试验证和错误处理"""
        # 测试空数据处理
        with pytest.raises(ValueError, match="数据不能为空"):
            mapping_engine.map_fields(
                data=pd.DataFrame(),
                source_type='tushare',
                target_type='v4_business_table',
                data_category='futures',
                frequency='daily'
            )
        
        # 测试不支持的数据源类型
        test_data = pd.DataFrame({'test': [1]})
        with pytest.raises(ValueError, match="不支持的数据源类型"):
            mapping_engine.map_fields(
                data=test_data,
                source_type='unsupported_source',
                target_type='v4_business_table',
                data_category='futures',
                frequency='daily'
            )
        
        # 测试缺失必需字段的处理
        incomplete_data = pd.DataFrame({
            'ts_code': ['AL2501.SHF'],
            # 缺少其他必需字段
        })
        
        with pytest.raises(ValueError, match="缺少必需字段"):
            mapping_engine.map_fields(
                data=incomplete_data,
                source_type='tushare',
                target_type='v4_business_table',
                data_category='futures',
                frequency='daily'
            )
    
    def test_mapping_performance(self, mapping_engine):
        """测试映射性能"""
        # 创建大量数据
        large_data = pd.DataFrame({
            'ts_code': ['AL2501.SHF'] * 50000,
            'trade_date': ['20250129'] * 50000,
            'open': [19750.0] * 50000,
            'high': [19850.0] * 50000,
            'low': [19700.0] * 50000,
            'close': [19800.0] * 50000,
            'vol': [123456] * 50000,
            'amount': [2450000000.0] * 50000,
            'oi': [789012] * 50000
        })
        
        import time
        start_time = time.time()
        
        mapped_data = mapping_engine.map_fields(
            data=large_data,
            source_type='tushare',
            target_type='v4_business_table',
            data_category='futures',
            frequency='daily'
        )
        
        processing_time = time.time() - start_time
        
        # 验证处理性能（应该在10秒内完成）
        assert processing_time < 10.0, f"Mapping took too long: {processing_time}s"
        assert len(mapped_data) == 50000
        assert 'contract_code' in mapped_data.columns
    
    def test_cross_platform_compatibility(self, mapping_engine):
        """测试跨平台兼容性"""
        # 测试不同平台的日期格式处理
        date_formats = [
            '20250129',    # YYYYMMDD
            '2025-01-29',  # YYYY-MM-DD
            '2025/01/29',  # YYYY/MM/DD
            '29/01/2025',  # DD/MM/YYYY
            '01/29/2025'   # MM/DD/YYYY
        ]
        
        for date_format in date_formats:
            test_data = pd.DataFrame({
                'ts_code': ['AL2501.SHF'],
                'trade_date': [date_format],
                'open': [19750.0],
                'high': [19850.0],
                'low': [19700.0],
                'close': [19800.0],
                'vol': [123456],
                'amount': [2450000000.0],
                'oi': [789012]
            })
            
            # 应该能够成功处理所有日期格式
            try:
                mapped_data = mapping_engine.map_fields(
                    data=test_data,
                    source_type='tushare',
                    target_type='v4_business_table',
                    data_category='futures',
                    frequency='daily'
                )
                assert len(mapped_data) == 1
                assert 'trade_date' in mapped_data.columns
            except Exception as e:
                pytest.fail(f"Failed to process date format {date_format}: {e}")


class TestFieldMapper:
    """字段映射器测试类"""
    
    @pytest.fixture
    def field_mapper(self):
        """创建字段映射器实例"""
        return FieldMapper()
    
    def test_get_mapping_rules(self, field_mapper):
        """测试获取映射规则"""
        # 获取TUSHARE期货映射规则
        futures_rules = field_mapper.get_mapping_rules('tushare', 'futures', 'daily')
        
        assert 'ts_code' in futures_rules
        assert futures_rules['ts_code'] == 'contract_code'
        assert futures_rules['vol'] == 'volume'
        assert futures_rules['oi'] == 'open_interest'
        
        # 获取股票映射规则
        stock_rules = field_mapper.get_mapping_rules('tushare', 'stock', 'daily')
        
        assert 'ts_code' in stock_rules
        assert stock_rules['ts_code'] == 'symbol'
        assert stock_rules['vol'] == 'volume'
    
    def test_apply_field_mapping(self, field_mapper):
        """测试应用字段映射"""
        original_data = pd.DataFrame({
            'ts_code': ['AL2501.SHF'],
            'vol': [123456],
            'oi': [789012]
        })
        
        mapped_data = field_mapper.apply_mapping(
            data=original_data,
            source_type='tushare',
            data_category='futures',
            frequency='daily'
        )
        
        # 验证映射结果
        assert 'contract_code' in mapped_data.columns
        assert 'volume' in mapped_data.columns
        assert 'open_interest' in mapped_data.columns
        
        # 验证原始字段不存在
        assert 'ts_code' not in mapped_data.columns
        assert 'vol' not in mapped_data.columns
        assert 'oi' not in mapped_data.columns


class TestTypeConverter:
    """类型转换器测试类"""
    
    @pytest.fixture
    def type_converter(self):
        """创建类型转换器实例"""
        return TypeConverter()
    
    def test_date_conversion(self, type_converter):
        """测试日期类型转换"""
        test_dates = pd.Series(['20250129', '20250130', '20250131'])
        
        converted_dates = type_converter.convert_to_date(test_dates)
        
        assert pd.api.types.is_datetime64_any_dtype(converted_dates) or all(isinstance(d, (date, datetime)) for d in converted_dates)
        assert len(converted_dates) == 3
    
    def test_numeric_conversion(self, type_converter):
        """测试数值类型转换"""
        # 测试整数转换
        test_integers = pd.Series(['123456', '234567', '345678'])
        converted_integers = type_converter.convert_to_integer(test_integers)
        
        assert pd.api.types.is_integer_dtype(converted_integers)
        assert converted_integers.iloc[0] == 123456
        
        # 测试浮点数转换
        test_floats = pd.Series(['123.45', '234.56', '345.67'])
        converted_floats = type_converter.convert_to_float(test_floats)
        
        assert pd.api.types.is_float_dtype(converted_floats)
        assert abs(converted_floats.iloc[0] - 123.45) < 0.001
    
    def test_string_conversion(self, type_converter):
        """测试字符串类型转换"""
        test_data = pd.Series([123, 456, 789])
        converted_strings = type_converter.convert_to_string(test_data)
        
        assert converted_strings.dtype == 'object'
        assert converted_strings.iloc[0] == '123'


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
