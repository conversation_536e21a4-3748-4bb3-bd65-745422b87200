"""
AQUA CLI 增强功能测试用例
验证新增的优化功能是否正常工作
"""
import pytest
import os
import sys
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typer.testing import CliRunner

# 添加src路径到sys.path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

from aqua.main import app
from aqua.cli.setup_wizard import SetupWizard, EnvironmentDetector
from aqua.cli.health_checker import HealthChecker, HealthStatus
from aqua.cli.enhanced_ui import EnhancedUI, CommandHistory
from aqua.cli.windows_compat import WindowsCompatibilityManager
from aqua.cli.dev_tools import QualityAnalyzer, PreCommitManager

runner = CliRunner()

class TestSetupWizard:
    """测试智能配置向导"""
    
    def test_environment_detector_platform(self):
        """测试平台检测"""
        detector = EnvironmentDetector()
        platform_type = detector.detect_platform()
        assert platform_type in ["windows", "unix"]
    
    def test_environment_detector_memory(self):
        """测试内存检测"""
        detector = EnvironmentDetector()
        memory = detector.detect_memory()
        assert isinstance(memory, int)
        assert memory > 0
    
    def test_environment_detector_network(self):
        """测试网络环境检测"""
        detector = EnvironmentDetector()
        network_type = detector.check_network_speed()
        assert network_type in ["china", "international"]
    
    @patch('aqua.cli.setup_wizard.os.getenv')
    @patch('aqua.cli.setup_wizard.Path')
    def test_data_sources_detection(self, mock_path, mock_getenv):
        """测试数据源检测"""
        # 模拟TUSHARE TOKEN存在
        mock_getenv.return_value = "test_token"
        
        # 模拟CSV路径存在
        mock_path_instance = Mock()
        mock_path_instance.expanduser.return_value.exists.return_value = True
        mock_path.return_value = mock_path_instance
        
        detector = EnvironmentDetector()
        sources = detector.detect_data_sources()
        
        assert isinstance(sources, list)
        # 应该至少检测到一些数据源
        assert len(sources) >= 0
    
    def test_setup_wizard_initialization(self):
        """测试设置向导初始化"""
        wizard = SetupWizard()
        assert wizard.detector is not None
        assert wizard.console is not None

class TestHealthChecker:
    """测试系统健康检查"""
    
    def test_health_checker_initialization(self):
        """测试健康检查器初始化"""
        checker = HealthChecker()
        assert checker.console is not None
        assert checker.project_root.exists()
        assert len(checker.checks) > 0
    
    def test_python_environment_check(self):
        """测试Python环境检查"""
        checker = HealthChecker()
        result = checker._check_python_environment()
        
        assert result.name == "Python版本"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.ERROR]
        assert result.message is not None
    
    def test_virtual_environment_check(self):
        """测试虚拟环境检查"""
        checker = HealthChecker()
        result = checker._check_virtual_environment()
        
        assert result.name == "虚拟环境"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.ERROR]
        assert result.message is not None
    
    def test_dependencies_check(self):
        """测试依赖检查"""
        checker = HealthChecker()
        result = checker._check_dependencies()
        
        assert result.name == "项目依赖"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.ERROR]
        assert result.message is not None
    
    @patch('aqua.cli.health_checker.Path')
    def test_configuration_check(self, mock_path):
        """测试配置文件检查"""
        # 模拟配置文件存在
        mock_config_file = Mock()
        mock_config_file.exists.return_value = True
        mock_path.return_value = mock_config_file
        
        checker = HealthChecker()
        result = checker._check_configuration()
        
        assert result.name == "配置文件"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.ERROR, HealthStatus.CRITICAL]
    
    def test_platform_compatibility_check(self):
        """测试平台兼容性检查"""
        checker = HealthChecker()
        result = checker._check_platform_compatibility()
        
        assert result.name == "平台兼容性"
        assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING]
        assert result.message is not None

class TestEnhancedUI:
    """测试增强用户界面"""
    
    def test_enhanced_ui_initialization(self):
        """测试增强UI初始化"""
        ui = EnhancedUI()
        assert ui.console is not None
        assert ui.history is not None
        assert ui.prompt is not None
        assert ui.progress is not None
        assert ui.error_handler is not None
    
    def test_command_history(self):
        """测试命令历史"""
        history = CommandHistory()
        
        # 测试历史文件路径
        assert history.history_file.name == "command_history.json"
        assert history.history_file.parent.name == ".aqua"
        
        # 测试添加操作
        from aqua.cli.enhanced_ui import UserAction
        from datetime import datetime
        
        action = UserAction(
            timestamp=datetime.now(),
            command="test_command",
            parameters={"param1": "value1"},
            result="success",
            duration=1.5
        )
        
        initial_count = len(history.actions)
        history.add_action(action)
        assert len(history.actions) == initial_count + 1
    
    def test_smart_prompt_initialization(self):
        """测试智能提示初始化"""
        from aqua.cli.enhanced_ui import SmartPrompt
        
        prompt = SmartPrompt()
        assert prompt.console is not None
        assert 'environment' in prompt.suggestions
        assert 'data_source' in prompt.suggestions
    
    def test_progress_tracker(self):
        """测试进度跟踪器"""
        from aqua.cli.enhanced_ui import ProgressTracker
        
        tracker = ProgressTracker()
        assert tracker.console is not None
        
        # 测试创建进度条
        progress = tracker.create_enhanced_progress("Test")
        assert progress is not None

class TestWindowsCompatibility:
    """测试Windows兼容性"""
    
    def test_windows_compat_manager_initialization(self):
        """测试Windows兼容性管理器初始化"""
        manager = WindowsCompatibilityManager()
        assert manager.console is not None
        assert isinstance(manager.is_windows, bool)
        assert isinstance(manager.is_admin, bool)
    
    @patch('aqua.cli.windows_compat.platform.system')
    def test_non_windows_platform(self, mock_system):
        """测试非Windows平台"""
        mock_system.return_value = "Darwin"
        
        manager = WindowsCompatibilityManager()
        assert not manager.is_windows
        
        # 非Windows平台应该返回not_windows状态
        results = manager.setup_enhanced_compatibility()
        assert results.get("status") == "not_windows"
    
    @patch('aqua.cli.windows_compat.platform.system')
    def test_windows_platform_detection(self, mock_system):
        """测试Windows平台检测"""
        mock_system.return_value = "Windows"
        
        manager = WindowsCompatibilityManager()
        assert manager.is_windows
    
    def test_compatibility_status_check(self):
        """测试兼容性状态检查"""
        manager = WindowsCompatibilityManager()
        status = manager.check_compatibility_status()
        
        assert isinstance(status, dict)
        assert 'platform' in status
        
        if manager.is_windows:
            assert 'version' in status
            assert 'is_admin' in status
            assert 'encoding' in status

class TestDevTools:
    """测试开发工具链"""
    
    def test_quality_analyzer_initialization(self):
        """测试质量分析器初始化"""
        analyzer = QualityAnalyzer(project_root)
        assert analyzer.console is not None
        assert analyzer.project_root.exists()
        assert analyzer.src_path == analyzer.project_root / "src"
        assert analyzer.reports_dir == analyzer.project_root / "reports"
    
    def test_pre_commit_manager_initialization(self):
        """测试Pre-commit管理器初始化"""
        manager = PreCommitManager(project_root)
        assert manager.project_root == project_root
        assert manager.console is not None
        assert manager.pre_commit_config == project_root / ".pre-commit-config.yaml"
    
    @patch('subprocess.run')
    def test_pre_commit_installation_check(self, mock_run):
        """测试Pre-commit安装检查"""
        # 模拟pre-commit已安装
        mock_run.return_value.returncode = 0
        
        manager = PreCommitManager(project_root)
        result = manager._install_pre_commit()
        assert result is True
    
    def test_quality_metrics_creation(self):
        """测试质量指标创建"""
        from aqua.cli.dev_tools import QualityMetrics
        
        metrics = QualityMetrics(
            test_coverage=95.0,
            type_coverage=90.0,
            lint_score=85.0,
            complexity_score=80.0,
            security_score=95.0,
            performance_score=88.0
        )
        
        assert metrics.test_coverage == 95.0
        assert metrics.type_coverage == 90.0
        assert metrics.lint_score == 85.0
        assert metrics.complexity_score == 80.0
        assert metrics.security_score == 95.0
        assert metrics.performance_score == 88.0

class TestCLICommands:
    """测试CLI命令集成"""
    
    def test_main_app_initialization(self):
        """测试主应用初始化"""
        # 测试help命令
        result = runner.invoke(app, ["--help"])
        assert result.exit_code == 0
        assert "AQUA 项目统一管理工具" in result.stdout
    
    def test_setup_command_exists(self):
        """测试setup命令存在"""
        result = runner.invoke(app, ["setup", "--help"])
        assert result.exit_code == 0
        assert "智能配置向导" in result.stdout
    
    def test_doctor_command_exists(self):
        """测试doctor命令存在"""
        result = runner.invoke(app, ["doctor", "--help"])
        assert result.exit_code == 0
        assert "系统健康检查" in result.stdout
    
    def test_windows_command_exists(self):
        """测试windows命令存在"""
        result = runner.invoke(app, ["windows", "--help"])
        assert result.exit_code == 0
        assert "Windows深度兼容性管理" in result.stdout
    
    def test_dev_command_exists(self):
        """测试dev命令存在"""
        result = runner.invoke(app, ["dev", "--help"])
        assert result.exit_code == 0
        assert "开发工具链集成" in result.stdout
    
    def test_stats_command_exists(self):
        """测试stats命令存在"""
        result = runner.invoke(app, ["stats", "--help"])
        assert result.exit_code == 0
        assert "显示使用统计和历史" in result.stdout
    
    @patch('aqua.cli.enhanced_ui.enhanced_ui')
    def test_enhanced_ui_integration(self, mock_ui):
        """测试增强UI集成"""
        # 模拟增强UI方法
        mock_ui.show_welcome_banner = Mock()
        mock_ui.show_usage_stats = Mock()
        
        # 测试stats命令
        result = runner.invoke(app, ["stats"])
        
        # 应该调用show_usage_stats方法
        mock_ui.show_usage_stats.assert_called_once()

class TestIntegrationScenarios:
    """集成测试场景"""
    
    @patch('aqua.cli.health_checker.HealthChecker')
    def test_doctor_command_integration(self, mock_checker_class):
        """测试doctor命令集成"""
        # 创建模拟的健康检查器
        mock_checker = Mock()
        mock_checker_class.return_value = mock_checker
        
        # 模拟健康检查结果
        from aqua.cli.health_checker import SystemReport, HealthStatus
        mock_report = SystemReport(overall_status=HealthStatus.HEALTHY)
        mock_checker.run_full_check.return_value = mock_report
        mock_checker.display_report = Mock()
        
        # 运行doctor命令
        result = runner.invoke(app, ["doctor", "--no-auto-fix"])
        
        # 验证调用
        mock_checker.run_full_check.assert_called_once()
        mock_checker.display_report.assert_called_once()
    
    @patch('aqua.cli.setup_wizard.SetupWizard')
    def test_setup_command_integration(self, mock_wizard_class):
        """测试setup命令集成"""
        # 创建模拟的设置向导
        mock_wizard = Mock()
        mock_wizard_class.return_value = mock_wizard
        
        # 模拟配置结果
        from aqua.cli.setup_wizard import SetupConfig
        mock_config = SetupConfig(
            environment="dev",
            platform="unix",
            data_source_types=["csv", "tushare"]
        )
        mock_wizard.run.return_value = mock_config
        
        # 运行setup命令
        result = runner.invoke(app, ["setup"])
        
        # 验证调用
        mock_wizard.run.assert_called_once()
    
    def test_command_help_consistency(self):
        """测试所有命令help信息一致性"""
        commands = ["init", "start", "stop", "status", "setup", "doctor", "windows", "dev", "stats"]
        
        for command in commands:
            result = runner.invoke(app, [command, "--help"])
            assert result.exit_code == 0, f"Command {command} help failed"
            assert len(result.stdout) > 0, f"Command {command} has empty help"

class TestErrorHandling:
    """测试错误处理"""
    
    def test_enhanced_ui_error_handler(self):
        """测试增强UI错误处理"""
        from aqua.cli.enhanced_ui import ErrorHandler
        
        handler = ErrorHandler()
        assert handler.console is not None
        assert isinstance(handler.error_solutions, dict)
        
        # 测试已知错误类型
        assert 'FileNotFoundError' in handler.error_solutions
        assert 'PermissionError' in handler.error_solutions
        assert 'ImportError' in handler.error_solutions
    
    @patch('aqua.cli.enhanced_ui.enhanced_ui.error_handler')
    def test_with_enhanced_ui_decorator_error_handling(self, mock_error_handler):
        """测试增强UI装饰器错误处理"""
        from aqua.cli.enhanced_ui import with_enhanced_ui
        
        mock_error_handler.handle_error = Mock()
        
        @with_enhanced_ui
        def test_function():
            raise ValueError("Test error")
        
        # 测试函数应该抛出异常
        with pytest.raises(ValueError):
            test_function()
        
        # 错误处理器应该被调用
        mock_error_handler.handle_error.assert_called_once()

class TestPerformanceOptimization:
    """测试性能优化"""
    
    def test_command_history_size_limit(self):
        """测试命令历史大小限制"""
        history = CommandHistory()
        
        # 添加超过100条记录
        from aqua.cli.enhanced_ui import UserAction
        from datetime import datetime
        
        for i in range(150):
            action = UserAction(
                timestamp=datetime.now(),
                command=f"test_command_{i}",
                parameters={},
                result="success",
                duration=1.0
            )
            history.add_action(action)
        
        # 历史记录应该被限制在100条以内
        assert len(history.actions) <= 150  # 内存中可能保留更多，但文件中只保存100条
    
    def test_quality_analyzer_timeout_handling(self):
        """测试质量分析器超时处理"""
        analyzer = QualityAnalyzer(project_root)
        
        # 测试性能分析不应该无限期运行
        import time
        start_time = time.time()
        
        score = analyzer._analyze_performance()
        
        elapsed_time = time.time() - start_time
        
        # 性能分析应该在合理时间内完成
        assert elapsed_time < 30  # 最多30秒
        assert isinstance(score, float)
        assert 0 <= score <= 100

# 运行特定测试的辅助函数
if __name__ == "__main__":
    pytest.main([__file__, "-v"])