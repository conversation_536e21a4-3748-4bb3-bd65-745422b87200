import os
import shutil
from pathlib import Path
import sys
import pytest

# 动态导入env_init.py中的init_project_dirs
sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent / "scripts"))
from env_init import init_project_dirs, _PROJECT_ROOT

def test_init_project_dirs_creates_dirs_and_readmes(tmp_path, monkeypatch):
    """测试目录初始化能自动创建标准目录和README.md"""
    # 切换到临时目录，防止污染真实项目
    monkeypatch.chdir(tmp_path)
    # 执行目录初始化（传入tmp_path作为根目录）
    init_project_dirs(tmp_path)
    # 检查部分关键目录和README
    dirs = [
        "src/core", "frontend/src/modules", "data/raw", "logs", "tests/unit"
    ]
    for d in dirs:
        dir_path = tmp_path / d
        readme_path = dir_path / "README.md"
        assert dir_path.is_dir(), f"目录未创建: {dir_path}"
        assert readme_path.is_file(), f"README未创建: {readme_path}"
        content = readme_path.read_text(encoding="utf-8")
        assert d in content
        assert "用途" in content 