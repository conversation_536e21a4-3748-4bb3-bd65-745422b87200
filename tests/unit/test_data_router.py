#!/usr/bin/env python3
"""
数据路由API单元测试

测试数据库浏览API的各个端点功能
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from main import app
from src.database.duckdb_init_check import <PERSON><PERSON><PERSON>nitChecker
from src.database.connection_manager import DuckDBConnectionManager

client = TestClient(app)


class TestDataRouter:
    """数据路由测试类"""
    
    def test_health_check(self):
        """测试健康检查端点"""
        response = client.get("/api/data/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
        assert "timestamp" in data
    
    @patch('src.api.routers.data_router.DuckDBConnectionManager')
    def test_get_tables_success(self, mock_manager_class):
        """测试获取表列表成功"""
        # 模拟数据库管理器
        mock_manager = Mo<PERSON>()
        mock_manager.get_all_tables.return_value = ["test_table1", "test_table2"]
        mock_manager.get_table_count.return_value = 100
        mock_manager.get_table_info.return_value = [
            {
                "column_name": "id",
                "column_type": "INTEGER",
                "null": "NO",
                "key": "PRI",
                "default": None
            },
            {
                "column_name": "name",
                "column_type": "VARCHAR",
                "null": "YES",
                "key": "",
                "default": None
            }
        ]
        mock_manager_class.return_value = mock_manager
        
        # 模拟检查器
        with patch('src.api.routers.data_router.DuckDBInitChecker'):
            response = client.get("/api/data/tables")
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert "data" in data
            assert len(data["data"]) == 2
            assert data["data"][0]["name"] == "test_table1"
            assert data["data"][0]["record_count"] == 100
    
    @patch('src.api.routers.data_router.DuckDBConnectionManager')
    def test_get_table_data_success(self, mock_manager_class):
        """测试获取表数据成功"""
        # 模拟数据库管理器
        mock_manager = Mock()
        mock_manager.table_exists.return_value = True
        mock_manager.get_table_info.return_value = [
            {"column_name": "id", "column_type": "INTEGER"},
            {"column_name": "name", "column_type": "VARCHAR"}
        ]
        mock_manager.execute_query.return_value = [
            (1, "test_name1"),
            (2, "test_name2")
        ]
        mock_manager.get_table_count.return_value = 2
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/data/tables/test_table?limit=10&offset=0")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]) == 2
        assert data["data"][0]["id"] == 1
        assert data["data"][0]["name"] == "test_name1"
        assert data["pagination"]["limit"] == 10
        assert data["pagination"]["offset"] == 0
        assert data["pagination"]["total"] == 2
    
    @patch('src.api.routers.data_router.DuckDBConnectionManager')
    def test_get_table_data_not_found(self, mock_manager_class):
        """测试获取不存在的表数据"""
        mock_manager = Mock()
        mock_manager.table_exists.return_value = False
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/data/tables/non_existent_table")
        assert response.status_code == 404
        
        data = response.json()
        assert "不存在" in data["detail"]
    
    @patch('src.api.routers.data_router.DuckDBConnectionManager')
    def test_get_table_meta_success(self, mock_manager_class):
        """测试获取表元数据成功"""
        mock_manager = Mock()
        mock_manager.table_exists.return_value = True
        mock_manager.get_table_info.return_value = [
            {
                "column_name": "id",
                "column_type": "INTEGER",
                "null": "NO",
                "key": "PRI",
                "default": None
            }
        ]
        mock_manager.get_table_count.return_value = 50
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/data/tables/test_table/meta")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert data["data"]["table_name"] == "test_table"
        assert data["data"]["record_count"] == 50
        assert data["data"]["column_count"] == 1
    
    def test_get_table_data_invalid_params(self):
        """测试无效参数"""
        # 测试无效的limit参数
        response = client.get("/api/data/tables/test_table?limit=0")
        assert response.status_code == 422
        
        # 测试无效的offset参数
        response = client.get("/api/data/tables/test_table?offset=-1")
        assert response.status_code == 422
        
        # 测试超过最大limit
        response = client.get("/api/data/tables/test_table?limit=2000")
        assert response.status_code == 422
    
    @patch('src.api.routers.data_router.DuckDBConnectionManager')
    def test_get_table_data_with_columns(self, mock_manager_class):
        """测试指定列查询"""
        mock_manager = Mock()
        mock_manager.table_exists.return_value = True
        mock_manager.get_table_info.return_value = [
            {"column_name": "id", "column_type": "INTEGER"},
            {"column_name": "name", "column_type": "VARCHAR"}
        ]
        mock_manager.execute_query.return_value = [(1,), (2,)]
        mock_manager.get_table_count.return_value = 2
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/data/tables/test_table?columns=id")
        assert response.status_code == 200
        
        # 验证执行了正确的SQL查询
        mock_manager.execute_query.assert_called_once()
        call_args = mock_manager.execute_query.call_args[0][0]
        assert "SELECT id" in call_args
        assert "FROM test_table" in call_args
    
    @patch('src.api.routers.data_router.DuckDBConnectionManager')
    def test_get_table_data_invalid_column(self, mock_manager_class):
        """测试无效列名"""
        mock_manager = Mock()
        mock_manager.table_exists.return_value = True
        mock_manager.get_table_info.return_value = [
            {"column_name": "id", "column_type": "INTEGER"}
        ]
        mock_manager_class.return_value = mock_manager
        
        response = client.get("/api/data/tables/test_table?columns=invalid_column")
        assert response.status_code == 400
        
        data = response.json()
        assert "不存在" in data["detail"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])