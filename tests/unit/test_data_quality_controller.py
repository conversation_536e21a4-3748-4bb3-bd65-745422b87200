#!/usr/bin/env python3
"""
数据质量控制器测试模块
基于TDD原则的测试驱动开发

测试覆盖：
1. 数据完整性验证
2. 数据准确性检查
3. 数据一致性验证
4. 质量评分系统
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, date
from typing import Dict, List, Any

# 导入被测试的模块
try:
    from src.data_import.processors.data_quality_controller import DataQualityController
    from src.data_import.processors.data_validator import DataValidator
    from src.data_import.processors.quality_metrics import QualityMetrics
except ImportError:
    # 测试时的导入路径
    import sys
    sys.path.append('/Users/<USER>/Documents/AQUA/Dev/AQUA/src')
    from data_import.processors.data_quality_controller import DataQualityController
    from data_import.processors.data_validator import DataValidator
    from data_import.processors.quality_metrics import QualityMetrics


class TestDataQualityController:
    """数据质量控制器测试类"""
    
    @pytest.fixture
    def quality_controller(self):
        """创建质量控制器实例"""
        return DataQualityController()
    
    @pytest.fixture
    def valid_futures_data(self):
        """正常的期货数据"""
        return pd.DataFrame({
            'contract_code': ['AL2501', 'AL2502', 'AL2503'],
            'trade_date': [date(2025, 1, 29), date(2025, 1, 29), date(2025, 1, 29)],
            'open': [19750.0, 19780.0, 19800.0],
            'high': [19850.0, 19880.0, 19900.0],
            'low': [19700.0, 19730.0, 19750.0],
            'close': [19800.0, 19820.0, 19850.0],
            'volume': [123456, 234567, 345678],
            'amount': [2.45e9, 4.67e9, 6.89e9],
            'open_interest': [789012, 890123, 991234]
        })
    
    @pytest.fixture
    def invalid_futures_data(self):
        """包含质量问题的期货数据"""
        return pd.DataFrame({
            'contract_code': ['AL2501', '', 'AL2503'],  # 空值
            'trade_date': [date(2025, 1, 29), date(2025, 1, 29), None],  # 空值
            'open': [19750.0, -100.0, 19800.0],  # 负值
            'high': [19850.0, 19880.0, 19750.0],  # 高价<开盘价
            'low': [19900.0, 19730.0, 19750.0],   # 低价>高价
            'close': [19800.0, 19820.0, 19850.0],
            'volume': [123456, -1000, 345678],     # 负数量
            'amount': [2.45e9, 4.67e9, np.inf],   # 无穷大值
            'open_interest': [789012, 890123, 991234]
        })
    
    @pytest.fixture
    def valid_stock_data(self):
        """正常的股票数据"""
        return pd.DataFrame({
            'symbol': ['000001.SZ', '000002.SZ', '600000.SH'],
            'trade_date': [date(2025, 1, 29), date(2025, 1, 29), date(2025, 1, 29)],
            'open': [12.50, 23.80, 8.90],
            'high': [12.80, 24.20, 9.20],
            'low': [12.30, 23.50, 8.75],
            'close': [12.65, 24.00, 9.05],
            'volume': [1234567, 2345678, 3456789],
            'amount': [15678900.0, 56789012.0, 31234567.0],
            'turnover_rate': [1.23, 2.45, 0.89]
        })
    
    def test_controller_initialization(self, quality_controller):
        """测试质量控制器初始化"""
        assert quality_controller is not None
        assert hasattr(quality_controller, 'validator')
        assert hasattr(quality_controller, 'metrics')
        assert isinstance(quality_controller.validator, DataValidator)
        assert isinstance(quality_controller.metrics, QualityMetrics)
    
    def test_validate_valid_data(self, quality_controller, valid_futures_data):
        """测试正常数据的验证"""
        result = quality_controller.validate(
            data=valid_futures_data,
            data_type='futures',
            frequency='daily'
        )
        
        # 验证结果结构
        assert 'is_valid' in result
        assert 'quality_score' in result
        assert 'issues' in result
        assert 'metrics' in result
        assert 'recommendations' in result
        
        # 验证正常数据的结果
        assert result['is_valid'] is True
        assert result['quality_score'] >= 0.95  # 高质量数据
        assert len(result['issues']) == 0
    
    def test_validate_invalid_data(self, quality_controller, invalid_futures_data):
        """测试包含问题数据的验证"""
        result = quality_controller.validate(
            data=invalid_futures_data,
            data_type='futures',
            frequency='daily'
        )
        
        # 验证问题数据的结果
        assert result['is_valid'] is False
        assert result['quality_score'] < 0.7  # 低质量数据
        assert len(result['issues']) > 0
        
        # 验证具体问题类型
        issue_types = [issue['type'] for issue in result['issues']]
        assert 'missing_value' in issue_types
        assert 'invalid_range' in issue_types 
        assert 'logical_inconsistency' in issue_types
    
    def test_completeness_check(self, quality_controller):
        """测试数据完整性检查"""
        # 完整数据
        complete_data = pd.DataFrame({
            'contract_code': ['AL2501', 'AL2502'],
            'trade_date': [date(2025, 1, 29), date(2025, 1, 29)],
            'open': [19750.0, 19780.0],
            'close': [19800.0, 19820.0]
        })
        
        completeness_result = quality_controller.check_completeness(
            data=complete_data,
            required_fields=['contract_code', 'trade_date', 'open', 'close']
        )
        
        assert completeness_result['completeness_score'] == 1.0
        assert len(completeness_result['missing_fields']) == 0
        
        # 不完整数据
        incomplete_data = pd.DataFrame({
            'contract_code': ['AL2501', None],
            'trade_date': [date(2025, 1, 29), date(2025, 1, 29)],
            'open': [19750.0, 19780.0]
            # 缺少 close 字段
        })
        
        completeness_result = quality_controller.check_completeness(
            data=incomplete_data,
            required_fields=['contract_code', 'trade_date', 'open', 'close']
        )
        
        assert completeness_result['completeness_score'] < 1.0
        assert 'close' in completeness_result['missing_fields']
        assert completeness_result['null_values']['contract_code'] == 1
    
    def test_accuracy_check(self, quality_controller):
        """测试数据准确性检查"""
        test_data = pd.DataFrame({
            'contract_code': ['AL2501', 'INVALID_CODE', 'RB2501'],
            'open': [19750.0, -100.0, 4200.0],  # 负值不合理
            'high': [19850.0, 19880.0, 4250.0],
            'low': [19700.0, 19730.0, 4180.0],
            'close': [19800.0, 19820.0, 4220.0],
            'volume': [123456, -1000, 234567]  # 负数量不合理
        })
        
        accuracy_result = quality_controller.check_accuracy(
            data=test_data,
            data_type='futures'
        )
        
        assert accuracy_result['accuracy_score'] < 1.0
        assert len(accuracy_result['accuracy_issues']) > 0
        
        # 验证具体问题
        issues = accuracy_result['accuracy_issues']
        issue_descriptions = [issue['description'] for issue in issues]
        
        assert any('负值' in desc for desc in issue_descriptions)
        assert any('不合理' in desc for desc in issue_descriptions)
    
    def test_consistency_check(self, quality_controller):
        """测试数据一致性检查"""
        inconsistent_data = pd.DataFrame({
            'contract_code': ['AL2501', 'AL2502'],
            'open': [19750.0, 19780.0],
            'high': [19850.0, 19780.0],  # high < open
            'low': [19900.0, 19730.0],   # low > high
            'close': [19800.0, 19820.0],
            'volume': [123456, 234567],
            'amount': [0, 4.67e9]        # amount = 0 but volume > 0
        })
        
        consistency_result = quality_controller.check_consistency(
            data=inconsistent_data,
            data_type='futures'
        )
        
        assert consistency_result['consistency_score'] < 1.0
        assert len(consistency_result['consistency_issues']) > 0
        
        # 验证逐个一致性检查
        issues = consistency_result['consistency_issues']
        issue_types = [issue['type'] for issue in issues]
        
        assert 'price_logic_error' in issue_types
        assert 'volume_amount_mismatch' in issue_types
    
    def test_quality_score_calculation(self, quality_controller):
        """测试质量评分计算"""
        # 高质量数据
        high_quality_data = pd.DataFrame({
            'contract_code': ['AL2501', 'AL2502'],
            'trade_date': [date(2025, 1, 29), date(2025, 1, 29)],
            'open': [19750.0, 19780.0],
            'high': [19850.0, 19880.0],
            'low': [19700.0, 19730.0],
            'close': [19800.0, 19820.0],
            'volume': [123456, 234567],
            'amount': [2.45e9, 4.67e9],
            'open_interest': [789012, 890123]
        })
        
        quality_score = quality_controller.calculate_quality_score(
            data=high_quality_data,
            data_type='futures'
        )
        
        assert 0.0 <= quality_score <= 1.0
        assert quality_score >= 0.95  # 高质量数据应该得分很高
        
        # 低质量数据
        low_quality_data = pd.DataFrame({
            'contract_code': ['AL2501', None],
            'trade_date': [date(2025, 1, 29), None],
            'open': [19750.0, -100.0],
            'high': [19850.0, 19880.0],
            'low': [19900.0, 19730.0],  # low > high
            'close': [19800.0, 19820.0],
            'volume': [123456, -1000],
            'amount': [2.45e9, np.inf],
            'open_interest': [789012, 890123]
        })
        
        quality_score = quality_controller.calculate_quality_score(
            data=low_quality_data,
            data_type='futures'
        )
        
        assert quality_score < 0.5  # 低质量数据应该得分很低
    
    def test_quality_improvement_recommendations(self, quality_controller, invalid_futures_data):
        """测试质量改进建议"""
        validation_result = quality_controller.validate(
            data=invalid_futures_data,
            data_type='futures',
            frequency='daily'
        )
        
        recommendations = validation_result['recommendations']
        
        assert len(recommendations) > 0
        assert any('missing_value' in rec['type'] for rec in recommendations)
        assert any('invalid_range' in rec['type'] for rec in recommendations)
        assert any('logical_inconsistency' in rec['type'] for rec in recommendations)
        
        # 验证建议内容
        for rec in recommendations:
            assert 'description' in rec
            assert 'action' in rec
            assert 'priority' in rec
            assert rec['priority'] in ['high', 'medium', 'low']
    
    def test_batch_quality_assessment(self, quality_controller, valid_futures_data, invalid_futures_data):
        """测试批量质量评估"""
        batch_data = {
            'batch_1': valid_futures_data,
            'batch_2': invalid_futures_data
        }
        
        batch_results = quality_controller.assess_batch_quality(
            batch_data=batch_data,
            data_type='futures',
            frequency='daily'
        )
        
        assert len(batch_results) == 2
        assert 'batch_1' in batch_results
        assert 'batch_2' in batch_results
        
        # 验证批次结果
        assert batch_results['batch_1']['is_valid'] is True
        assert batch_results['batch_2']['is_valid'] is False
        
        # 验证批次统计
        batch_summary = quality_controller.get_batch_summary(batch_results)
        
        assert 'total_batches' in batch_summary
        assert 'valid_batches' in batch_summary
        assert 'invalid_batches' in batch_summary
        assert 'overall_quality_score' in batch_summary
        
        assert batch_summary['total_batches'] == 2
        assert batch_summary['valid_batches'] == 1
        assert batch_summary['invalid_batches'] == 1
    
    def test_real_time_monitoring(self, quality_controller):
        """测试实时监控功能"""
        # 模拟实时数据流
        streaming_data = [
            pd.DataFrame({
                'contract_code': ['AL2501'],
                'trade_date': [date(2025, 1, 29)],
                'open': [19750.0],
                'high': [19850.0],
                'low': [19700.0],
                'close': [19800.0],
                'volume': [123456],
                'amount': [2.45e9],
                'open_interest': [789012]
            }),
            pd.DataFrame({
                'contract_code': ['AL2502'],
                'trade_date': [date(2025, 1, 29)],
                'open': [-100.0],  # 异常数据
                'high': [19880.0],
                'low': [19730.0],
                'close': [19820.0],
                'volume': [234567],
                'amount': [4.67e9],
                'open_interest': [890123]
            })
        ]
        
        monitoring_results = []
        for data_chunk in streaming_data:
            result = quality_controller.monitor_real_time(
                data=data_chunk,
                data_type='futures',
                frequency='daily'
            )
            monitoring_results.append(result)
        
        # 验证监控结果
        assert len(monitoring_results) == 2
        assert monitoring_results[0]['is_valid'] is True
        assert monitoring_results[1]['is_valid'] is False
        
        # 验证实时报警
        alerts = quality_controller.get_real_time_alerts()
        assert len(alerts) > 0
        assert any(alert['severity'] == 'high' for alert in alerts)
    
    def test_performance_requirements(self, quality_controller):
        """测试性能要求"""
        # 创建大量数据
        large_data = pd.DataFrame({
            'contract_code': ['AL2501'] * 100000,
            'trade_date': [date(2025, 1, 29)] * 100000,
            'open': [19750.0] * 100000,
            'high': [19850.0] * 100000,
            'low': [19700.0] * 100000,
            'close': [19800.0] * 100000,
            'volume': [123456] * 100000,
            'amount': [2.45e9] * 100000,
            'open_interest': [789012] * 100000
        })
        
        import time
        start_time = time.time()
        
        result = quality_controller.validate(
            data=large_data,
            data_type='futures',
            frequency='daily'
        )
        
        processing_time = time.time() - start_time
        
        # 验证处理性能（应该在15秒内完成）
        assert processing_time < 15.0, f"Quality validation took too long: {processing_time}s"
        assert result['is_valid'] is not None
        assert 'quality_score' in result


class TestDataValidator:
    """数据验证器测试类"""
    
    @pytest.fixture
    def data_validator(self):
        """创建数据验证器实例"""
        return DataValidator()
    
    def test_validate_required_fields(self, data_validator):
        """测试必需字段验证"""
        test_data = pd.DataFrame({
            'contract_code': ['AL2501'],
            'open': [19750.0]
            # 缺少 trade_date, close 等必需字段
        })
        
        required_fields = ['contract_code', 'trade_date', 'open', 'close']
        
        validation_result = data_validator.validate_required_fields(
            data=test_data,
            required_fields=required_fields
        )
        
        assert validation_result['is_valid'] is False
        assert 'trade_date' in validation_result['missing_fields']
        assert 'close' in validation_result['missing_fields']
    
    def test_validate_data_types(self, data_validator):
        """测试数据类型验证"""
        test_data = pd.DataFrame({
            'contract_code': ['AL2501', 123],  # 应该是字符串
            'volume': [123456, 'invalid'],     # 应该是数字
            'amount': [2.45e9, 4.67e9]         # 正常数字
        })
        
        type_requirements = {
            'contract_code': 'string',
            'volume': 'integer',
            'amount': 'float'
        }
        
        validation_result = data_validator.validate_data_types(
            data=test_data,
            type_requirements=type_requirements
        )
        
        assert validation_result['is_valid'] is False
        assert len(validation_result['type_errors']) > 0
    
    def test_validate_value_ranges(self, data_validator):
        """测试数值范围验证"""
        test_data = pd.DataFrame({
            'open': [19750.0, -100.0, 50000.0],  # 负值和超大值
            'volume': [123456, -1000, 0],        # 负数量
            'turnover_rate': [1.23, 150.0, -5.0] # 超过100%和负值
        })
        
        range_rules = {
            'open': {'min': 0, 'max': 10000},
            'volume': {'min': 0, 'max': None},
            'turnover_rate': {'min': 0, 'max': 100}
        }
        
        validation_result = data_validator.validate_value_ranges(
            data=test_data,
            range_rules=range_rules
        )
        
        assert validation_result['is_valid'] is False
        assert len(validation_result['range_errors']) > 0


class TestQualityMetrics:
    """质量指标测试类"""
    
    @pytest.fixture
    def quality_metrics(self):
        """创建质量指标实例"""
        return QualityMetrics()
    
    def test_calculate_completeness(self, quality_metrics):
        """测试完整性计算"""
        test_data = pd.DataFrame({
            'col1': [1, 2, None],
            'col2': [1, None, None],
            'col3': [1, 2, 3]
        })
        
        completeness = quality_metrics.calculate_completeness(test_data)
        
        # 验证完整性计算结果
        assert 0.0 <= completeness <= 1.0
        expected_completeness = (2 + 1 + 3) / (3 * 3)  # 6/9 = 0.667
        assert abs(completeness - expected_completeness) < 0.01
    
    def test_calculate_uniqueness(self, quality_metrics):
        """测试唯一性计算"""
        test_data = pd.DataFrame({
            'contract_code': ['AL2501', 'AL2501', 'AL2502']  # 有重复
        })
        
        uniqueness = quality_metrics.calculate_uniqueness(
            data=test_data,
            column='contract_code'
        )
        
        # 验证唯一性计算结果
        assert 0.0 <= uniqueness <= 1.0
        expected_uniqueness = 2 / 3  # 2个唯一值 / 3个总值
        assert abs(uniqueness - expected_uniqueness) < 0.01
    
    def test_calculate_consistency(self, quality_metrics):
        """测试一致性计算"""
        test_data = pd.DataFrame({
            'open': [100.0, 110.0, 120.0],
            'high': [105.0, 105.0, 115.0],  # 第2行不一致：high < open
            'low': [95.0, 95.0, 90.0],
            'close': [102.0, 108.0, 118.0]
        })
        
        consistency = quality_metrics.calculate_consistency(
            data=test_data,
            consistency_rules=['high >= open', 'low <= open', 'high >= close', 'low <= close']
        )
        
        # 验证一致性计算结果
        assert 0.0 <= consistency <= 1.0
        assert consistency < 1.0  # 应该有不一致的数据


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
