import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'

// TDD-Red/Green：Tailwind CSS集成前后样式验证

describe('Tailwind CSS 集成验证', () => {
  it('按钮应应用Tailwind样式', async () => {
    const wrapper = mount({
      template: '<button class="bg-blue-500 text-white p-2">测试按钮</button>'
    })
    const button = wrapper.find('button')
    // 断言：按钮class属性包含Tailwind类
    expect(button.classes()).toContain('bg-blue-500')
    expect(button.classes()).toContain('text-white')
    expect(button.classes()).toContain('p-2')
    // 进一步断言：Tailwind集成后，页面应能渲染出对应样式（如背景色、字体色等）
    // 由于jsdom环境无法真实渲染CSS，建议开发环境下手动验收页面样式
    // 但可断言class已被正确应用
  })
})

// 说明：
// 1. 本测试用于TDD绿阶段，断言Tailwind类已被正确应用。
// 2. 样式渲染建议结合页面手动验收。 