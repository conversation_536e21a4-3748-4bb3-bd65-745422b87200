#!/usr/bin/env python3
"""
TUSHARE数据处理器测试模块
基于TDD原则的测试驱动开发

测试覆盖：
1. TUSHARE数据到V4.0业务表的完整映射
2. 数据验证和质量控制
3. 元数据管理和数据源追溯
"""

import pytest
import pandas as pd
from datetime import datetime, date
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List, Any

# 导入被测试的模块
try:
    from src.data_import.processors.tushare_data_processor import TushareDataProcessor
    from src.data_import.mappers.business_table_mapper import BusinessTableMapper
    from src.data_import.mappers.data_dictionary_mapper import DataDictionaryMapper
except ImportError:
    # 测试时的导入路径
    import sys
    sys.path.append('/Users/<USER>/Documents/AQUA/Dev/AQUA/src')
    from data_import.processors.tushare_data_processor import TushareDataProcessor
    from data_import.mappers.business_table_mapper import BusinessTableMapper
    from data_import.mappers.data_dictionary_mapper import DataDictionaryMapper


class TestTushareDataProcessor:
    """TUSHARE数据处理器测试类"""
    
    @pytest.fixture
    def processor(self):
        """创建测试用的数据处理器实例"""
        return TushareDataProcessor()
    
    @pytest.fixture
    def sample_futures_data(self):
        """模拟期货数据"""
        return pd.DataFrame({
            'ts_code': ['AL2501.SHF', 'AL2502.SHF'],
            'trade_date': ['20250129', '20250129'],
            'open': [19750.0, 19780.0],
            'high': [19850.0, 19880.0],
            'low': [19700.0, 19730.0],
            'close': [19800.0, 19820.0],
            'vol': [123456, 234567],
            'amount': [2.45e9, 4.67e9],
            'oi': [789012, 890123]
        })
    
    @pytest.fixture
    def sample_stock_data(self):
        """模拟股票数据"""
        return pd.DataFrame({
            'ts_code': ['000001.SZ', '000002.SZ'],
            'trade_date': ['20250129', '20250129'],
            'open': [12.50, 23.80],
            'high': [12.80, 24.20],
            'low': [12.30, 23.50],
            'close': [12.65, 24.00],
            'vol': [1234567, 2345678],
            'amount': [15678900, 56789012],
            'turnover_rate': [1.23, 2.45]
        })
    
    def test_processor_initialization(self, processor):
        """测试处理器初始化"""
        assert processor is not None
        assert hasattr(processor, 'business_mapper')
        assert hasattr(processor, 'dictionary_mapper')
        assert isinstance(processor.business_mapper, BusinessTableMapper)
        assert isinstance(processor.dictionary_mapper, DataDictionaryMapper)
    
    def test_process_futures_daily_data(self, processor, sample_futures_data):
        """测试期货日线数据处理"""
        # 测试输入验证
        with pytest.raises(ValueError, match="数据不能为空"):
            processor.process_futures_data(pd.DataFrame(), 'daily')
        
        # 测试正常处理
        result = processor.process_futures_data(sample_futures_data, 'daily')
        
        # 验证返回结果结构
        assert 'mapped_data' in result
        assert 'metadata' in result
        assert 'validation_result' in result
        
        # 验证映射后的字段名
        mapped_df = result['mapped_data']
        expected_columns = ['contract_code', 'trade_date', 'open', 'high', 'low', 'close', 'volume', 'amount', 'open_interest']
        
        for col in expected_columns:
            assert col in mapped_df.columns, f"Missing column: {col}"
        
        # 验证数据类型转换
        assert mapped_df['trade_date'].dtype == 'object'  # date类型
        assert pd.api.types.is_numeric_dtype(mapped_df['open'])
        assert pd.api.types.is_numeric_dtype(mapped_df['volume'])
    
    def test_process_stock_daily_data(self, processor, sample_stock_data):
        """测试股票日线数据处理"""
        result = processor.process_stock_data(sample_stock_data, 'daily')
        
        # 验证返回结果结构
        assert 'mapped_data' in result
        assert 'metadata' in result
        assert 'validation_result' in result
        
        # 验证映射后的字段名
        mapped_df = result['mapped_data']
        expected_columns = ['symbol', 'trade_date', 'open', 'high', 'low', 'close', 'volume', 'amount', 'turnover_rate']
        
        for col in expected_columns:
            assert col in mapped_df.columns, f"Missing column: {col}"
    
    def test_data_validation_and_quality_control(self, processor, sample_futures_data):
        """测试数据验证和质量控制"""
        # 测试正常数据验证
        validation_result = processor.validate_data_quality(sample_futures_data, 'futures', 'daily')
        
        assert 'is_valid' in validation_result
        assert 'quality_score' in validation_result
        assert 'issues' in validation_result
        assert 'recommendations' in validation_result
        
        # 测试缺失数据验证
        incomplete_data = sample_futures_data.copy()
        incomplete_data.loc[0, 'close'] = None
        
        validation_result = processor.validate_data_quality(incomplete_data, 'futures', 'daily')
        assert not validation_result['is_valid']
        assert len(validation_result['issues']) > 0
    
    def test_metadata_management(self, processor, sample_futures_data):
        """测试元数据管理"""
        metadata = processor.generate_metadata(sample_futures_data, 'futures', 'daily', 'tushare')
        
        # 验证元数据结构
        assert 'data_source' in metadata
        assert 'data_type' in metadata
        assert 'frequency' in metadata
        assert 'record_count' in metadata
        assert 'processing_timestamp' in metadata
        assert 'data_range' in metadata
        assert 'quality_metrics' in metadata
        
        # 验证元数据内容
        assert metadata['data_source'] == 'tushare'
        assert metadata['data_type'] == 'futures'
        assert metadata['frequency'] == 'daily'
        assert metadata['record_count'] == len(sample_futures_data)
    
    def test_data_source_traceability(self, processor):
        """测试数据源追溯"""
        # 创建追溯记录
        trace_info = {
            'api_endpoint': 'fut_daily',
            'query_params': {'ts_code': 'AL2501.SHF', 'start_date': '20250101', 'end_date': '20250129'},
            'request_timestamp': datetime.now(),
            'points_consumed': 5,
            'data_version': 'v1.0'
        }
        
        trace_id = processor.create_data_trace(trace_info)
        
        # 验证追溯ID生成
        assert trace_id is not None
        assert isinstance(trace_id, str)
        assert len(trace_id) > 0
        
        # 验证追溯信息检索
        retrieved_info = processor.get_data_trace(trace_id)
        assert retrieved_info is not None
        assert retrieved_info['api_endpoint'] == 'fut_daily'
        assert retrieved_info['points_consumed'] == 5
    
    def test_batch_processing(self, processor, sample_futures_data):
        """测试批量数据处理"""
        # 创建多批次数据
        batch_data = {
            'batch_1': sample_futures_data,
            'batch_2': sample_futures_data.copy()
        }
        
        results = processor.process_batch_data(batch_data, 'futures', 'daily')
        
        # 验证批量处理结果
        assert len(results) == 2
        assert 'batch_1' in results
        assert 'batch_2' in results
        
        for batch_id, result in results.items():
            assert 'mapped_data' in result
            assert 'metadata' in result
            assert 'validation_result' in result
    
    def test_error_handling(self, processor):
        """测试错误处理"""
        # 测试空数据处理
        with pytest.raises(ValueError):
            processor.process_futures_data(pd.DataFrame(), 'daily')
        
        # 测试无效频率处理
        with pytest.raises(ValueError, match="不支持的数据频率"):
            processor.process_futures_data(pd.DataFrame({'test': [1]}), 'invalid_freq')
        
        # 测试无效数据类型处理
        with pytest.raises(ValueError, match="不支持的数据类型"):
            processor.validate_data_quality(pd.DataFrame({'test': [1]}), 'invalid_type', 'daily')
    
    def test_performance_requirements(self, processor):
        """测试性能要求"""
        # 创建大量数据进行性能测试
        large_data = pd.DataFrame({
            'ts_code': ['AL2501.SHF'] * 10000,
            'trade_date': ['20250129'] * 10000,
            'open': [19750.0] * 10000,
            'high': [19850.0] * 10000,
            'low': [19700.0] * 10000,
            'close': [19800.0] * 10000,
            'vol': [123456] * 10000,
            'amount': [2.45e9] * 10000,
            'oi': [789012] * 10000
        })
        
        import time
        start_time = time.time()
        result = processor.process_futures_data(large_data, 'daily')
        processing_time = time.time() - start_time
        
        # 验证处理时间（应该在合理范围内）
        assert processing_time < 5.0, f"Processing took too long: {processing_time}s"
        assert result['mapped_data'] is not None
        assert len(result['mapped_data']) == 10000
    
    def test_cross_platform_compatibility(self, processor):
        """测试跨平台兼容性"""
        # 测试路径处理（Windows/macOS兼容）
        windows_path = Path("C:\\data\\futures\\AL_daily.csv")
        unix_path = Path("/data/futures/AL_daily.csv")
        
        # 验证路径处理不会出错
        assert processor._normalize_path(windows_path) is not None
        assert processor._normalize_path(unix_path) is not None
        
        # 测试时间格式处理
        test_dates = ['20250129', '2025-01-29', '2025/01/29']
        for date_str in test_dates:
            normalized_date = processor._normalize_date(date_str)
            assert normalized_date is not None
            assert isinstance(normalized_date, (date, datetime))


class TestDataMappingEngine:
    """数据映射引擎测试类"""
    
    @pytest.fixture
    def mapping_engine(self):
        """创建映射引擎实例"""
        from src.data_import.processors.data_mapping_engine import DataMappingEngine
        return DataMappingEngine()
    
    def test_tushare_to_v4_field_mapping(self, mapping_engine):
        """测试TUSHARE字段到V4.0业务表的映射"""
        # 期货字段映射测试
        tushare_futures_fields = ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'vol', 'amount', 'oi']
        v4_futures_fields = mapping_engine.map_fields(tushare_futures_fields, 'futures', 'daily')
        
        expected_fields = ['contract_code', 'trade_date', 'open', 'high', 'low', 'close', 'volume', 'amount', 'open_interest']
        assert v4_futures_fields == expected_fields
        
        # 股票字段映射测试
        tushare_stock_fields = ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'vol', 'amount', 'turnover_rate']
        v4_stock_fields = mapping_engine.map_fields(tushare_stock_fields, 'stock', 'daily')
        
        expected_stock_fields = ['symbol', 'trade_date', 'open', 'high', 'low', 'close', 'volume', 'amount', 'turnover_rate']
        assert v4_stock_fields == expected_stock_fields
    
    def test_data_type_conversion(self, mapping_engine):
        """测试数据类型转换"""
        # 测试TUSHARE数据类型到V4.0标准类型的转换
        conversion_rules = mapping_engine.get_type_conversion_rules('futures')
        
        assert 'ts_code' in conversion_rules
        assert conversion_rules['ts_code'] == 'string'
        assert conversion_rules['trade_date'] == 'date'
        assert conversion_rules['vol'] == 'integer'
        assert conversion_rules['amount'] == 'decimal'
    
    def test_validation_rules(self, mapping_engine):
        """测试数据验证规则"""
        validation_rules = mapping_engine.get_validation_rules('futures', 'daily')
        
        # 验证必填字段规则
        assert 'required_fields' in validation_rules
        assert 'contract_code' in validation_rules['required_fields']
        
        # 验证数值范围规则
        assert 'range_rules' in validation_rules
        assert 'volume' in validation_rules['range_rules']
        assert validation_rules['range_rules']['volume']['min'] >= 0


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
