#!/usr/bin/env python3
"""
异常检测引擎测试

测试覆盖：
1. 基于机器学习的异常检测算法
2. 时间序列异常模式识别
3. 异常分类和严重程度评估
4. 复用ConflictResolutionEngine架构
5. 通知和审计功能
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import tempfile
import json

# 假设的导入路径 - 基于架构设计
from src.ai_agent.anomaly_detection_engine import AnomalyDetectionEngine
from src.ai_agent.ai_strategy import AnomalyDetectionStrategy
from src.storage.unified_storage_manager import UnifiedStorageManager
from src.storage.conflict_resolution_engine import NotificationManager, AuditLogger
from src.utils.logger import get_logger


class TestAnomalyDetectionEngine:
    """异常检测引擎测试类"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        return {
            "database": {
                "environments": {
                    "test": "test_anomaly.db"
                },
                "default_environment": "test"
            },
            "anomaly_detection": {
                "confidence_threshold": 0.8,
                "severity_threshold": 0.7,
                "notification_channels": ["email", "dashboard"],
                "enable_ml_detection": True,
                "enable_rule_based": True,
                "time_window_minutes": 60,
                "min_data_points": 10
            },
            "ml_models": {
                "isolation_forest_config": {
                    "contamination": 0.1,
                    "n_estimators": 100
                },
                "lstm_config": {
                    "sequence_length": 50,
                    "hidden_units": 64
                }
            },
            "notification": {
                "severity_threshold": "major",
                "channels": ["email", "sms"]
            }
        }
    
    @pytest.fixture
    def normal_market_data(self):
        """正常市场数据"""
        dates = pd.date_range("20240101", periods=100, freq="D")
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.normal(0, 1, 100) * 0.02)
        volumes = np.random.normal(1000000, 200000, 100)
        
        return pd.DataFrame({
            "trade_date": dates.strftime("%Y%m%d"),
            "ts_code": ["000001.SZ"] * 100,
            "close": prices,
            "vol": volumes,
            "pct_chg": np.random.normal(0, 2, 100)
        })
    
    @pytest.fixture
    def anomalous_market_data(self):
        """包含异常的市场数据"""
        dates = pd.date_range("20240101", periods=100, freq="D")
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.normal(0, 1, 100) * 0.02)
        volumes = np.random.normal(1000000, 200000, 100)
        
        # 注入异常值
        prices[50] = prices[49] * 1.15  # 价格异常暴涨
        volumes[75] = volumes[74] * 5   # 成交量异常暴增
        
        return pd.DataFrame({
            "trade_date": dates.strftime("%Y%m%d"),
            "ts_code": ["000001.SZ"] * 100,
            "close": prices,
            "vol": volumes,
            "pct_chg": np.random.normal(0, 2, 100)
        })
    
    @pytest.fixture
    def anomaly_engine(self, mock_config):
        """创建异常检测引擎实例"""
        with patch('src.storage.unified_storage_manager.UnifiedStorageManager'):
            return AnomalyDetectionEngine(mock_config)
    
    def test_anomaly_engine_initialization(self, mock_config):
        """测试异常检测引擎初始化"""
        with patch('src.storage.unified_storage_manager.UnifiedStorageManager') as mock_storage:
            engine = AnomalyDetectionEngine(mock_config)
            
            # 验证组件初始化
            assert engine.config == mock_config
            assert engine.detection_config == mock_config["anomaly_detection"]
            assert engine.confidence_threshold == 0.8
            assert engine.ml_detector is not None
            assert engine.time_series_analyzer is not None
            assert engine.rule_engine is not None
            
            # 验证复用的组件
            assert hasattr(engine, 'notification_manager')
            assert hasattr(engine, 'audit_logger')
    
    def test_detect_price_anomalies_isolation_forest(self, anomaly_engine, anomalous_market_data):
        """测试基于孤立森林的价格异常检测"""
        # Given: 包含价格异常的数据
        data = anomalous_market_data
        
        # When: 使用孤立森林检测异常
        with patch.object(anomaly_engine.ml_detector, 'fit_isolation_forest') as mock_fit:
            with patch.object(anomaly_engine.ml_detector, 'predict_anomalies') as mock_predict:
                mock_predict.return_value = {
                    "anomaly_scores": np.random.random(100),
                    "anomaly_labels": [1 if i == 50 else 0 for i in range(100)],
                    "confidence": 0.9
                }
                
                result = anomaly_engine.detect_anomalies(
                    data, 
                    method="isolation_forest",
                    features=["close", "vol"]
                )
        
        # Then: 验证检测结果
        assert result["success"] is True
        assert result["detection_method"] == "isolation_forest"
        assert result["anomalies_found"] > 0
        assert result["confidence"] == 0.9
        assert len(result["anomaly_details"]) > 0
    
    def test_detect_time_series_anomalies_lstm(self, anomaly_engine, anomalous_market_data):
        """测试基于LSTM的时间序列异常检测"""
        # Given: 时间序列数据
        data = anomalous_market_data.sort_values("trade_date")
        
        # When: 使用LSTM检测时间序列异常
        with patch.object(anomaly_engine.time_series_analyzer, 'detect_lstm_anomalies') as mock_lstm:
            mock_lstm.return_value = {
                "anomaly_scores": np.random.random(100),
                "anomaly_indices": [50, 75],
                "reconstruction_errors": np.random.random(100),
                "confidence": 0.85
            }
            
            result = anomaly_engine.detect_time_series_anomalies(
                data,
                time_column="trade_date",
                value_column="close",
                sequence_length=10
            )
        
        # Then: 验证时间序列异常检测
        assert result["success"] is True
        assert result["detection_method"] == "lstm"
        assert len(result["anomaly_indices"]) == 2
        assert 50 in result["anomaly_indices"]
        assert 75 in result["anomaly_indices"]
    
    def test_detect_statistical_anomalies(self, anomaly_engine, anomalous_market_data):
        """测试统计方法异常检测"""
        # Given: 市场数据
        data = anomalous_market_data
        
        # When: 使用统计方法检测异常
        result = anomaly_engine.detect_statistical_anomalies(
            data,
            features=["close", "vol", "pct_chg"],
            method="z_score",
            threshold=3.0
        )
        
        # Then: 验证统计异常检测
        assert result["success"] is True
        assert result["detection_method"] == "z_score"
        assert "z_scores" in result
        assert "outlier_indices" in result
        assert result["threshold"] == 3.0
    
    def test_rule_based_anomaly_detection(self, anomaly_engine, anomalous_market_data):
        """测试基于规则的异常检测"""
        # Given: 自定义规则
        rules = [
            {
                "rule_id": "price_jump_rule",
                "description": "检测价格异常跳跃",
                "condition": "abs(pct_chg) > 10",
                "severity": "high"
            },
            {
                "rule_id": "volume_spike_rule", 
                "description": "检测成交量异常",
                "condition": "vol > vol.rolling(5).mean() * 3",
                "severity": "medium"
            }
        ]
        
        # When: 应用规则检测异常
        with patch.object(anomaly_engine.rule_engine, 'apply_rules') as mock_rules:
            mock_rules.return_value = {
                "matched_rules": [
                    {
                        "rule_id": "price_jump_rule",
                        "matched_indices": [50],
                        "severity": "high"
                    }
                ],
                "total_violations": 1
            }
            
            result = anomaly_engine.detect_rule_based_anomalies(
                anomalous_market_data,
                rules=rules
            )
        
        # Then: 验证规则检测结果
        assert result["success"] is True
        assert result["detection_method"] == "rule_based"
        assert len(result["matched_rules"]) == 1
        assert result["matched_rules"][0]["rule_id"] == "price_jump_rule"
    
    def test_calculate_anomaly_severity_score(self, anomaly_engine):
        """测试异常严重程度评分 - 复用ConflictResolutionEngine逻辑"""
        # Given: 异常数据
        anomaly_data = {
            "anomaly_type": "price_jump",
            "severity_indicators": {
                "price_change_percent": 15.0,
                "volume_multiplier": 3.5,
                "market_impact": "high"
            },
            "affected_stocks": 5,
            "duration_minutes": 30
        }
        
        # When: 计算严重程度评分
        score = anomaly_engine._calculate_anomaly_severity_score(anomaly_data)
        
        # Then: 验证评分计算 (复用ConflictResolutionEngine._calculate_severity_score模式)
        assert 0.0 <= score <= 1.0
        assert score > 0.7  # 高严重程度异常
    
    def test_classify_anomalies_by_severity(self, anomaly_engine):
        """测试异常分类 - 复用ConflictResolutionEngine.classify_conflicts"""
        # Given: 多个异常
        anomalies = [
            {"anomaly_id": "a1", "severity_score": 0.9, "type": "price_jump"},
            {"anomaly_id": "a2", "severity_score": 0.6, "type": "volume_spike"},
            {"anomaly_id": "a3", "severity_score": 0.3, "type": "minor_deviation"}
        ]
        
        # When: 分类异常
        classified = anomaly_engine.classify_anomalies(anomalies)
        
        # Then: 验证分类结果 (复用ConflictResolutionEngine分类逻辑)
        assert "critical" in classified
        assert "major" in classified  
        assert "minor" in classified
        assert len(classified["critical"]) == 1  # severity_score >= 0.8
        assert len(classified["major"]) == 1     # 0.5 <= severity_score < 0.8
        assert len(classified["minor"]) == 1     # severity_score < 0.5
    
    def test_anomaly_notification_system(self, anomaly_engine):
        """测试异常通知系统 - 复用NotificationManager"""
        # Given: 检测到的异常
        anomalies = [
            {
                "anomaly_id": "critical_anomaly_1",
                "severity": "critical",
                "type": "price_manipulation",
                "affected_stocks": ["000001.SZ", "000002.SZ"],
                "detected_at": datetime.now()
            }
        ]
        
        # When: 发送异常通知
        with patch.object(anomaly_engine.notification_manager, 'send_notification') as mock_notify:
            anomaly_engine.send_anomaly_notifications(anomalies)
        
        # Then: 验证通知发送
        mock_notify.assert_called_once()
        call_args = mock_notify.call_args[0][0]
        assert call_args["anomaly_count"] == 1
        assert call_args["severity_distribution"]["critical"] == 1
    
    def test_anomaly_audit_logging(self, anomaly_engine):
        """测试异常检测审计日志 - 复用AuditLogger"""
        # Given: 异常检测结果
        detection_result = {
            "method": "isolation_forest",
            "anomalies_found": 3,
            "confidence": 0.9,
            "execution_time": 1.5
        }
        
        # When: 记录审计日志
        with patch.object(anomaly_engine.audit_logger, 'log_detection') as mock_log:
            anomaly_engine.audit_logger.log_detection(
                "anomaly_detection", 
                detection_result
            )
        
        # Then: 验证审计记录
        mock_log.assert_called_once_with("anomaly_detection", detection_result)
    
    def test_batch_anomaly_detection(self, anomaly_engine, anomalous_market_data):
        """测试批量异常检测"""
        # Given: 多个数据批次
        batch_data = [
            anomalous_market_data.iloc[:30],
            anomalous_market_data.iloc[30:60], 
            anomalous_market_data.iloc[60:]
        ]
        
        # When: 批量检测异常
        with patch.object(anomaly_engine, 'detect_anomalies') as mock_detect:
            mock_detect.return_value = {
                "success": True,
                "anomalies_found": 1,
                "confidence": 0.8
            }
            
            result = anomaly_engine.detect_batch_anomalies(
                batch_data,
                method="isolation_forest",
                parallel=True
            )
        
        # Then: 验证批量检测结果
        assert result["success"] is True
        assert result["total_batches"] == 3
        assert result["total_anomalies"] == 3  # 每批1个异常
        assert result["processing_mode"] == "parallel"
    
    def test_real_time_anomaly_monitoring(self, anomaly_engine):
        """测试实时异常监控"""
        # Given: 启用实时监控
        anomaly_engine.enable_real_time_monitoring = True
        
        # When: 处理实时数据流
        streaming_data = pd.DataFrame({
            "trade_date": ["20240115"],
            "ts_code": ["000001.SZ"],
            "close": [999.99],  # 异常价格
            "vol": [10000000],
            "pct_chg": [50.0]   # 异常涨幅
        })
        
        with patch.object(anomaly_engine, '_is_real_time_anomaly') as mock_realtime:
            mock_realtime.return_value = True
            
            result = anomaly_engine.process_real_time_data(streaming_data)
        
        # Then: 验证实时检测
        assert result["real_time_anomaly"] is True
        assert result["immediate_alert"] is True
    
    def test_anomaly_false_positive_handling(self, anomaly_engine):
        """测试异常误报处理"""
        # Given: 误报的异常记录
        false_positive_anomaly = {
            "anomaly_id": "fp_001",
            "type": "price_jump",
            "severity": "high",
            "confidence": 0.7
        }
        
        # When: 标记为误报
        result = anomaly_engine.mark_false_positive(
            false_positive_anomaly["anomaly_id"],
            reason="市场正常波动",
            user_id="analyst_001"
        )
        
        # Then: 验证误报处理
        assert result["success"] is True
        assert result["anomaly_id"] == "fp_001"
        assert result["status"] == "false_positive"
        
        # 验证模型自学习更新
        assert anomaly_engine.false_positive_count > 0
    
    def test_anomaly_pattern_learning(self, anomaly_engine, normal_market_data):
        """测试异常模式学习"""
        # Given: 历史正常数据用于学习
        training_data = normal_market_data
        
        # When: 训练异常检测模型
        with patch.object(anomaly_engine.ml_detector, 'train_model') as mock_train:
            mock_train.return_value = {
                "model_accuracy": 0.95,
                "training_samples": len(training_data),
                "feature_importance": {
                    "close": 0.4,
                    "vol": 0.3,
                    "pct_chg": 0.3
                }
            }
            
            result = anomaly_engine.train_anomaly_model(
                training_data,
                features=["close", "vol", "pct_chg"],
                validation_split=0.2
            )
        
        # Then: 验证模型训练
        assert result["success"] is True
        assert result["model_accuracy"] >= 0.9
        assert "feature_importance" in result
    
    def test_cross_market_anomaly_detection(self, anomaly_engine):
        """测试跨市场异常检测"""
        # Given: 多市场数据
        multi_market_data = pd.DataFrame({
            "trade_date": ["20240115"] * 6,
            "ts_code": ["000001.SZ", "000002.SZ", "600000.SH", "600001.SH", "AAPL.US", "TSLA.US"],
            "close": [10.0, 25.0, 8.0, 15.0, 150.0, 200.0],
            "vol": [1000000, 800000, 1200000, 900000, 2000000, 1500000],
            "market": ["SZ", "SZ", "SH", "SH", "US", "US"]
        })
        
        # When: 检测跨市场异常
        with patch.object(anomaly_engine, 'detect_cross_market_correlation_anomalies') as mock_cross:
            mock_cross.return_value = {
                "success": True,
                "correlation_anomalies": [
                    {
                        "anomaly_type": "decoupled_movement",
                        "markets": ["SZ", "SH"],
                        "correlation_drop": 0.3
                    }
                ]
            }
            
            result = anomaly_engine.detect_cross_market_anomalies(
                multi_market_data,
                group_by="market"
            )
        
        # Then: 验证跨市场异常检测
        assert result["success"] is True
        assert len(result["correlation_anomalies"]) > 0
    
    def test_anomaly_recovery_monitoring(self, anomaly_engine):
        """测试异常恢复监控"""
        # Given: 已识别的异常
        active_anomaly = {
            "anomaly_id": "active_001",
            "type": "volume_spike",
            "detected_at": datetime.now() - timedelta(minutes=30),
            "status": "active"
        }
        
        # When: 监控异常恢复
        current_data = pd.DataFrame({
            "trade_date": ["20240115"],
            "ts_code": ["000001.SZ"],
            "close": [10.0],
            "vol": [1000000],  # 正常成交量
            "pct_chg": [1.0]
        })
        
        result = anomaly_engine.monitor_anomaly_recovery(
            active_anomaly,
            current_data
        )
        
        # Then: 验证恢复监控
        assert result["anomaly_recovered"] is True
        assert result["recovery_time"] > 0
        assert result["anomaly_status"] == "resolved"


class TestAnomalyDetectionStrategy:
    """异常检测策略测试"""
    
    @pytest.fixture
    def anomaly_strategy(self):
        """创建异常检测策略实例"""
        return AnomalyDetectionStrategy()
    
    def test_anomaly_strategy_execution(self, anomaly_strategy):
        """测试异常检测策略执行"""
        # Given: 数据和上下文
        data = pd.DataFrame({
            "close": [10.0, 10.1, 15.0],  # 包含异常值
            "vol": [1000000, 1000000, 5000000]
        })
        context = {
            "detection_method": "isolation_forest",
            "confidence_threshold": 0.8
        }
        
        # When: 执行策略
        with patch.object(anomaly_strategy, '_detect_anomalies') as mock_detect:
            mock_detect.return_value = {
                "success": True,
                "anomalies_found": 1,
                "anomaly_scores": [0.1, 0.1, 0.9]
            }
            
            result = anomaly_strategy.execute(data, context)
        
        # Then: 验证策略执行结果
        assert result["success"] is True
        assert result["strategy_type"] == "anomaly_detection"
        assert result["anomalies_found"] == 1
    
    def test_anomaly_strategy_config_validation(self, anomaly_strategy):
        """测试异常检测策略配置验证"""
        # Given: 有效配置
        valid_config = {
            "confidence_threshold": 0.8,
            "detection_methods": ["isolation_forest", "lstm"],
            "notification_channels": ["email"]
        }
        
        # When: 验证配置
        result = anomaly_strategy.validate_config(valid_config)
        
        # Then: 验证通过
        assert result is True
        
        # Given: 无效配置
        invalid_config = {
            "confidence_threshold": 1.5  # 无效阈值
        }
        
        # When: 验证无效配置
        result = anomaly_strategy.validate_config(invalid_config)
        
        # Then: 验证失败
        assert result is False


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])