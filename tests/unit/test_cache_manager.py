#!/usr/bin/env python3
"""
缓存管理器单元测试
"""

import pytest
import tempfile
import shutil
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from src.cache.cache_manager import (
    LRUCache,
    DiskCache,
    MultiLevelCacheManager,
    CacheItem,
    CacheStats,
    CacheLevel,
    CacheStrategy,
    cache_result,
    get_cache_manager,
    warm_up_cache
)
from src.utils.exceptions import CacheException


class TestCacheItem:
    """缓存项测试类"""
    
    def test_cache_item_creation(self):
        """测试缓存项创建"""
        now = datetime.now()
        item = CacheItem(
            key="test_key",
            value="test_value",
            created_at=now,
            last_accessed=now,
            access_count=1,
            ttl=3600
        )
        
        assert item.key == "test_key"
        assert item.value == "test_value"
        assert item.created_at == now
        assert item.last_accessed == now
        assert item.access_count == 1
        assert item.ttl == 3600
    
    def test_is_expired_with_ttl(self):
        """测试TTL过期检查"""
        past_time = datetime.now() - timedelta(seconds=3700)  # 超过1小时
        item = CacheItem(
            key="test_key",
            value="test_value",
            created_at=past_time,
            last_accessed=past_time,
            access_count=1,
            ttl=3600  # 1小时
        )
        
        assert item.is_expired is True
    
    def test_is_expired_without_ttl(self):
        """测试无TTL时的过期检查"""
        past_time = datetime.now() - timedelta(seconds=3700)
        item = CacheItem(
            key="test_key",
            value="test_value",
            created_at=past_time,
            last_accessed=past_time,
            access_count=1,
            ttl=None
        )
        
        assert item.is_expired is False
    
    def test_age_calculation(self):
        """测试缓存项年龄计算"""
        past_time = datetime.now() - timedelta(seconds=100)
        item = CacheItem(
            key="test_key",
            value="test_value",
            created_at=past_time,
            last_accessed=past_time,
            access_count=1
        )
        
        assert item.age >= 100  # 至少100秒


class TestCacheStats:
    """缓存统计测试类"""
    
    def test_cache_stats_creation(self):
        """测试缓存统计创建"""
        stats = CacheStats(
            hits=100,
            misses=20,
            evictions=5,
            size=150,
            memory_usage=1024
        )
        
        assert stats.hits == 100
        assert stats.misses == 20
        assert stats.evictions == 5
        assert stats.size == 150
        assert stats.memory_usage == 1024
    
    def test_hit_rate_calculation(self):
        """测试命中率计算"""
        stats = CacheStats(hits=80, misses=20)
        assert stats.hit_rate == 0.8
        
        # 测试空统计
        empty_stats = CacheStats()
        assert empty_stats.hit_rate == 0.0


class TestLRUCache:
    """LRU缓存测试类"""
    
    @pytest.fixture
    def lru_cache(self):
        """创建LRU缓存实例"""
        return LRUCache(max_size=3)
    
    def test_lru_cache_creation(self, lru_cache):
        """测试LRU缓存创建"""
        assert lru_cache.max_size == 3
        assert len(lru_cache.cache) == 0
        assert lru_cache.stats.size == 0
    
    def test_put_and_get(self, lru_cache):
        """测试缓存存取操作"""
        lru_cache.put("key1", "value1")
        assert lru_cache.get("key1") == "value1"
        assert lru_cache.stats.hits == 1
        assert lru_cache.stats.misses == 0
    
    def test_get_miss(self, lru_cache):
        """测试缓存未命中"""
        result = lru_cache.get("nonexistent")
        assert result is None
        assert lru_cache.stats.misses == 1
    
    def test_lru_eviction(self, lru_cache):
        """测试LRU淘汰机制"""
        # 添加3个项目（达到最大容量）
        lru_cache.put("key1", "value1")
        lru_cache.put("key2", "value2")
        lru_cache.put("key3", "value3")
        assert len(lru_cache.cache) == 3
        
        # 添加第4个项目，应该淘汰最旧的
        lru_cache.put("key4", "value4")
        assert len(lru_cache.cache) == 3
        assert lru_cache.get("key1") is None  # 最旧的被淘汰
        assert lru_cache.get("key4") == "value4"
        assert lru_cache.stats.evictions == 1
    
    def test_lru_access_order(self, lru_cache):
        """测试LRU访问顺序"""
        lru_cache.put("key1", "value1")
        lru_cache.put("key2", "value2")
        lru_cache.put("key3", "value3")
        
        # 访问key1，使其变为最近使用
        lru_cache.get("key1")
        
        # 添加新项目，应该淘汰key2（最旧的）
        lru_cache.put("key4", "value4")
        assert lru_cache.get("key1") == "value1"  # 仍然存在
        assert lru_cache.get("key2") is None  # 被淘汰
        assert lru_cache.get("key4") == "value4"  # 新项目
    
    def test_ttl_expiration(self, lru_cache):
        """测试TTL过期"""
        # 使用很短的TTL
        lru_cache.put("key1", "value1", ttl=0.1)
        
        # 立即获取应该成功
        assert lru_cache.get("key1") == "value1"
        
        # 等待过期
        time.sleep(0.2)
        
        # 再次获取应该返回None
        assert lru_cache.get("key1") is None
        assert lru_cache.stats.evictions == 1
    
    def test_update_existing_key(self, lru_cache):
        """测试更新已存在的键"""
        lru_cache.put("key1", "value1")
        assert lru_cache.get("key1") == "value1"
        
        # 更新同一个键
        lru_cache.put("key1", "new_value")
        assert lru_cache.get("key1") == "new_value"
        assert len(lru_cache.cache) == 1
    
    def test_delete(self, lru_cache):
        """测试删除操作"""
        lru_cache.put("key1", "value1")
        assert lru_cache.get("key1") == "value1"
        
        # 删除存在的键
        assert lru_cache.delete("key1") is True
        assert lru_cache.get("key1") is None
        
        # 删除不存在的键
        assert lru_cache.delete("nonexistent") is False
    
    def test_clear(self, lru_cache):
        """测试清空操作"""
        lru_cache.put("key1", "value1")
        lru_cache.put("key2", "value2")
        assert len(lru_cache.cache) == 2
        
        lru_cache.clear()
        assert len(lru_cache.cache) == 0
        assert lru_cache.stats.size == 0
    
    def test_cleanup_expired(self, lru_cache):
        """测试清理过期项"""
        # 添加一些项目，其中一些会过期
        lru_cache.put("key1", "value1", ttl=0.1)
        lru_cache.put("key2", "value2", ttl=3600)
        
        # 等待第一个过期
        time.sleep(0.2)
        
        # 清理过期项
        expired_count = lru_cache.cleanup_expired()
        assert expired_count == 1
        assert lru_cache.get("key1") is None
        assert lru_cache.get("key2") == "value2"


class TestDiskCache:
    """磁盘缓存测试类"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def disk_cache(self, temp_dir):
        """创建磁盘缓存实例"""
        return DiskCache(cache_dir=temp_dir, max_size_mb=1)
    
    def test_disk_cache_creation(self, disk_cache, temp_dir):
        """测试磁盘缓存创建"""
        assert disk_cache.cache_dir == Path(temp_dir)
        assert disk_cache.max_size_mb == 1
        assert disk_cache.cache_dir.exists()
    
    def test_put_and_get(self, disk_cache):
        """测试磁盘缓存存取"""
        disk_cache.put("key1", "value1")
        assert disk_cache.get("key1") == "value1"
        assert disk_cache.stats.hits == 1
    
    def test_get_miss(self, disk_cache):
        """测试磁盘缓存未命中"""
        result = disk_cache.get("nonexistent")
        assert result is None
        assert disk_cache.stats.misses == 1
    
    def test_ttl_expiration(self, disk_cache):
        """测试磁盘缓存TTL过期"""
        disk_cache.put("key1", "value1", ttl=0.1)
        
        # 立即获取应该成功
        assert disk_cache.get("key1") == "value1"
        
        # 等待过期
        time.sleep(0.2)
        
        # 再次获取应该返回None
        assert disk_cache.get("key1") is None
    
    def test_delete(self, disk_cache):
        """测试磁盘缓存删除"""
        disk_cache.put("key1", "value1")
        assert disk_cache.get("key1") == "value1"
        
        # 删除存在的键
        assert disk_cache.delete("key1") is True
        assert disk_cache.get("key1") is None
        
        # 删除不存在的键
        assert disk_cache.delete("nonexistent") is False
    
    def test_clear(self, disk_cache):
        """测试磁盘缓存清空"""
        disk_cache.put("key1", "value1")
        disk_cache.put("key2", "value2")
        
        disk_cache.clear()
        assert disk_cache.get("key1") is None
        assert disk_cache.get("key2") is None
        assert len(disk_cache.index) == 0
    
    def test_complex_data_types(self, disk_cache):
        """测试复杂数据类型的缓存"""
        complex_data = {
            "list": [1, 2, 3],
            "dict": {"nested": "value"},
            "tuple": (1, 2, 3)
        }
        
        disk_cache.put("complex", complex_data)
        retrieved = disk_cache.get("complex")
        
        assert retrieved == complex_data
        assert isinstance(retrieved["list"], list)
        assert isinstance(retrieved["dict"], dict)


class TestMultiLevelCacheManager:
    """多级缓存管理器测试类"""
    
    @pytest.fixture
    def temp_config(self):
        """创建临时配置"""
        temp_dir = tempfile.mkdtemp()
        config_path = Path(temp_dir) / "settings.toml"
        
        config_content = f"""
[dev]
[dev.cache]
enable_l1 = true
enable_l2 = true
l1_max_size = 100
l2_cache_dir = "{temp_dir}/cache"
l2_max_size_mb = 1
default_ttl = 3600
"""
        
        with open(config_path, "w") as f:
            f.write(config_content)
        
        yield str(config_path)
        
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def cache_manager(self, temp_config):
        """创建缓存管理器实例"""
        return MultiLevelCacheManager(temp_config, "dev")
    
    def test_cache_manager_creation(self, cache_manager):
        """测试缓存管理器创建"""
        assert cache_manager.enable_l1 is True
        assert cache_manager.enable_l2 is True
        assert cache_manager.default_ttl == 3600
        assert cache_manager.l1_cache is not None
        assert cache_manager.l2_cache is not None
    
    def test_put_and_get_l1(self, cache_manager):
        """测试L1缓存存取"""
        cache_manager.put("key1", "value1", level=CacheLevel.L1_MEMORY)
        assert cache_manager.get("key1") == "value1"
    
    def test_put_and_get_l2(self, cache_manager):
        """测试L2缓存存取"""
        cache_manager.put("key1", "value1", level=CacheLevel.L2_DISK)
        assert cache_manager.get("key1") == "value1"
    
    def test_multi_level_fallback(self, cache_manager):
        """测试多级缓存回退"""
        # 只存储到L2
        cache_manager.put("key1", "value1", level=CacheLevel.L2_DISK)
        
        # 清空L1缓存
        cache_manager.l1_cache.clear()
        
        # 获取时应该从L2获取并回写到L1
        assert cache_manager.get("key1") == "value1"
        
        # 验证已经回写到L1
        assert cache_manager.l1_cache.get("key1") == "value1"
    
    def test_delete_from_both_levels(self, cache_manager):
        """测试从所有级别删除"""
        cache_manager.put("key1", "value1")  # 存储到两个级别
        
        # 删除
        assert cache_manager.delete("key1") is True
        
        # 验证两个级别都删除了
        assert cache_manager.l1_cache.get("key1") is None
        assert cache_manager.l2_cache.get("key1") is None
    
    def test_clear_all_levels(self, cache_manager):
        """测试清空所有级别"""
        cache_manager.put("key1", "value1")
        cache_manager.put("key2", "value2")
        
        cache_manager.clear()
        
        assert cache_manager.get("key1") is None
        assert cache_manager.get("key2") is None
    
    def test_get_stats(self, cache_manager):
        """测试获取统计信息"""
        cache_manager.put("key1", "value1")
        cache_manager.get("key1")
        
        stats = cache_manager.get_stats()
        
        assert "l1_memory" in stats
        assert "l2_disk" in stats
        assert isinstance(stats["l1_memory"], CacheStats)
        assert isinstance(stats["l2_disk"], CacheStats)
    
    @patch('threading.Thread')
    def test_cleanup_thread_start(self, mock_thread, temp_config):
        """测试清理线程启动"""
        cache_manager = MultiLevelCacheManager(temp_config, "dev")
        
        # 验证线程被创建和启动
        mock_thread.assert_called_once()
        mock_thread.return_value.start.assert_called_once()


class TestCacheDecorator:
    """缓存装饰器测试类"""
    
    def test_cache_result_decorator(self):
        """测试缓存结果装饰器"""
        call_count = 0
        
        @cache_result(ttl=3600)
        def test_function(x, y):
            nonlocal call_count
            call_count += 1
            return x + y
        
        # 第一次调用
        result1 = test_function(1, 2)
        assert result1 == 3
        assert call_count == 1
        
        # 第二次调用相同参数，应该从缓存返回
        result2 = test_function(1, 2)
        assert result2 == 3
        assert call_count == 1  # 函数没有再次执行
        
        # 不同参数，应该重新执行
        result3 = test_function(2, 3)
        assert result3 == 5
        assert call_count == 2
    
    def test_cache_result_with_kwargs(self):
        """测试带关键字参数的缓存装饰器"""
        call_count = 0
        
        @cache_result(ttl=3600)
        def test_function(x, y=10):
            nonlocal call_count
            call_count += 1
            return x + y
        
        # 测试不同的关键字参数调用
        result1 = test_function(1, y=2)
        result2 = test_function(1, y=2)
        result3 = test_function(1, y=3)
        
        assert result1 == 3
        assert result2 == 3
        assert result3 == 4
        assert call_count == 2  # 只有两次不同的调用


class TestCacheUtilities:
    """缓存工具函数测试类"""
    
    def test_get_cache_manager_singleton(self):
        """测试缓存管理器单例"""
        manager1 = get_cache_manager()
        manager2 = get_cache_manager()
        
        assert manager1 is manager2
    
    def test_warm_up_cache(self):
        """测试缓存预热"""
        cache_manager = get_cache_manager()
        
        warm_up_data = {
            "key1": "value1",
            "key2": "value2",
            "key3": {"nested": "value"}
        }
        
        warm_up_cache(cache_manager, warm_up_data)
        
        # 验证数据已被缓存
        assert cache_manager.get("key1") == "value1"
        assert cache_manager.get("key2") == "value2"
        assert cache_manager.get("key3") == {"nested": "value"}


@pytest.mark.parametrize("cache_level", [
    CacheLevel.L1_MEMORY,
    CacheLevel.L2_DISK
])
def test_cache_levels(cache_level):
    """测试不同缓存级别"""
    assert isinstance(cache_level, CacheLevel)
    assert cache_level.value in ["l1_memory", "l2_disk", "l3_distributed"]


@pytest.mark.parametrize("cache_strategy", [
    CacheStrategy.LRU,
    CacheStrategy.TTL,
    CacheStrategy.LFU
])
def test_cache_strategies(cache_strategy):
    """测试不同缓存策略"""
    assert isinstance(cache_strategy, CacheStrategy)
    assert cache_strategy.value in ["lru", "ttl", "lfu"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])