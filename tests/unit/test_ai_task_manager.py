#!/usr/bin/env python3
"""
AI任务管理器测试

测试覆盖：
1. AI专用任务类型处理和调度
2. 模型训练任务管理和监控
3. 推理队列和批处理优化
4. 扩展PriorityQueueManager功能
5. AI任务性能监控和资源管理
"""

import pytest
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import threading
import time
import uuid

# 假设的导入路径 - 基于架构设计
from src.ai_agent.ai_task_manager import AITaskManager, AITaskType, ModelTrainingTask, InferenceTask
from src.storage.priority_queue_manager import PriorityQueueManager, TaskPriority, TaskStatus
from src.utils.logger import get_logger


class TestAITaskManager:
    """AI任务管理器测试类"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        return {
            "ai_task_management": {
                "max_concurrent_training": 2,
                "max_concurrent_inference": 5,
                "model_update_interval_hours": 24,
                "inference_timeout_seconds": 300,
                "training_timeout_hours": 48,
                "enable_gpu_acceleration": True,
                "resource_allocation": {
                    "training": {"cpu_cores": 8, "memory_gb": 16, "gpu_count": 1},
                    "inference": {"cpu_cores": 4, "memory_gb": 8, "gpu_count": 0}
                }
            },
            "priority_queue": {
                "max_queue_size": 2000,
                "worker_threads": 6,
                "enable_monitoring": True
            },
            "model_registry": {
                "storage_path": "models/",
                "versioning_enabled": True,
                "auto_cleanup": True,
                "max_model_versions": 5
            }
        }
    
    @pytest.fixture
    def ai_task_manager(self, mock_config):
        """创建AI任务管理器实例"""
        return AITaskManager(mock_config)
    
    def test_ai_task_manager_initialization(self, mock_config):
        """测试AI任务管理器初始化"""
        # When: 创建AI任务管理器
        ai_manager = AITaskManager(mock_config)
        
        # Then: 验证初始化
        assert ai_manager.config == mock_config
        assert ai_manager.ai_config == mock_config["ai_task_management"]
        assert ai_manager.max_concurrent_training == 2
        assert ai_manager.max_concurrent_inference == 5
        
        # 验证AI任务处理器注册
        assert "nlp_query" in ai_manager._task_handlers
        assert "anomaly_detection" in ai_manager._task_handlers
        assert "report_generation" in ai_manager._task_handlers
        assert "strategy_optimization" in ai_manager._task_handlers
        assert "model_training" in ai_manager._task_handlers
        assert "model_inference" in ai_manager._task_handlers
    
    def test_submit_nlp_query_task(self, ai_task_manager):
        """测试提交NLP查询任务"""
        # Given: NLP查询任务配置
        query_config = {
            "query_text": "查询平安银行最近一周的股价数据",
            "user_id": "analyst_001",
            "output_format": "table",
            "enable_cache": True
        }
        
        # When: 提交NLP查询任务
        task_id = ai_task_manager.submit_nlp_query_task(
            query_config,
            priority=TaskPriority.HIGH,
            timeout_seconds=120
        )
        
        # Then: 验证任务提交
        assert task_id is not None
        task_status = ai_task_manager.get_task_status(task_id)
        assert task_status["task_type"] == "nlp_query"
        assert task_status["priority"] == "HIGH"
        assert task_status["status"] == "PENDING"
    
    def test_submit_anomaly_detection_task(self, ai_task_manager):
        """测试提交异常检测任务"""
        # Given: 异常检测任务配置
        detection_config = {
            "data_source": "stock_daily",
            "detection_method": "isolation_forest",
            "symbols": ["000001.SZ", "000002.SZ"],
            "time_window": "1D",
            "confidence_threshold": 0.8
        }
        
        # When: 提交异常检测任务
        task_id = ai_task_manager.submit_anomaly_detection_task(
            detection_config,
            priority=TaskPriority.MEDIUM
        )
        
        # Then: 验证任务提交
        assert task_id is not None
        task_status = ai_task_manager.get_task_status(task_id)
        assert task_status["task_type"] == "anomaly_detection"
        assert task_status["priority"] == "MEDIUM"
    
    def test_submit_model_training_task(self, ai_task_manager):
        """测试提交模型训练任务"""
        # Given: 模型训练配置
        training_config = {
            "model_type": "lstm_anomaly_detector",
            "training_data_query": "SELECT * FROM stock_daily WHERE trade_date >= '20230101'",
            "hyperparameters": {
                "sequence_length": 50,
                "hidden_units": 64,
                "learning_rate": 0.001,
                "epochs": 100,
                "batch_size": 32
            },
            "validation_split": 0.2,
            "early_stopping": True
        }
        
        # When: 提交模型训练任务
        task_id = ai_task_manager.submit_model_training_task(
            training_config,
            priority=TaskPriority.HIGH,
            estimated_duration_hours=12
        )
        
        # Then: 验证训练任务提交
        assert task_id is not None
        task_status = ai_task_manager.get_task_status(task_id)
        assert task_status["task_type"] == "model_training"
        assert task_status["priority"] == "HIGH"
        
        # 验证资源分配
        assert "resource_allocation" in task_status
        assert task_status["resource_allocation"]["cpu_cores"] == 8
    
    def test_submit_batch_inference_task(self, ai_task_manager):
        """测试提交批量推理任务"""
        # Given: 批量推理配置
        inference_config = {
            "model_id": "anomaly_detector_v1.2",
            "input_data_query": "SELECT * FROM stock_daily WHERE trade_date = '20240115'",
            "batch_size": 1000,
            "output_table": "anomaly_scores",
            "confidence_threshold": 0.8
        }
        
        # When: 提交批量推理任务
        task_id = ai_task_manager.submit_inference_task(
            inference_config,
            priority=TaskPriority.MEDIUM,
            timeout_seconds=600
        )
        
        # Then: 验证推理任务提交
        assert task_id is not None
        task_status = ai_task_manager.get_task_status(task_id)
        assert task_status["task_type"] == "model_inference"
        assert task_status["timeout_seconds"] == 600
    
    def test_handle_nlp_query_task_execution(self, ai_task_manager):
        """测试NLP查询任务执行"""
        # Given: NLP查询任务负载
        task_payload = {
            "query_text": "显示科技股今日涨幅排行",
            "user_id": "trader_001",
            "session_id": "session_123"
        }
        
        # When: 执行NLP查询任务
        with patch('src.ai_agent.nlp_query_processor.NLPQueryProcessor') as mock_processor:
            mock_instance = Mock()
            mock_instance.execute_nlp_query.return_value = {
                "success": True,
                "data": pd.DataFrame({"symbol": ["000001.SZ"], "pct_chg": [5.2]}),
                "query_confidence": 0.9
            }
            mock_processor.return_value = mock_instance
            
            result = ai_task_manager._handle_nlp_query(task_payload)
        
        # Then: 验证执行结果
        assert result["success"] is True
        assert result["task_type"] == "nlp_query"
        assert result["query_confidence"] == 0.9
        assert not result["data"].empty
    
    def test_handle_anomaly_detection_task_execution(self, ai_task_manager):
        """测试异常检测任务执行"""
        # Given: 异常检测任务负载
        task_payload = {
            "data_source": "stock_daily",
            "symbols": ["000001.SZ", "000002.SZ"],
            "detection_method": "isolation_forest"
        }
        
        # When: 执行异常检测任务
        with patch('src.ai_agent.anomaly_detection_engine.AnomalyDetectionEngine') as mock_engine:
            mock_instance = Mock()
            mock_instance.detect_anomalies.return_value = {
                "success": True,
                "anomalies_found": 2,
                "anomaly_details": [
                    {"symbol": "000001.SZ", "anomaly_score": 0.9, "type": "price_jump"}
                ]
            }
            mock_engine.return_value = mock_instance
            
            result = ai_task_manager._handle_anomaly_detection(task_payload)
        
        # Then: 验证执行结果
        assert result["success"] is True
        assert result["task_type"] == "anomaly_detection"
        assert result["anomalies_found"] == 2
        assert len(result["anomaly_details"]) == 1
    
    def test_handle_model_training_task_execution(self, ai_task_manager):
        """测试模型训练任务执行"""
        # Given: 模型训练任务负载
        task_payload = {
            "model_type": "lstm_anomaly_detector",
            "hyperparameters": {
                "sequence_length": 50,
                "hidden_units": 64,
                "epochs": 10  # 测试用小数量
            },
            "training_data_query": "SELECT * FROM test_data"
        }
        
        # When: 执行模型训练任务
        with patch('src.ai_agent.model_trainer.ModelTrainer') as mock_trainer:
            mock_instance = Mock()
            mock_instance.train_model.return_value = {
                "success": True,
                "model_id": "lstm_v1_001",
                "training_accuracy": 0.95,
                "validation_accuracy": 0.92,
                "training_time_seconds": 300,
                "model_size_mb": 15.2
            }
            mock_trainer.return_value = mock_instance
            
            result = ai_task_manager._handle_model_training(task_payload)
        
        # Then: 验证训练结果
        assert result["success"] is True
        assert result["task_type"] == "model_training"
        assert result["model_id"] == "lstm_v1_001"
        assert result["training_accuracy"] == 0.95
    
    def test_handle_model_inference_task_execution(self, ai_task_manager):
        """测试模型推理任务执行"""
        # Given: 模型推理任务负载
        task_payload = {
            "model_id": "anomaly_detector_v1.2",
            "input_data_query": "SELECT * FROM stock_daily LIMIT 100",
            "batch_size": 50
        }
        
        # When: 执行模型推理任务
        with patch('src.ai_agent.model_inference.ModelInference') as mock_inference:
            mock_instance = Mock()
            mock_instance.run_inference.return_value = {
                "success": True,
                "predictions": [0.1, 0.8, 0.2, 0.9],  # 异常分数
                "processed_records": 100,
                "inference_time_seconds": 5.2
            }
            mock_inference.return_value = mock_instance
            
            result = ai_task_manager._handle_model_inference(task_payload)
        
        # Then: 验证推理结果
        assert result["success"] is True
        assert result["task_type"] == "model_inference"
        assert result["processed_records"] == 100
        assert len(result["predictions"]) == 4
    
    def test_concurrent_training_task_limit(self, ai_task_manager):
        """测试并发训练任务限制"""
        # Given: 多个训练任务
        training_configs = [
            {"model_type": "lstm_1", "epochs": 50},
            {"model_type": "lstm_2", "epochs": 50},
            {"model_type": "lstm_3", "epochs": 50}  # 超过限制
        ]
        
        # When: 提交超过限制的训练任务
        submitted_tasks = []
        for config in training_configs:
            try:
                task_id = ai_task_manager.submit_model_training_task(
                    config, 
                    priority=TaskPriority.MEDIUM
                )
                submitted_tasks.append(task_id)
            except Exception as e:
                # 预期第三个任务会被拒绝或排队
                pass
        
        # Then: 验证并发限制
        active_training_tasks = ai_task_manager.get_active_training_tasks()
        assert len(active_training_tasks) <= ai_task_manager.max_concurrent_training
    
    def test_ai_task_resource_allocation(self, ai_task_manager):
        """测试AI任务资源分配"""
        # Given: 不同类型的AI任务
        training_task_id = ai_task_manager.submit_model_training_task(
            {"model_type": "lstm", "epochs": 100},
            priority=TaskPriority.HIGH
        )
        
        inference_task_id = ai_task_manager.submit_inference_task(
            {"model_id": "test_model", "batch_size": 1000},
            priority=TaskPriority.MEDIUM
        )
        
        # When: 获取资源分配信息
        training_resources = ai_task_manager.get_task_resources(training_task_id)
        inference_resources = ai_task_manager.get_task_resources(inference_task_id)
        
        # Then: 验证资源分配
        assert training_resources["cpu_cores"] == 8
        assert training_resources["memory_gb"] == 16
        assert training_resources["gpu_count"] == 1
        
        assert inference_resources["cpu_cores"] == 4
        assert inference_resources["memory_gb"] == 8
        assert inference_resources["gpu_count"] == 0
    
    def test_ai_task_priority_scheduling(self, ai_task_manager):
        """测试AI任务优先级调度"""
        # Given: 不同优先级的AI任务
        low_priority_task = ai_task_manager.submit_nlp_query_task(
            {"query_text": "低优先级查询"}, 
            priority=TaskPriority.LOW
        )
        
        high_priority_task = ai_task_manager.submit_anomaly_detection_task(
            {"data_source": "urgent_data"},
            priority=TaskPriority.CRITICAL
        )
        
        # When: 检查任务队列顺序
        pending_tasks = ai_task_manager.get_pending_tasks(limit=5)
        
        # Then: 验证高优先级任务排在前面
        task_priorities = [task["priority"] for task in pending_tasks]
        critical_index = next(i for i, p in enumerate(task_priorities) if p == "CRITICAL")
        low_index = next((i for i, p in enumerate(task_priorities) if p == "LOW"), len(task_priorities))
        
        assert critical_index < low_index
    
    def test_ai_task_monitoring_and_metrics(self, ai_task_manager):
        """测试AI任务监控和指标"""
        # Given: 启用监控
        ai_task_manager.enable_ai_monitoring(
            metrics=["task_throughput", "model_accuracy", "resource_utilization"]
        )
        
        # When: 执行多个AI任务并收集指标
        for i in range(5):
            ai_task_manager.submit_nlp_query_task(
                {"query_text": f"测试查询{i}"},
                priority=TaskPriority.MEDIUM
            )
        
        # 模拟任务完成
        with patch.object(ai_task_manager, '_complete_tasks_simulation'):
            time.sleep(0.1)  # 模拟任务执行时间
        
        # Then: 验证监控指标
        metrics = ai_task_manager.get_ai_monitoring_metrics()
        assert "task_throughput" in metrics
        assert "average_task_duration" in metrics
        assert "task_type_distribution" in metrics
        assert metrics["total_ai_tasks"] >= 5
    
    def test_model_lifecycle_management(self, ai_task_manager):
        """测试模型生命周期管理"""
        # Given: 模型训练完成
        model_info = {
            "model_id": "lstm_anomaly_v2.0",
            "model_type": "lstm_anomaly_detector",
            "training_accuracy": 0.96,
            "validation_accuracy": 0.94,
            "model_size_mb": 25.5,
            "training_date": datetime.now()
        }
        
        # When: 注册新模型
        registration_result = ai_task_manager.register_trained_model(model_info)
        
        # Then: 验证模型注册
        assert registration_result["success"] is True
        assert registration_result["model_id"] == "lstm_anomaly_v2.0"
        
        # When: 获取可用模型
        available_models = ai_task_manager.get_available_models(model_type="lstm_anomaly_detector")
        
        # Then: 验证模型可用性
        assert len(available_models) > 0
        assert any(model["model_id"] == "lstm_anomaly_v2.0" for model in available_models)
    
    def test_ai_task_error_handling_and_retry(self, ai_task_manager):
        """测试AI任务错误处理和重试"""
        # Given: 会失败的任务配置
        failing_config = {
            "query_text": "故意失败的查询",
            "force_failure": True
        }
        
        # When: 提交会失败的任务
        task_id = ai_task_manager.submit_nlp_query_task(
            failing_config,
            priority=TaskPriority.MEDIUM,
            max_retries=2
        )
        
        # Mock任务执行失败
        with patch.object(ai_task_manager, '_handle_nlp_query') as mock_handle:
            mock_handle.side_effect = Exception("模拟任务失败")
            
            # 等待任务执行和重试
            time.sleep(0.2)
        
        # Then: 验证重试机制
        task_status = ai_task_manager.get_task_status(task_id)
        assert task_status["status"] in ["FAILED", "PENDING"]  # 取决于重试状态
        if task_status["status"] == "FAILED":
            assert task_status["retry_count"] <= 2
    
    def test_ai_task_timeout_handling(self, ai_task_manager):
        """测试AI任务超时处理"""
        # Given: 超时配置很短的任务
        timeout_config = {
            "model_type": "slow_model",
            "epochs": 1000  # 故意设置很大的训练量
        }
        
        # When: 提交超时任务
        task_id = ai_task_manager.submit_model_training_task(
            timeout_config,
            priority=TaskPriority.MEDIUM,
            timeout_seconds=1  # 1秒超时
        )
        
        # Mock长时间执行
        with patch.object(ai_task_manager, '_handle_model_training') as mock_handle:
            def slow_execution(*args):
                time.sleep(2)  # 超过超时时间
                return {"success": True}
            
            mock_handle.side_effect = slow_execution
            
            # 等待超时
            time.sleep(1.5)
        
        # Then: 验证超时处理
        task_status = ai_task_manager.get_task_status(task_id)
        # 任务应该被标记为超时或取消
        assert task_status["status"] in ["FAILED", "CANCELLED"]
    
    def test_ai_task_batch_processing(self, ai_task_manager):
        """测试AI任务批处理"""
        # Given: 批量任务配置
        batch_configs = [
            {"query_text": f"批量查询{i}", "batch_id": "batch_001"} 
            for i in range(10)
        ]
        
        # When: 提交批量任务
        batch_result = ai_task_manager.submit_batch_tasks(
            "nlp_query",
            batch_configs,
            priority=TaskPriority.MEDIUM,
            parallel_execution=True
        )
        
        # Then: 验证批量提交
        assert batch_result["success"] is True
        assert batch_result["submitted_tasks"] == 10
        assert len(batch_result["task_ids"]) == 10
        assert batch_result["batch_id"] == "batch_001"
    
    def test_ai_task_performance_optimization(self, ai_task_manager):
        """测试AI任务性能优化"""
        # Given: 启用性能优化
        ai_task_manager.enable_performance_optimization(
            features=["task_batching", "resource_pooling", "caching"]
        )
        
        # When: 提交相似任务以测试优化
        similar_configs = [
            {"data_source": "stock_daily", "detection_method": "isolation_forest"},
            {"data_source": "stock_daily", "detection_method": "isolation_forest"},
            {"data_source": "stock_daily", "detection_method": "isolation_forest"}
        ]
        
        submitted_tasks = []
        for config in similar_configs:
            task_id = ai_task_manager.submit_anomaly_detection_task(config)
            submitted_tasks.append(task_id)
        
        # Then: 验证性能优化效果
        optimization_stats = ai_task_manager.get_optimization_statistics()
        assert "batching_efficiency" in optimization_stats
        assert "cache_hit_rate" in optimization_stats
        assert "resource_utilization" in optimization_stats


class TestModelTrainingTask:
    """模型训练任务测试"""
    
    def test_model_training_task_creation(self):
        """测试模型训练任务创建"""
        # Given: 训练任务配置
        task_config = {
            "model_type": "lstm_anomaly_detector",
            "hyperparameters": {"epochs": 100, "batch_size": 32},
            "training_data": "stock_daily",
            "validation_split": 0.2
        }
        
        # When: 创建训练任务
        training_task = ModelTrainingTask(
            task_id="training_001",
            config=task_config,
            priority=TaskPriority.HIGH
        )
        
        # Then: 验证任务创建
        assert training_task.task_id == "training_001"
        assert training_task.model_type == "lstm_anomaly_detector"
        assert training_task.hyperparameters["epochs"] == 100
        assert training_task.priority == TaskPriority.HIGH
    
    def test_model_training_task_execution(self):
        """测试模型训练任务执行"""
        # Given: 训练任务
        task_config = {
            "model_type": "lstm_anomaly_detector",
            "hyperparameters": {"epochs": 5},  # 小数量用于测试
            "training_data": "test_data"
        }
        
        training_task = ModelTrainingTask("training_002", task_config)
        
        # When: 执行训练任务
        with patch('src.ai_agent.model_trainer.ModelTrainer') as mock_trainer:
            mock_instance = Mock()
            mock_instance.train.return_value = {
                "success": True,
                "model_path": "models/lstm_002.pkl",
                "training_metrics": {"accuracy": 0.95, "loss": 0.05}
            }
            mock_trainer.return_value = mock_instance
            
            result = training_task.execute()
        
        # Then: 验证执行结果
        assert result["success"] is True
        assert "model_path" in result
        assert result["training_metrics"]["accuracy"] == 0.95


class TestInferenceTask:
    """推理任务测试"""
    
    def test_inference_task_creation(self):
        """测试推理任务创建"""
        # Given: 推理任务配置
        task_config = {
            "model_id": "anomaly_detector_v1.0",
            "input_data": "recent_market_data",
            "batch_size": 1000,
            "output_format": "scores"
        }
        
        # When: 创建推理任务
        inference_task = InferenceTask(
            task_id="inference_001",
            config=task_config,
            priority=TaskPriority.MEDIUM
        )
        
        # Then: 验证任务创建
        assert inference_task.task_id == "inference_001"
        assert inference_task.model_id == "anomaly_detector_v1.0"
        assert inference_task.batch_size == 1000
        assert inference_task.priority == TaskPriority.MEDIUM
    
    def test_inference_task_execution(self):
        """测试推理任务执行"""
        # Given: 推理任务
        task_config = {
            "model_id": "anomaly_detector_v1.0",
            "input_data": "test_data",
            "batch_size": 100
        }
        
        inference_task = InferenceTask("inference_002", task_config)
        
        # When: 执行推理任务
        with patch('src.ai_agent.model_inference.ModelInference') as mock_inference:
            mock_instance = Mock()
            mock_instance.predict.return_value = {
                "success": True,
                "predictions": [0.1, 0.8, 0.3],
                "confidence_scores": [0.9, 0.95, 0.85]
            }
            mock_inference.return_value = mock_instance
            
            result = inference_task.execute()
        
        # Then: 验证执行结果
        assert result["success"] is True
        assert len(result["predictions"]) == 3
        assert len(result["confidence_scores"]) == 3


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])