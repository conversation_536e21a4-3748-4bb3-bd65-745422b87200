#!/usr/bin/env python3
"""
统一CSV导入器测试

测试重构后的CSV导入系统：
1. FromC2C专用导入器
2. 数据字典权威性映射
3. 统一CSV导入逻辑
"""

import unittest
import tempfile
import pandas as pd
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from src.data_import.unified_csv_importer import UnifiedCSVImporter
from src.data_import.fromC2C_csv_main_contract_importer import FromC2C_csv_main_contract_importer
from src.data_import.mappers.data_dictionary_mapper import DataDictionaryMapper
from src.database.data_dictionary_schema import DataDictionarySchema


class TestUnifiedCSVImporter(unittest.TestCase):
    """统一CSV导入器测试"""
    
    def setUp(self):
        """测试设置"""
        self.environment = "test"
        
        # 创建测试用的模拟配置
        self.mock_config = {
            "csv": {
                "encoding": "utf-8",
                "delimiter": ",",
                "max_file_size_mb": 200,
                "batch_size": 1000
            }
        }
        
        # 创建临时测试文件
        self.temp_dir = Path(tempfile.mkdtemp())
        self.sample_fromC2C_file = self.temp_dir / "期货主连5min" / "AL_5分钟主连合约数据.csv"
        self.sample_fromC2C_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建FromC2C格式的测试数据
        fromC2C_data = {
            "index": ["2005-01-04 09:05:00", "2005-01-04 09:10:00"],
            "open": [16540.0, 16470.0],
            "close": [16470.0, 16420.0],
            "high": [16540.0, 16480.0],
            "low": [16400.0, 16420.0],
            "volume": [2370.0, 2114.0],
            "money": [195061100.0, 173826900.0],
            "open_interest": [34910.0, 34268.0],
            "contract_code": ["AL0503.XSGE", "AL0503.XSGE"],
            "date": ["2005-01-04", "2005-01-04"]
        }
        
        df = pd.DataFrame(fromC2C_data)
        df.to_csv(self.sample_fromC2C_file, index=False)
    
    def tearDown(self):
        """测试清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('src.data_import.unified_csv_importer.ConfigLoader')
    @patch('src.data_import.unified_csv_importer.DuckDBConnectionManager')
    def test_unified_importer_initialization(self, mock_connection_manager, mock_config_loader):
        """测试统一导入器初始化"""
        # 设置模拟配置
        mock_config_instance = Mock()
        mock_config_instance.get_config.return_value = self.mock_config
        mock_config_instance.get_csv_config.return_value = self.mock_config["csv"]
        mock_config_loader.return_value = mock_config_instance
        
        # 初始化导入器
        importer = UnifiedCSVImporter(self.environment)
        
        # 验证初始化
        self.assertEqual(importer.environment, self.environment)
        self.assertIsInstance(importer.data_dictionary_mapper, DataDictionaryMapper)
        self.assertIsInstance(importer.schema_manager, DataDictionarySchema)
        self.assertIn("FromC2C", importer.supported_data_sources)
    
    def test_data_dictionary_mapper(self):
        """测试数据字典映射器"""
        mapper = DataDictionaryMapper()
        
        # 测试FromC2C目录映射
        fromC2C_5min_file = Path("/Users/<USER>/Documents/Data/FromC2C/期货主连5min/AL_5分钟主连合约数据.csv")
        target_table = mapper.map_file_to_data_dictionary_table(fromC2C_5min_file)
        self.assertEqual(target_table, "fut_main_contract_kline_5min")
        
        fromC2C_15min_file = Path("/Users/<USER>/Documents/Data/FromC2C/期货主连15min/RB_15分钟主连合约数据.csv")
        target_table = mapper.map_file_to_data_dictionary_table(fromC2C_15min_file)
        self.assertEqual(target_table, "fut_main_contract_kline_15min")
        
        fromC2C_30min_file = Path("/Users/<USER>/Documents/Data/FromC2C/期货主连30min/IF_30分钟主连合约数据.csv")
        target_table = mapper.map_file_to_data_dictionary_table(fromC2C_30min_file)
        self.assertEqual(target_table, "fut_main_contract_kline_30min")
        
        # 测试数据字典表验证
        self.assertTrue(mapper.is_data_dictionary_table("fut_main_contract_kline_5min"))
        self.assertTrue(mapper.is_data_dictionary_table("fut_main_contract_kline_15min"))
        self.assertTrue(mapper.is_data_dictionary_table("stock_kline_daily"))
        self.assertFalse(mapper.is_data_dictionary_table("invalid_table"))
        
        # 测试支持的表列表
        supported_tables = mapper.get_supported_tables()
        self.assertIn("fut_main_contract_kline_5min", supported_tables)
        self.assertIn("fut_main_contract_kline_15min", supported_tables)
        self.assertIn("fut_main_contract_kline_30min", supported_tables)
        self.assertIn("stock_kline_daily", supported_tables)
    
    def test_data_dictionary_schema(self):
        """测试数据字典表结构"""
        schema_manager = DataDictionarySchema("test")
        
        # 测试表定义获取
        table_def = schema_manager.get_table_definition("fut_main_contract_kline_5min")
        self.assertIsNotNone(table_def)
        self.assertIn("create_sql", table_def)
        self.assertIn("indexes", table_def)
        
        # 测试支持的表
        supported_tables = schema_manager.get_supported_tables()
        self.assertIn("fut_main_contract_kline_5min", supported_tables)
        self.assertIn("fut_main_contract_kline_15min", supported_tables)
        self.assertIn("fut_main_contract_kline_30min", supported_tables)
        
        # 测试表验证
        self.assertTrue(schema_manager.is_data_dictionary_table("fut_main_contract_kline_15min"))
        self.assertFalse(schema_manager.is_data_dictionary_table("invalid_table"))
    
    @patch('src.data_import.fromC2C_csv_main_contract_importer.DuckDBConnectionManager')
    def test_fromC2C_importer(self, mock_connection_manager):
        """测试FromC2C专用导入器"""
        # 创建FromC2C导入器
        fromC2C_importer = FromC2C_csv_main_contract_importer("test")
        
        # 测试频率检测
        test_file_5min = Path("/Users/<USER>/Documents/Data/FromC2C/期货主连5min/AL_5分钟主连合约数据.csv")
        target_table = fromC2C_importer.detect_frequency_from_directory(test_file_5min)
        self.assertEqual(target_table, "fut_main_contract_kline_5min")
        
        test_file_15min = Path("/Users/<USER>/Documents/Data/FromC2C/期货主连15min/RB_15分钟主连合约数据.csv")
        target_table = fromC2C_importer.detect_frequency_from_directory(test_file_15min)
        self.assertEqual(target_table, "fut_main_contract_kline_15min")
        
        # 测试合约代码提取
        contract_symbol = fromC2C_importer.extract_contract_symbol("AL0503.XSGE")
        self.assertEqual(contract_symbol, "AL")
        
        contract_symbol = fromC2C_importer.extract_contract_symbol("RB2501.XSGE")
        self.assertEqual(contract_symbol, "RB")
        
        # 测试无效合约代码
        contract_symbol = fromC2C_importer.extract_contract_symbol("INVALID")
        self.assertIsNone(contract_symbol)
        
        # 测试字段映射
        self.assertEqual(fromC2C_importer.fromC2C_field_mapping["money"], "amount")
        self.assertEqual(fromC2C_importer.fromC2C_field_mapping["date"], "trade_datetime")
        self.assertIsNone(fromC2C_importer.fromC2C_field_mapping["index"])
    
    @patch('src.data_import.unified_csv_importer.ConfigLoader')
    @patch('src.data_import.unified_csv_importer.DuckDBConnectionManager')
    def test_detect_data_source(self, mock_connection_manager, mock_config_loader):
        """测试数据源检测"""
        # 设置模拟配置
        mock_config_instance = Mock()
        mock_config_instance.get_config.return_value = self.mock_config
        mock_config_instance.get_csv_config.return_value = self.mock_config["csv"]
        mock_config_loader.return_value = mock_config_instance
        
        # 初始化导入器
        importer = UnifiedCSVImporter(self.environment)
        
        # 测试FromC2C数据源检测
        fromC2C_file = Path("/Users/<USER>/Documents/Data/FromC2C/期货主连5min/AL_5分钟主连合约数据.csv")
        detected_source = importer.detect_data_source(fromC2C_file)
        self.assertEqual(detected_source, "FromC2C")
        
        # 测试其他路径
        other_file = Path("/other/path/data.csv")
        detected_source = importer.detect_data_source(other_file)
        self.assertIsNone(detected_source)
    
    @patch('src.data_import.unified_csv_importer.ConfigLoader')
    @patch('src.data_import.unified_csv_importer.DuckDBConnectionManager')
    def test_supported_data_sources(self, mock_connection_manager, mock_config_loader):
        """测试支持的数据源"""
        # 设置模拟配置
        mock_config_instance = Mock()
        mock_config_instance.get_config.return_value = self.mock_config
        mock_config_instance.get_csv_config.return_value = self.mock_config["csv"]
        mock_config_loader.return_value = mock_config_instance
        
        # 初始化导入器
        importer = UnifiedCSVImporter(self.environment)
        
        # 验证支持的数据源
        self.assertIn("FromC2C", importer.supported_data_sources)
        
        fromC2C_config = importer.supported_data_sources["FromC2C"]
        self.assertEqual(fromC2C_config["data_path"], "/Users/<USER>/Documents/Data/FromC2C")
        self.assertIn("fut_main_contract_kline_5min", fromC2C_config["supported_tables"])
        self.assertIn("fut_main_contract_kline_15min", fromC2C_config["supported_tables"])
        self.assertIn("fut_main_contract_kline_30min", fromC2C_config["supported_tables"])
    
    def test_fromC2C_data_standardization(self):
        """测试FromC2C数据标准化"""
        fromC2C_importer = FromC2C_csv_main_contract_importer("test")
        
        # 创建测试数据
        test_data = pd.DataFrame({
            "index": ["2005-01-04 09:05:00"],
            "open": [16540.0],
            "close": [16470.0],
            "high": [16540.0],
            "low": [16400.0],
            "volume": [2370],
            "money": [195061100.0],
            "open_interest": [34910],
            "contract_code": ["AL0503.XSGE"],
            "date": ["2005-01-04 09:05:00"]
        })
        
        # 标准化数据
        standardized_data = fromC2C_importer.standardize_fromC2C_data(test_data, "fut_main_contract_kline_5min")
        
        # 验证字段映射
        self.assertIn("amount", standardized_data.columns)  # money -> amount
        self.assertIn("trade_datetime", standardized_data.columns)  # date -> trade_datetime
        self.assertNotIn("money", standardized_data.columns)  # 原字段应被重命名
        self.assertNotIn("index", standardized_data.columns)  # index应被丢弃
        
        # 验证系统字段
        self.assertIn("created_at", standardized_data.columns)
        self.assertIn("updated_at", standardized_data.columns)
        
        # 验证必需字段
        required_columns = ["contract_code", "trade_datetime", "open", "high", "low", "close", "volume", "amount", "open_interest"]
        for col in required_columns:
            self.assertIn(col, standardized_data.columns)
    
    def test_fromC2C_data_compliance_validation(self):
        """测试FromC2C数据合规性验证"""
        fromC2C_importer = FromC2C_csv_main_contract_importer("test")
        
        # 创建有效数据
        valid_data = pd.DataFrame({
            "contract_code": ["AL0503.XSGE"],
            "trade_datetime": [pd.Timestamp("2005-01-04 09:05:00")],
            "open": [16540.0],
            "high": [16540.0],
            "low": [16400.0],
            "close": [16470.0],
            "volume": [2370],
            "amount": [195061100.0],
            "open_interest": [34910]
        })
        
        # 验证有效数据
        result = fromC2C_importer.validate_fromC2C_data_compliance(valid_data, "fut_main_contract_kline_5min")
        self.assertTrue(result["valid"])
        self.assertEqual(len(result["errors"]), 0)
        
        # 创建无效数据（负价格）
        invalid_data = valid_data.copy()
        invalid_data.loc[0, "open"] = -100.0
        
        result = fromC2C_importer.validate_fromC2C_data_compliance(invalid_data, "fut_main_contract_kline_5min")
        self.assertFalse(result["valid"])
        self.assertGreater(len(result["errors"]), 0)
    
    def test_table_compliance_validation(self):
        """测试表结构合规性验证"""
        mapper = DataDictionaryMapper()
        
        # 测试符合规范的列
        valid_columns = ["contract_code", "trade_datetime", "open", "high", "low", "close", "volume", "amount", "open_interest", "created_at", "updated_at"]
        result = mapper.validate_table_compliance("fut_main_contract_kline_5min", valid_columns)
        self.assertTrue(result["compliant"])
        self.assertEqual(len(result["missing_columns"]), 0)
        
        # 测试缺少必需列
        incomplete_columns = ["contract_code", "trade_datetime", "open", "high", "low"]  # 缺少 close, volume, amount, open_interest
        result = mapper.validate_table_compliance("fut_main_contract_kline_5min", incomplete_columns)
        self.assertFalse(result["compliant"])
        self.assertGreater(len(result["missing_columns"]), 0)
        self.assertIn("close", result["missing_columns"])
        self.assertIn("volume", result["missing_columns"])
        
        # 测试不存在的表
        result = mapper.validate_table_compliance("invalid_table", valid_columns)
        self.assertIn("error", result)


class TestDataDictionaryMappingIntegration(unittest.TestCase):
    """数据字典映射集成测试"""
    
    def test_futures_frequency_mapping_consistency(self):
        """测试期货频率映射的一致性"""
        mapper = DataDictionaryMapper()
        
        # 测试所有期货频率表都存在
        futures_tables = [
            "fut_main_contract_kline_5min",
            "fut_main_contract_kline_15min", 
            "fut_main_contract_kline_30min",
            "fut_main_contract_kline_daily"
        ]
        
        for table in futures_tables:
            self.assertTrue(mapper.is_data_dictionary_table(table), f"表 {table} 应该在数据字典中")
            
            # 获取表架构
            schema = mapper.get_table_schema(table)
            self.assertIsNotNone(schema, f"表 {table} 应该有架构定义")
            self.assertIn("required_columns", schema)
            self.assertIn("primary_key", schema)
            
            # 验证所有期货表都包含多品种数据
            self.assertEqual(schema["data_scope"], "all_futures_contracts")
    
    def test_fromC2C_directory_mapping_coverage(self):
        """测试FromC2C目录映射覆盖度"""
        mapper = DataDictionaryMapper()
        
        # FromC2C数据源的目录结构
        fromC2C_directories = [
            ("期货主连5min", "fut_main_contract_kline_5min"),
            ("期货主连15min", "fut_main_contract_kline_15min"),
            ("期货主连30min", "fut_main_contract_kline_30min")
        ]
        
        for directory, expected_table in fromC2C_directories:
            test_file = Path(f"/Users/<USER>/Documents/Data/FromC2C/{directory}/TEST_data.csv")
            mapped_table = mapper.map_file_to_data_dictionary_table(test_file)
            self.assertEqual(mapped_table, expected_table, 
                           f"目录 {directory} 应该映射到表 {expected_table}")
    
    def test_data_dictionary_authority_principle(self):
        """测试数据字典权威性原则"""
        mapper = DataDictionaryMapper()
        schema_manager = DataDictionarySchema("test")
        
        # 验证映射器和架构管理器的表列表一致
        mapper_tables = set(mapper.get_supported_tables())
        schema_tables = set(schema_manager.get_supported_tables())
        
        # 核心表必须一致
        core_tables = {
            "fut_main_contract_kline_5min",
            "fut_main_contract_kline_15min",
            "fut_main_contract_kline_30min", 
            "stock_kline_daily",
            "fut_basic_info",
            "stock_basic_info"
        }
        
        for table in core_tables:
            self.assertIn(table, mapper_tables, f"映射器缺少核心表: {table}")
            self.assertIn(table, schema_tables, f"架构管理器缺少核心表: {table}")


if __name__ == "__main__":
    unittest.main()