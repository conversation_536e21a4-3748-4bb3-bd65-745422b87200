#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA简化错误处理系统单元测试

测试简化错误处理系统的核心功能
版本: 1.0.0
创建时间: 2025-07-31
"""

import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path
import sys

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.simple_error import (
    ErrorLevel, ErrorInfo, SimpleErrorHandler,
    safe_call, error_handler_decorator, validate_input,
    get_error_handler, handle_error, get_error_summary, clear_error_history
)


class TestErrorLevel(unittest.TestCase):
    """错误级别枚举测试"""
    
    def test_error_levels_exist(self):
        """测试错误级别枚举值"""
        self.assertEqual(ErrorLevel.INFO.value, "info")
        self.assertEqual(ErrorLevel.WARNING.value, "warning")
        self.assertEqual(ErrorLevel.ERROR.value, "error")
        self.assertEqual(ErrorLevel.CRITICAL.value, "critical")


class TestErrorInfo(unittest.TestCase):
    """错误信息数据类测试"""
    
    def test_error_info_creation(self):
        """测试错误信息创建"""
        error_info = ErrorInfo(
            level=ErrorLevel.ERROR,
            message="测试错误",
            location="test.py:10",
            suggestion="检查输入参数"
        )
        
        self.assertEqual(error_info.level, ErrorLevel.ERROR)
        self.assertEqual(error_info.message, "测试错误")
        self.assertEqual(error_info.location, "test.py:10")
        self.assertEqual(error_info.suggestion, "检查输入参数")
        self.assertIsInstance(error_info.details, dict)
    
    def test_error_info_str_representation(self):
        """测试错误信息字符串表示"""
        error_info = ErrorInfo(
            level=ErrorLevel.WARNING,
            message="测试警告",
            location="test.py:20",
            suggestion="这是建议"
        )
        
        str_repr = str(error_info)
        self.assertIn("WARNING", str_repr)
        self.assertIn("测试警告", str_repr)
        self.assertIn("test.py:20", str_repr)
        self.assertIn("建议: 这是建议", str_repr)
    
    def test_error_info_without_location_and_suggestion(self):
        """测试无位置和建议的错误信息"""
        error_info = ErrorInfo(
            level=ErrorLevel.ERROR,
            message="简单错误"
        )
        
        str_repr = str(error_info)
        self.assertIn("ERROR", str_repr)
        self.assertIn("简单错误", str_repr)
        self.assertNotIn("[", str_repr)  # 无位置信息
        self.assertNotIn("建议:", str_repr)  # 无建议


class TestSimpleErrorHandler(unittest.TestCase):
    """简化错误处理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.handler = SimpleErrorHandler()
    
    def test_handler_initialization(self):
        """测试处理器初始化"""
        self.assertEqual(self.handler.error_count, 0)
        self.assertEqual(len(self.handler.error_history), 0)
        self.assertEqual(self.handler.max_history, 100)
        self.assertIsNotNone(self.handler.suggestion_map)
    
    def test_handle_error_basic(self):
        """测试基本错误处理"""
        error = ValueError("测试错误")
        error_info = self.handler.handle_error(error, ErrorLevel.ERROR, "测试上下文")
        
        self.assertEqual(error_info.level, ErrorLevel.ERROR)
        self.assertEqual(error_info.message, "测试错误")
        self.assertEqual(error_info.details["error_type"], "ValueError")
        self.assertEqual(error_info.details["context"], "测试上下文")
        self.assertEqual(self.handler.error_count, 1)
        self.assertEqual(len(self.handler.error_history), 1)
    
    def test_suggestion_mapping(self):
        """测试建议映射"""
        file_error = FileNotFoundError("文件未找到")
        error_info = self.handler.handle_error(file_error)
        
        self.assertIn("文件路径", error_info.suggestion)
        
        permission_error = PermissionError("权限被拒绝")
        error_info2 = self.handler.handle_error(permission_error)
        
        self.assertIn("权限", error_info2.suggestion)
    
    def test_custom_suggestion(self):
        """测试自定义建议"""
        error = ValueError("自定义错误")
        custom_suggestion = "这是自定义建议"
        error_info = self.handler.handle_error(error, suggestion=custom_suggestion)
        
        self.assertEqual(error_info.suggestion, custom_suggestion)
    
    def test_error_location(self):
        """测试错误位置获取"""
        try:
            raise ValueError("位置测试")
        except Exception as e:
            error_info = self.handler.handle_error(e)
            
            # 应该包含文件名和行号
            self.assertIn("test_simple_error.py", error_info.location)
            self.assertIn(":", error_info.location)  # 包含行号分隔符
    
    def test_error_history_limit(self):
        """测试错误历史限制"""
        # 设置较小的历史限制进行测试
        self.handler.max_history = 3
        
        # 添加超过限制的错误
        for i in range(5):
            error = ValueError(f"错误 {i}")
            self.handler.handle_error(error)
        
        # 应该只保留最后3个错误
        self.assertEqual(len(self.handler.error_history), 3)
        self.assertEqual(self.handler.error_count, 5)  # 总计数不变
    
    def test_get_error_summary(self):
        """测试错误摘要获取"""
        # 添加不同级别的错误
        self.handler.handle_error(ValueError("错误1"), ErrorLevel.ERROR)
        self.handler.handle_error(TypeError("错误2"), ErrorLevel.WARNING)
        self.handler.handle_error(KeyError("错误3"), ErrorLevel.ERROR)
        
        summary = self.handler.get_error_summary()
        
        self.assertEqual(summary["total_errors"], 3)
        self.assertEqual(summary["level_counts"]["error"], 2)
        self.assertEqual(summary["level_counts"]["warning"], 1)
        self.assertEqual(len(summary["recent_errors"]), 3)
        self.assertIsInstance(summary["suggestions_available"], int)
    
    def test_clear_history(self):
        """测试清空历史"""
        # 添加一些错误
        self.handler.handle_error(ValueError("错误1"))
        self.handler.handle_error(ValueError("错误2"))
        
        self.assertEqual(self.handler.error_count, 2)
        self.assertEqual(len(self.handler.error_history), 2)
        
        # 清空历史
        self.handler.clear_history()
        
        self.assertEqual(self.handler.error_count, 0)
        self.assertEqual(len(self.handler.error_history), 0)


class TestSafeCall(unittest.TestCase):
    """安全调用测试"""
    
    def test_safe_call_success(self):
        """测试安全调用成功情况"""
        def success_func(x, y):
            return x + y
        
        result = safe_call(success_func, 2, 3)
        self.assertEqual(result, 5)
    
    def test_safe_call_with_exception(self):
        """测试安全调用异常情况"""
        def error_func():
            raise ValueError("测试异常")
        
        result = safe_call(error_func, default_return="fallback")
        self.assertEqual(result, "fallback")
    
    def test_safe_call_with_context(self):
        """测试带上下文的安全调用"""
        def error_func():
            raise RuntimeError("上下文测试")
        
        result = safe_call(
            error_func,
            error_level=ErrorLevel.WARNING,
            default_return=None,
            context="测试上下文"
        )
        
        self.assertIsNone(result)


class TestErrorHandlerDecorator(unittest.TestCase):
    """错误处理装饰器测试"""
    
    def test_decorator_success(self):
        """测试装饰器成功情况"""
        @error_handler_decorator()
        def success_func(value):
            return value * 2
        
        result = success_func(5)
        self.assertEqual(result, 10)
    
    def test_decorator_with_exception(self):
        """测试装饰器异常情况"""
        @error_handler_decorator(default_return="default")
        def error_func():
            raise ValueError("装饰器测试")
        
        result = error_func()
        self.assertEqual(result, "default")
    
    def test_decorator_with_reraise(self):
        """测试装饰器重新抛出异常"""
        @error_handler_decorator(reraise=True)
        def error_func():
            raise ValueError("重新抛出测试")
        
        with self.assertRaises(ValueError):
            error_func()


class TestValidateInput(unittest.TestCase):
    """输入验证测试"""
    
    def test_valid_input(self):
        """测试有效输入"""
        result = validate_input(
            10,
            lambda x: isinstance(x, int) and x > 0,
            "必须是正整数"
        )
        self.assertEqual(result, 10)
    
    def test_invalid_input(self):
        """测试无效输入"""
        with self.assertRaises(ValueError) as cm:
            validate_input(
                -5,
                lambda x: isinstance(x, int) and x > 0,
                "必须是正整数",
                "请输入大于0的整数"
            )
        
        self.assertIn("必须是正整数", str(cm.exception))
    
    def test_validator_exception(self):
        """测试验证器本身异常"""
        def buggy_validator(x):
            raise RuntimeError("验证器错误")
        
        # 验证器出错时返回原值
        result = validate_input(
            "test",
            buggy_validator,
            "验证失败"
        )
        self.assertEqual(result, "test")


class TestConvenienceFunctions(unittest.TestCase):
    """便捷函数测试"""
    
    def setUp(self):
        """测试前准备"""
        # 清空错误历史
        clear_error_history()
    
    def test_get_error_handler_singleton(self):
        """测试错误处理器单例模式"""
        handler1 = get_error_handler()
        handler2 = get_error_handler()
        
        self.assertIs(handler1, handler2)
    
    def test_handle_error_function(self):
        """测试处理错误便捷函数"""
        error = ValueError("便捷函数测试")
        error_info = handle_error(error, ErrorLevel.WARNING, "测试上下文")
        
        self.assertEqual(error_info.level, ErrorLevel.WARNING)
        self.assertEqual(error_info.message, "便捷函数测试")
        self.assertEqual(error_info.details["context"], "测试上下文")
    
    def test_get_error_summary_function(self):
        """测试获取错误摘要便捷函数"""
        # 添加一个错误
        handle_error(ValueError("摘要测试"))
        
        summary = get_error_summary()
        
        self.assertEqual(summary["total_errors"], 1)
        self.assertIn("level_counts", summary)
        self.assertIn("recent_errors", summary)
    
    def test_clear_error_history_function(self):
        """测试清空错误历史便捷函数"""
        # 添加错误
        handle_error(ValueError("历史测试"))
        
        summary_before = get_error_summary()
        self.assertEqual(summary_before["total_errors"], 1)
        
        # 清空历史
        clear_error_history()
        
        summary_after = get_error_summary()
        self.assertEqual(summary_after["total_errors"], 0)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """测试前准备"""
        clear_error_history()
    
    def test_full_error_handling_workflow(self):
        """测试完整错误处理流程"""
        # 1. 处理不同类型的错误
        errors = [
            (FileNotFoundError("文件不存在"), ErrorLevel.ERROR),
            (ValueError("值错误"), ErrorLevel.WARNING),
            (TypeError("类型错误"), ErrorLevel.ERROR)
        ]
        
        for error, level in errors:
            handle_error(error, level, f"处理 {type(error).__name__}")
        
        # 2. 检查错误统计
        summary = get_error_summary()
        self.assertEqual(summary["total_errors"], 3)
        self.assertEqual(summary["level_counts"]["error"], 2)
        self.assertEqual(summary["level_counts"]["warning"], 1)
        
        # 3. 验证所有错误都有建议
        self.assertGreater(summary["suggestions_available"], 0)
        
        # 4. 测试安全调用集成
        def risky_operation(should_fail=False):
            if should_fail:
                raise RuntimeError("操作失败")
            return "操作成功"
        
        success_result = safe_call(risky_operation, should_fail=False)
        self.assertEqual(success_result, "操作成功")
        
        fail_result = safe_call(
            risky_operation,
            should_fail=True,
            default_return="安全回退"
        )
        self.assertEqual(fail_result, "安全回退")
        
        # 5. 验证最终统计
        final_summary = get_error_summary()
        self.assertEqual(final_summary["total_errors"], 4)  # 新增1个错误
    
    def test_error_level_escalation(self):
        """测试错误级别升级场景"""
        # 模拟逐步升级的错误处理
        
        # 第一次：信息级别
        handle_error(
            RuntimeError("连接不稳定"),
            ErrorLevel.INFO,
            "网络检测",
            "这是正常的网络波动"
        )
        
        # 第二次：警告级别
        handle_error(
            RuntimeError("连接频繁中断"),
            ErrorLevel.WARNING,
            "网络监控",
            "检查网络配置"
        )
        
        # 第三次：错误级别
        handle_error(
            RuntimeError("连接完全失败"),
            ErrorLevel.ERROR,
            "网络故障",
            "联系网络管理员"
        )
        
        # 验证升级处理
        summary = get_error_summary()
        self.assertEqual(summary["total_errors"], 3)
        self.assertEqual(summary["level_counts"]["info"], 1)
        self.assertEqual(summary["level_counts"]["warning"], 1)
        self.assertEqual(summary["level_counts"]["error"], 1)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)