#!/usr/bin/env python3
"""
积分消耗计算引擎测试模块
针对TUSHARE API积分管理的TDD测试

测试覆盖：
- F2.T2.S1: PointsCalculator积分消耗计算引擎
- API积分映射表和消耗预估
- 预调用积分预估算法
- 积分使用统计和监控
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from src.tushare.points_calculator import PointsCalculator, ApiPointsMap, PointsUsageStats


class TestPointsCalculator:
    """积分计算引擎测试类 - F2.T2.S1"""
    
    def setup_method(self):
        """测试设置"""
        self.calculator = PointsCalculator()
    
    def test_points_calculator_initialization(self):
        """测试积分计算器初始化 - F2.T2.S1"""
        assert self.calculator is not None
        assert hasattr(self.calculator, 'api_points_map')
        assert hasattr(self.calculator, 'usage_stats')
        assert isinstance(self.calculator.usage_stats, PointsUsageStats)
    
    def test_calculate_api_call_points_futures_daily(self):
        """测试期货日线数据积分计算 - F2.T2.S1"""
        # 期货日线数据调用消耗5积分
        points = self.calculator.calculate_api_call_points('fut_daily')
        assert points == 5
        
        # 验证积分映射
        assert 'fut_daily' in self.calculator.api_points_map
        assert self.calculator.api_points_map['fut_daily'] == 5
    
    def test_calculate_api_call_points_stock_daily(self):
        """测试股票日线数据积分计算 - F2.T2.S1"""
        # 股票日线数据调用消耗1积分
        points = self.calculator.calculate_api_call_points('daily')
        assert points == 1
        
        # 验证积分映射
        assert 'daily' in self.calculator.api_points_map
        assert self.calculator.api_points_map['daily'] == 1
    
    def test_calculate_api_call_points_stock_basic(self):
        """测试股票基础信息积分计算 - F2.T2.S1"""
        # 股票基础信息调用消耗1积分
        points = self.calculator.calculate_api_call_points('stock_basic')
        assert points == 1
    
    def test_calculate_api_call_points_unknown_api(self):
        """测试未知API的积分计算 - F2.T2.S1"""
        # 未知API使用默认积分（10积分，保守估计）
        points = self.calculator.calculate_api_call_points('unknown_api')
        assert points == 10  # 默认保守估计
    
    def test_estimate_batch_points_consumption(self):
        """测试批量请求积分预估 - F2.T2.S1"""
        # 批量期货日线请求：3次调用 × 5积分 = 15积分
        estimated_points = self.calculator.estimate_batch_points('fut_daily', batch_count=3)
        assert estimated_points == 15
        
        # 批量股票日线请求：10次调用 × 1积分 = 10积分
        estimated_points = self.calculator.estimate_batch_points('daily', batch_count=10)
        assert estimated_points == 10
    
    def test_can_afford_api_call_sufficient_budget(self):
        """测试积分预算充足情况 - F2.T2.S1"""
        # 设置当前可用积分为100
        self.calculator.usage_stats.update_available_points(100)
        
        # 期货日线调用需要5积分，应该可以承受
        can_afford = self.calculator.can_afford_api_call('fut_daily')
        assert can_afford is True
        
        # 获取需要的积分数
        required_points = self.calculator.get_required_points('fut_daily')
        assert required_points == 5
    
    def test_can_afford_api_call_insufficient_budget(self):
        """测试积分预算不足情况 - F2.T2.S1"""
        # 设置当前可用积分为3
        self.calculator.usage_stats.update_available_points(3)
        
        # 期货日线调用需要5积分，积分不足
        can_afford = self.calculator.can_afford_api_call('fut_daily')
        assert can_afford is False
        
        # 但股票日线调用需要1积分，应该可以承受
        can_afford = self.calculator.can_afford_api_call('daily')
        assert can_afford is True
    
    def test_record_api_call_consumption(self):
        """测试API调用积分消耗记录 - F2.T2.S1"""
        # 设置初始积分
        self.calculator.usage_stats.update_available_points(100)
        initial_points = self.calculator.usage_stats.available_points
        
        # 记录一次期货日线调用
        consumed_points = self.calculator.record_api_call('fut_daily')
        
        # 验证消耗记录
        assert consumed_points == 5
        assert self.calculator.usage_stats.available_points == initial_points - 5
        assert self.calculator.usage_stats.total_consumed == 5
        assert self.calculator.usage_stats.call_count == 1
    
    def test_get_daily_usage_summary(self):
        """测试每日使用统计摘要 - F2.T2.S1"""
        # 模拟一些API调用
        self.calculator.usage_stats.update_available_points(100)
        self.calculator.record_api_call('fut_daily')  # 5积分
        self.calculator.record_api_call('daily')      # 1积分
        self.calculator.record_api_call('fut_daily')  # 5积分
        
        # 获取使用摘要
        summary = self.calculator.get_usage_summary()
        
        # 验证摘要内容
        assert summary['total_consumed'] == 11
        assert summary['call_count'] == 3
        assert summary['available_points'] == 89
        assert 'efficiency_rate' in summary
        assert 'api_breakdown' in summary
        
        # 验证API分解统计
        breakdown = summary['api_breakdown']
        assert breakdown['fut_daily']['calls'] == 2
        assert breakdown['fut_daily']['points'] == 10
        assert breakdown['daily']['calls'] == 1
        assert breakdown['daily']['points'] == 1


class TestApiPointsMap:
    """API积分映射表测试类"""
    
    def test_api_points_map_completeness(self):
        """测试API积分映射表完整性"""
        api_map = ApiPointsMap.get_default_map()
        
        # 验证重要API都有映射
        important_apis = [
            'stock_basic', 'daily', 'fut_daily', 'fut_basic',
            'trade_cal', 'index_daily', 'adj_factor'
        ]
        
        for api in important_apis:
            assert api in api_map, f"API {api} should be in points map"
            assert isinstance(api_map[api], int), f"Points for {api} should be integer"
            assert api_map[api] > 0, f"Points for {api} should be positive"
    
    def test_api_points_map_values_reasonable(self):
        """测试API积分映射值合理性"""
        api_map = ApiPointsMap.get_default_map()
        
        # 验证积分值在合理范围内
        for api, points in api_map.items():
            assert 1 <= points <= 50, f"Points for {api} should be between 1-50, got {points}"
        
        # 验证期货数据比股票数据消耗更多积分
        assert api_map['fut_daily'] >= api_map['daily']
        assert api_map['fut_basic'] >= api_map['stock_basic']


class TestPointsUsageStats:
    """积分使用统计测试类"""
    
    def setup_method(self):
        """测试设置"""
        self.stats = PointsUsageStats(total_budget=2100)
    
    def test_usage_stats_initialization(self):
        """测试使用统计初始化"""
        assert self.stats.total_budget == 2100
        assert self.stats.available_points == 2100
        assert self.stats.total_consumed == 0
        assert self.stats.call_count == 0
        assert len(self.stats.daily_usage) == 0
    
    def test_update_available_points(self):
        """测试可用积分更新"""
        # 更新可用积分
        self.stats.update_available_points(1500)
        assert self.stats.available_points == 1500
        
        # 计算消耗的积分
        consumed = self.stats.total_budget - self.stats.available_points
        assert consumed == 600
    
    def test_record_consumption(self):
        """测试消耗记录"""
        # 记录消耗
        self.stats.record_consumption('fut_daily', 5)
        
        # 验证统计更新
        assert self.stats.available_points == 2095
        assert self.stats.total_consumed == 5
        assert self.stats.call_count == 1
        
        # 验证API统计
        assert 'fut_daily' in self.stats.api_usage
        assert self.stats.api_usage['fut_daily']['calls'] == 1
        assert self.stats.api_usage['fut_daily']['points'] == 5
    
    def test_get_efficiency_rate(self):
        """测试使用效率计算"""
        # 模拟一些使用
        self.stats.record_consumption('fut_daily', 5)
        self.stats.record_consumption('daily', 1)
        
        # 计算效率（成功调用数 / 总消耗积分）
        efficiency = self.stats.get_efficiency_rate()
        
        # 2次成功调用 / 6积分消耗 = 0.333...
        expected_efficiency = 2 / 6
        assert abs(efficiency - expected_efficiency) < 0.001
    
    def test_is_budget_exhausted(self):
        """测试预算耗尽检查"""
        # 初始状态不应该耗尽
        assert self.stats.is_budget_exhausted() is False
        
        # 消耗所有积分
        self.stats.record_consumption('fut_daily', 2100)
        
        # 现在应该耗尽了
        assert self.stats.is_budget_exhausted() is True
        assert self.stats.available_points == 0
    
    def test_get_remaining_budget_percentage(self):
        """测试剩余预算百分比"""
        # 初始应该是100%
        assert self.stats.get_remaining_budget_percentage() == 100.0
        
        # 消耗一半
        self.stats.record_consumption('fut_daily', 1050)
        
        # 应该是50%
        percentage = self.stats.get_remaining_budget_percentage()
        assert abs(percentage - 50.0) < 0.1