#!/usr/bin/env python3
"""
WebSocket集成测试套件
基于AQUA宪法TDD工作流
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
import asyncio

from src.data_import.websocket_manager import WebSocketManager, get_websocket_manager
from src.data_import.task_control_manager import TaskControlManager


class TestWebSocketManager:
    """WebSocket管理器测试"""
    
    def test_websocket_manager_initialization(self):
        """测试WebSocket管理器初始化"""
        manager = WebSocketManager("test")
        
        assert manager.environment == "test"
        assert len(manager._connections) == 0
        assert len(manager._task_subscriptions) == 0
        assert manager._heartbeat_interval == 30
    
    @pytest.mark.asyncio
    async def test_websocket_connect_mock(self):
        """测试WebSocket连接（模拟）"""
        manager = WebSocketManager("test")
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock()
        mock_websocket.accept = AsyncMock()
        
        # 测试连接
        await manager.connect(mock_websocket, {"client_id": "test-123"})
        
        # 验证连接已添加
        assert mock_websocket in manager._connections
        assert id(mock_websocket) in manager._connection_metadata
        
        # 验证accept被调用
        mock_websocket.accept.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_websocket_disconnect_mock(self):
        """测试WebSocket断开连接（模拟）"""
        manager = WebSocketManager("test")
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock()
        mock_websocket.accept = AsyncMock()
        
        # 先连接
        await manager.connect(mock_websocket, {"client_id": "test-123"})
        
        # 然后断开
        await manager.disconnect(mock_websocket)
        
        # 验证连接已移除
        assert mock_websocket not in manager._connections
        assert id(mock_websocket) not in manager._connection_metadata
    
    @pytest.mark.asyncio
    async def test_task_subscription_mock(self):
        """测试任务订阅（模拟）"""
        manager = WebSocketManager("test")
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock()
        mock_websocket.accept = AsyncMock()
        mock_websocket.send_text = AsyncMock()
        
        # 先连接
        await manager.connect(mock_websocket, {"client_id": "test-123"})
        
        # 订阅任务
        success = await manager.subscribe_task(mock_websocket, "task-456")
        
        assert success is True
        assert "task-456" in manager._task_subscriptions
        assert mock_websocket in manager._task_subscriptions["task-456"]
        
        # 验证订阅确认消息被发送
        mock_websocket.send_text.assert_called()
    
    @pytest.mark.asyncio
    async def test_task_progress_broadcast_mock(self):
        """测试任务进度广播（模拟）"""
        manager = WebSocketManager("test")
        
        # 创建多个模拟WebSocket
        mock_ws1 = AsyncMock()
        mock_ws2 = AsyncMock()
        mock_ws1.accept = AsyncMock()
        mock_ws2.accept = AsyncMock()
        mock_ws1.send_text = AsyncMock()
        mock_ws2.send_text = AsyncMock()
        
        # 连接并订阅
        await manager.connect(mock_ws1, {"client_id": "client-1"})
        await manager.connect(mock_ws2, {"client_id": "client-2"})
        
        await manager.subscribe_task(mock_ws1, "task-789")
        await manager.subscribe_task(mock_ws2, "task-789")
        
        # 重置mock以忽略之前的调用
        mock_ws1.send_text.reset_mock()
        mock_ws2.send_text.reset_mock()
        
        # 广播进度更新
        progress_data = {
            "progress": 50,
            "message": "正在处理...",
            "stage": "processing"
        }
        
        await manager.broadcast_task_progress("task-789", progress_data)
        
        # 验证所有订阅者都收到了消息
        mock_ws1.send_text.assert_called_once()
        mock_ws2.send_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_task_status_change_broadcast_mock(self):
        """测试任务状态变更广播（模拟）"""
        manager = WebSocketManager("test")
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock()
        mock_websocket.accept = AsyncMock()
        mock_websocket.send_text = AsyncMock()
        
        # 连接并订阅
        await manager.connect(mock_websocket, {"client_id": "test-client"})
        await manager.subscribe_task(mock_websocket, "task-status-test")
        
        # 重置mock
        mock_websocket.send_text.reset_mock()
        
        # 广播状态变更
        await manager.broadcast_task_status_change(
            "task-status-test", 
            "running", 
            "completed",
            {"records_processed": 1000}
        )
        
        # 验证状态变更消息被发送
        mock_websocket.send_text.assert_called_once()
        
        # 解析发送的消息内容
        call_args = mock_websocket.send_text.call_args[0][0]
        import json
        message = json.loads(call_args)
        
        assert message["type"] == "task_status_change"
        assert message["data"]["task_id"] == "task-status-test"
        assert message["data"]["old_status"] == "running"
        assert message["data"]["new_status"] == "completed"
    
    @pytest.mark.asyncio
    async def test_client_message_handling_mock(self):
        """测试客户端消息处理（模拟）"""
        manager = WebSocketManager("test")
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock()
        mock_websocket.accept = AsyncMock()
        mock_websocket.send_text = AsyncMock()
        
        # 连接
        await manager.connect(mock_websocket, {"client_id": "test-client"})
        
        # 重置mock
        mock_websocket.send_text.reset_mock()
        
        # 测试ping消息
        ping_message = {"type": "ping", "data": {}}
        await manager.handle_client_message(mock_websocket, ping_message)
        
        # 验证pong响应
        mock_websocket.send_text.assert_called_once()
        call_args = mock_websocket.send_text.call_args[0][0]
        import json
        response = json.loads(call_args)
        
        assert response["type"] == "pong"
    
    def test_get_websocket_manager_singleton(self):
        """测试WebSocket管理器单例模式"""
        manager1 = get_websocket_manager("test")
        manager2 = get_websocket_manager("test")
        
        # 应该是同一个实例
        assert manager1 is manager2
    
    def test_websocket_statistics(self):
        """测试WebSocket统计信息"""
        manager = WebSocketManager("test")
        
        stats = manager.get_statistics()
        
        assert isinstance(stats, dict)
        assert "total_connections" in stats
        assert "total_subscriptions" in stats
        assert "tasks_with_subscribers" in stats
        assert "environment" in stats
        assert stats["environment"] == "test"
        assert stats["total_connections"] == 0


class TestWebSocketIntegration:
    """WebSocket集成测试"""
    
    @pytest.mark.asyncio
    async def test_websocket_with_task_control_integration(self):
        """测试WebSocket与任务控制的集成"""
        # 创建管理器实例
        websocket_manager = WebSocketManager("test")
        task_control_manager = TaskControlManager("test")
        
        # 设置引用
        websocket_manager.set_task_control_manager(task_control_manager)
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock()
        mock_websocket.accept = AsyncMock()
        mock_websocket.send_text = AsyncMock()
        
        # 创建模拟任务存储
        test_storage = {"test-task": {"status": "running", "progress": 50}}
        task_control_manager.register_task_storage("test", test_storage)
        
        # 连接并订阅
        await websocket_manager.connect(mock_websocket, {"client_id": "integration-test"})
        await websocket_manager.subscribe_task(mock_websocket, "test-task")
        
        # 验证当前任务状态被发送
        # 这会触发_send_current_task_status调用
        assert mock_websocket.send_text.call_count >= 2  # 连接确认 + 订阅确认
    
    @pytest.mark.asyncio
    async def test_websocket_error_handling(self):
        """测试WebSocket错误处理"""
        manager = WebSocketManager("test")
        
        # 创建会抛出异常的模拟WebSocket
        mock_websocket = AsyncMock()
        mock_websocket.accept = AsyncMock(side_effect=Exception("Connection failed"))
        
        # 测试连接异常处理
        with pytest.raises(Exception):
            await manager.connect(mock_websocket, {"client_id": "error-test"})
        
        # 验证连接没有被添加
        assert mock_websocket not in manager._connections