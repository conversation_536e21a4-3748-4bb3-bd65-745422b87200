#!/usr/bin/env python3
"""
导入历史和统计API测试套件
基于AQUA宪法TDD工作流
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from datetime import datetime, date

from src.api.routers.data_router import (
    get_import_history, 
    get_import_stats, 
    delete_import_record
)
from src.data_import.import_history_manager import ImportHistoryManager


@pytest.fixture
def mock_history_manager():
    """模拟历史记录管理器"""
    mock = Mock(spec=ImportHistoryManager)
    return mock


class TestImportHistoryAPI:
    """导入历史API测试"""
    
    @patch('src.api.routers.data_router.ImportHistoryManager')
    @patch('src.api.routers.data_router.get_default_environment')
    @pytest.mark.asyncio
    async def test_get_import_history_success(self, mock_get_env, mock_manager_class):
        """测试获取导入历史成功"""
        # 准备测试数据
        mock_get_env.return_value = "test"
        mock_manager = Mock()
        mock_manager_class.return_value = mock_manager
        
        mock_history_data = {
            "success": True,
            "data": {
                "records": [
                    {
                        "import_id": "test-123",
                        "import_type": "csv",
                        "source_info": {"file_name": "test.csv"},
                        "import_status": "completed",
                        "total_records": 1000,
                        "success_records": 950,
                        "error_records": 50,
                        "start_time": "2025-01-17T10:00:00",
                        "end_time": "2025-01-17T10:05:00",
                        "duration_seconds": 300
                    }
                ],
                "total_count": 1,
                "limit": 100,
                "offset": 0,
                "has_next": False
            },
            "message": "成功获取1条导入历史记录"
        }
        
        mock_manager.get_import_history.return_value = mock_history_data
        
        # 执行测试
        result = await get_import_history()
        
        # 验证结果
        assert result["success"] is True
        assert "data" in result
        assert len(result["data"]["records"]) == 1
        assert result["data"]["records"][0]["import_id"] == "test-123"
        
        # 验证调用参数
        mock_manager.get_import_history.assert_called_once_with(
            import_type=None,
            start_date=None,
            end_date=None,
            limit=100,
            offset=0
        )
    
    @patch('src.api.routers.data_router.ImportHistoryManager')
    def test_get_import_history_with_filters(self, mock_manager_class, test_client):
        """测试带筛选条件的导入历史查询"""
        mock_manager = Mock()
        mock_manager_class.return_value = mock_manager
        
        mock_history_data = {
            "success": True,
            "data": {"records": [], "total_count": 0},
            "message": "成功获取0条导入历史记录"
        }
        
        mock_manager.get_import_history.return_value = mock_history_data
        
        # 执行测试
        response = test_client.get(
            "/api/data/import/history",
            params={
                "importType": "mysql",
                "startDate": "2025-01-01",
                "endDate": "2025-01-31",
                "limit": 50,
                "offset": 10
            }
        )
        
        # 验证结果
        assert response.status_code == 200
        
        # 验证调用参数
        mock_manager.get_import_history.assert_called_once_with(
            import_type="mysql",
            start_date="2025-01-01",
            end_date="2025-01-31",
            limit=50,
            offset=10
        )
    
    @patch('src.api.routers.data_router.ImportHistoryManager')
    def test_get_import_history_error(self, mock_manager_class, test_client):
        """测试获取导入历史错误处理"""
        mock_manager = Mock()
        mock_manager_class.return_value = mock_manager
        
        # 模拟异常
        mock_manager.get_import_history.side_effect = Exception("数据库连接失败")
        
        # 执行测试
        response = test_client.get("/api/data/import/history")
        
        # 验证错误响应
        assert response.status_code == 500


class TestImportStatsAPI:
    """导入统计API测试"""
    
    @patch('src.api.routers.data_router.ImportHistoryManager')
    def test_get_import_stats_success(self, mock_manager_class, test_client):
        """测试获取导入统计成功"""
        mock_manager = Mock()
        mock_manager_class.return_value = mock_manager
        
        mock_stats_data = {
            "success": True,
            "data": {
                "period": "day",
                "stats": [
                    {
                        "stat_date": "2025-01-17",
                        "import_type": "csv",
                        "total_imports": 5,
                        "successful_imports": 4,
                        "failed_imports": 1,
                        "total_records": 5000,
                        "avg_duration_seconds": 120.5,
                        "success_rate": 0.8
                    }
                ],
                "summary": {
                    "total_periods": 1,
                    "total_imports": 5,
                    "successful_imports": 4,
                    "failed_imports": 1,
                    "total_records": 5000,
                    "avg_success_rate": 0.8
                }
            },
            "message": "成功获取1条统计记录"
        }
        
        mock_manager.get_import_stats.return_value = mock_stats_data
        
        # 执行测试
        response = test_client.get("/api/data/import/stats")
        
        # 验证结果
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert data["data"]["period"] == "day"
        assert len(data["data"]["stats"]) == 1
        
        # 验证调用参数
        mock_manager.get_import_stats.assert_called_once_with(
            period="day",
            start_date=None,
            end_date=None,
            import_type=None
        )
    
    @patch('src.api.routers.data_router.ImportHistoryManager')
    def test_get_import_stats_with_params(self, mock_manager_class, test_client):
        """测试带参数的统计查询"""
        mock_manager = Mock()
        mock_manager_class.return_value = mock_manager
        
        mock_stats_data = {
            "success": True,
            "data": {"stats": [], "summary": {}},
            "message": "成功获取0条统计记录"
        }
        
        mock_manager.get_import_stats.return_value = mock_stats_data
        
        # 执行测试
        response = test_client.get(
            "/api/data/import/stats",
            params={
                "period": "month",
                "importType": "fromc2c",
                "startDate": "2025-01-01",
                "endDate": "2025-01-31"
            }
        )
        
        # 验证结果
        assert response.status_code == 200
        
        # 验证调用参数
        mock_manager.get_import_stats.assert_called_once_with(
            period="month",
            start_date="2025-01-01",
            end_date="2025-01-31",
            import_type="fromc2c"
        )
    
    def test_get_import_stats_invalid_period(self, test_client):
        """测试无效统计周期"""
        response = test_client.get(
            "/api/data/import/stats",
            params={"period": "invalid"}
        )
        
        # 验证错误响应
        assert response.status_code == 400


class TestDeleteImportRecord:
    """删除导入记录测试"""
    
    @patch('src.api.routers.data_router.ImportHistoryManager')
    def test_delete_import_record_success(self, mock_manager_class, test_client):
        """测试删除导入记录成功"""
        mock_manager = Mock()
        mock_manager_class.return_value = mock_manager
        
        mock_delete_result = {
            "success": True,
            "message": "成功删除导入记录: test-123"
        }
        
        mock_manager.delete_import_record.return_value = mock_delete_result
        
        # 执行测试
        response = test_client.delete("/api/data/import/history/test-123")
        
        # 验证结果
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "成功删除" in data["message"]
        
        # 验证调用参数
        mock_manager.delete_import_record.assert_called_once_with("test-123")
    
    @patch('src.api.routers.data_router.ImportHistoryManager')
    def test_delete_import_record_not_found(self, mock_manager_class, test_client):
        """测试删除不存在的导入记录"""
        mock_manager = Mock()
        mock_manager_class.return_value = mock_manager
        
        mock_delete_result = {
            "success": False,
            "message": "导入记录不存在: nonexistent-123"
        }
        
        mock_manager.delete_import_record.return_value = mock_delete_result
        
        # 执行测试
        response = test_client.delete("/api/data/import/history/nonexistent-123")
        
        # 验证结果
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is False
        assert "不存在" in data["message"]
    
    def test_delete_import_record_empty_id(self, test_client):
        """测试删除空ID记录"""
        response = test_client.delete("/api/data/import/history/ ")
        
        # 验证错误响应
        assert response.status_code == 400


class TestImportHistoryManager:
    """导入历史记录管理器测试"""
    
    @patch('src.data_import.import_history_manager.DuckDBConnectionManager')
    @patch('src.data_import.import_history_manager.DataDictionarySchema')
    def test_record_import_start(self, mock_schema, mock_connection):
        """测试记录导入开始"""
        # 准备模拟对象
        mock_conn_manager = Mock()
        mock_connection.return_value = mock_conn_manager
        
        mock_schema_manager = Mock()
        mock_schema.return_value = mock_schema_manager
        
        # 创建管理器实例
        manager = ImportHistoryManager("test")
        
        # 执行测试
        source_info = {"type": "csv", "file_name": "test.csv"}
        import_config = {"mode": "replace"}
        
        import_id = manager.record_import_start(
            import_type="csv",
            source_info=source_info,
            target_tables=["test_table"],
            import_config=import_config
        )
        
        # 验证结果
        assert import_id is not None
        assert len(import_id) > 0
        
        # 验证数据库调用
        mock_conn_manager.execute_update.assert_called_once()
        call_args = mock_conn_manager.execute_update.call_args
        assert "INSERT INTO data_import_history" in call_args[0][0]
    
    @patch('src.data_import.import_history_manager.DuckDBConnectionManager')
    @patch('src.data_import.import_history_manager.DataDictionarySchema')
    def test_complete_import(self, mock_schema, mock_connection):
        """测试完成导入记录"""
        # 准备模拟对象
        mock_conn_manager = Mock()
        mock_connection.return_value = mock_conn_manager
        
        # 模拟查询结果
        mock_start_time = datetime.now()
        mock_conn_manager.execute_query.return_value = [[mock_start_time]]
        
        mock_schema_manager = Mock()
        mock_schema.return_value = mock_schema_manager
        
        # 创建管理器实例
        manager = ImportHistoryManager("test")
        
        # 执行测试
        manager.complete_import(
            import_id="test-123",
            status="completed",
            total_records=1000,
            success_records=950,
            error_records=50
        )
        
        # 验证数据库调用
        assert mock_conn_manager.execute_update.call_count == 2  # UPDATE + stats update
    
    @patch('src.data_import.import_history_manager.DuckDBConnectionManager')
    @patch('src.data_import.import_history_manager.DataDictionarySchema')
    def test_get_import_history(self, mock_schema, mock_connection):
        """测试获取导入历史"""
        # 准备模拟对象
        mock_conn_manager = Mock()
        mock_connection.return_value = mock_conn_manager
        
        # 模拟查询结果
        mock_count_result = [[5]]  # 总数
        mock_data_result = [
            [
                "test-123", "csv", '{"file_name": "test.csv"}', '["test_table"]',
                "completed", 1000, 950, 50,
                datetime.now(), datetime.now(), 300, None,
                '{"mode": "replace"}', '{"task_id": "task-123"}', datetime.now()
            ]
        ]
        
        mock_conn_manager.execute_query.side_effect = [mock_count_result, mock_data_result]
        
        mock_schema_manager = Mock()
        mock_schema.return_value = mock_schema_manager
        
        # 创建管理器实例
        manager = ImportHistoryManager("test")
        
        # 执行测试
        result = manager.get_import_history(
            import_type="csv",
            limit=10,
            offset=0
        )
        
        # 验证结果
        assert result["success"] is True
        assert len(result["data"]["records"]) == 1
        assert result["data"]["total_count"] == 5
        assert result["data"]["records"][0]["import_id"] == "test-123"
    
    @patch('src.data_import.import_history_manager.DuckDBConnectionManager')
    @patch('src.data_import.import_history_manager.DataDictionarySchema')
    def test_get_import_stats(self, mock_schema, mock_connection):
        """测试获取导入统计"""
        # 准备模拟对象
        mock_conn_manager = Mock()
        mock_connection.return_value = mock_conn_manager
        
        # 模拟查询结果
        mock_stats_result = [
            [
                date.today(), "csv", 5, 4, 1, 5000, 120.5, 0.8, '{}'
            ]
        ]
        
        mock_summary_result = [
            [1, 5, 4, 1, 5000, 0.8]
        ]
        
        mock_conn_manager.execute_query.side_effect = [mock_stats_result, mock_summary_result]
        
        mock_schema_manager = Mock()
        mock_schema.return_value = mock_schema_manager
        
        # 创建管理器实例
        manager = ImportHistoryManager("test")
        
        # 执行测试
        result = manager.get_import_stats(period="day")
        
        # 验证结果
        assert result["success"] is True
        assert len(result["data"]["stats"]) == 1
        assert result["data"]["summary"]["total_imports"] == 5
        assert result["data"]["stats"][0]["import_type"] == "csv"