#!/usr/bin/env python3
"""
跨平台配置测试模块
测试配置系统在不同平台下的兼容性和一致性

测试覆盖：
- TUSHARE配置验证
- 个人开发者配置验证
- 跨平台路径处理
- 环境变量处理
- 积分预算配置验证
"""

import os
import platform
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock
import pytest
import toml

from src.utils.config_validator import ConfigValidator, ValidationLevel


class TestCrossPlatformConfig:
    """跨平台配置测试类"""
    
    def setup_method(self):
        """测试设置"""
        self.test_config = {
            "platform": {
                "auto_detect": True,
                "normalize_paths": True,
                "supported_platforms": ["windows", "darwin", "linux"],
                "personal_dev": {
                    "env_vars": ["AQUA_CONFIG_DIR", "AQUA_DATA_DIR", "TUSHARE_TOKEN"],
                    "default_encoding": "utf-8",
                    "utf8_path_support": True
                }
            },
            "datasources": {
                "api": {
                    "tushare": {
                        "name": "Tushare Pro",
                        "enabled": True,
                        "token": "${TUSHARE_TOKEN}",
                        "rate_limit": 200,
                        "budget": {
                            "total_points": 2100,
                            "daily_limit": 50,
                            "emergency_reserve": 200
                        }
                    }
                }
            },
            "dev": {
                "performance": {
                    "personal_dev_mode": True,
                    "memory_limit_mb": 1536,
                    "startup_target_seconds": 2,
                    "memory_warning_threshold_mb": 1200
                }
            }
        }
    
    def test_tushare_config_validation_success(self):
        """测试TUSHARE配置验证 - 成功场景"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(self.test_config, f)
            temp_config_path = f.name
        
        try:
            with patch.dict(os.environ, {'TUSHARE_TOKEN': 'test_token_123'}):
                validator = ConfigValidator(temp_config_path)
                validator.load_config()
                validator.validate_tushare_config(validator.config, 'dev')
                
                # 应该没有严重错误
                errors = [r for r in validator.validation_results 
                         if r.level == ValidationLevel.ERROR]
                assert len(errors) == 0, f"不应该有错误: {[e.message for e in errors]}"
                
        finally:
            os.unlink(temp_config_path)
    
    def test_tushare_config_validation_missing_token(self):
        """测试TUSHARE配置验证 - 缺少Token"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(self.test_config, f)
            temp_config_path = f.name
        
        try:
            with patch.dict(os.environ, {}, clear=True):
                validator = ConfigValidator(temp_config_path)
                validator.load_config()
                validator.validate_tushare_config(validator.config, 'dev')
                
                # 应该有Token错误
                errors = [r for r in validator.validation_results 
                         if r.level == ValidationLevel.ERROR and 'TUSHARE_TOKEN' in r.message]
                assert len(errors) > 0, "应该检测到TUSHARE_TOKEN缺失错误"
                
        finally:
            os.unlink(temp_config_path)
    
    def test_tushare_budget_validation(self):
        """测试TUSHARE积分预算验证"""
        # 创建预算不足的配置
        budget_config = self.test_config.copy()
        budget_config['datasources']['api']['tushare']['budget']['daily_limit'] = 100  # 过高
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(budget_config, f)
            temp_config_path = f.name
        
        try:
            validator = ConfigValidator(temp_config_path)
            validator.load_config()
            validator.validate_tushare_config(validator.config, 'dev')
            
            # 应该有预算警告
            warnings = [r for r in validator.validation_results 
                       if r.level == ValidationLevel.WARNING and '积分' in r.message]
            assert len(warnings) > 0, "应该检测到积分预算警告"
            
        finally:
            os.unlink(temp_config_path)
    
    @patch('src.utils.config_validator.platform.system')
    def test_cross_platform_support_validation(self, mock_platform):
        """测试跨平台支持验证"""
        # 模拟不支持的平台
        mock_platform.return_value = 'FreeBSD'
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(self.test_config, f)
            temp_config_path = f.name
        
        try:
            validator = ConfigValidator(temp_config_path)
            validator.load_config()
            validator.validate_cross_platform_config(validator.config, 'dev')
            
            # 应该有平台不支持错误
            errors = [r for r in validator.validation_results 
                     if r.level == ValidationLevel.ERROR and '平台' in r.message]
            assert len(errors) > 0, "应该检测到平台不支持错误"
            
        finally:
            os.unlink(temp_config_path)
    
    def test_personal_dev_memory_config_validation(self):
        """测试个人开发者内存配置验证"""
        # 创建内存限制过高的配置
        memory_config = self.test_config.copy()
        memory_config['dev']['performance']['memory_limit_mb'] = 3000  # 过高
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(memory_config, f)
            temp_config_path = f.name
        
        try:
            validator = ConfigValidator(temp_config_path)
            validator.load_config()
            validator.validate_environment(('dev'))
            
            # 应该有内存限制警告
            warnings = [r for r in validator.validation_results 
                       if r.level == ValidationLevel.WARNING and '内存限制' in r.message]
            assert len(warnings) > 0, "应该检测到内存限制警告"
            
        finally:
            os.unlink(temp_config_path)
    
    def test_environment_variable_validation(self):
        """测试环境变量验证"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(self.test_config, f)
            temp_config_path = f.name
        
        try:
            # 清除所有环境变量
            with patch.dict(os.environ, {}, clear=True):
                validator = ConfigValidator(temp_config_path)
                validator.load_config()
                validator.validate_cross_platform_config(validator.config, 'dev')
                
                # 应该有环境变量警告
                warnings = [r for r in validator.validation_results 
                           if r.level == ValidationLevel.WARNING and 'TUSHARE_TOKEN' in r.message]
                assert len(warnings) > 0, "应该检测到TUSHARE_TOKEN环境变量警告"
                
        finally:
            os.unlink(temp_config_path)
    
    def test_current_platform_detection(self):
        """测试当前平台检测"""
        current_platform = platform.system().lower()
        
        # 验证当前平台在支持列表中
        supported_platforms = self.test_config['platform']['supported_platforms']
        platform_map = {'windows': 'windows', 'darwin': 'darwin', 'linux': 'linux'}
        mapped_platform = platform_map.get(current_platform)
        
        if mapped_platform:
            assert mapped_platform in supported_platforms, \
                f"当前平台{current_platform}应该在支持列表中"
    
    def test_config_file_encoding_utf8(self):
        """测试配置文件UTF-8编码支持"""
        # 创建包含中文的配置
        utf8_config = self.test_config.copy()
        utf8_config['datasources']['api']['tushare']['name'] = '推送Pro数据源'
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', 
                                       delete=False, encoding='utf-8') as f:
            toml.dump(utf8_config, f)
            temp_config_path = f.name
        
        try:
            validator = ConfigValidator(temp_config_path)
            validator.load_config()
            
            # 应该能正确加载中文配置
            tushare_name = validator.config['datasources']['api']['tushare']['name']
            assert tushare_name == '推送Pro数据源', "应该支持UTF-8中文配置"
            
        finally:
            os.unlink(temp_config_path)


def test_validator_integration():
    """集成测试 - 验证器完整功能"""
    # 使用实际配置文件进行测试
    config_path = Path("config/settings.toml")
    if config_path.exists():
        validator = ConfigValidator(str(config_path))
        results = validator.validate_all()
        
        # 打印验证结果
        validator.print_validation_results()
        
        # 不应该有严重错误
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        if errors:
            print(f"发现配置错误: {[e.message for e in errors]}")