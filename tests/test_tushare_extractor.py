#!/usr/bin/env python3
"""
TushareExtractor测试模块
针对TUSHARE数据源集成的完整TDD测试

测试覆盖：
- F2.T1.S1: TushareExtractor基础类创建和初始化
- F2.T1.S2: Token认证和连接验证  
- F2.T1.S3: SimpleExtractorFactory集成
- F2.T1.S4: 基础数据采集方法
- F2.T1.S5: 数据批量获取优化
- F2.T1.S6: 异常处理和日志记录
"""

import os
import pytest
from unittest.mock import patch, MagicMock
import polars as pl

from src.data_import.extractors.simple_extractor import SimpleExtractor, ExtractorException
from src.data_import.extractors.tushare_extractor import TushareExtractor, TushareApiError
from src.tushare.points_calculator import PointsCalculator


class TestTushareExtractorBasics:
    """TushareExtractor基础类测试"""
    
    def setup_method(self):
        """测试设置"""
        self.base_config = {
            "name": "tushare_test_extractor",
            "data_source": "tushare",
            "target_table": "test_fut_daily",
            "enabled": True,
            "batch_size": 500,
            "max_retries": 3
        }
    
    def test_tushare_extractor_initialization_success(self):
        """测试TUSHARE提取器成功初始化 - F2.T1.S1"""
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.base_config)
            
            # 验证基础属性
            assert extractor.name == "tushare_test_extractor"
            assert extractor.data_source == "tushare"
            assert extractor.target_table == "test_fut_daily"
            assert extractor.enabled is True
            
            # 验证TUSHARE特定属性
            assert hasattr(extractor, 'token')
            assert extractor.token == 'valid_token_123456789012345678901234567890ab'
            assert hasattr(extractor, 'pro_api')
            
            # 验证继承自SimpleExtractor
            assert isinstance(extractor, SimpleExtractor)
    
    def test_tushare_extractor_missing_token_error(self):
        """测试缺少TUSHARE_TOKEN时的错误处理 - F2.T1.S2"""
        # 使用patch绕过ConfigLoader的.env自动加载
        with patch('src.data_import.extractors.tushare_extractor.os.getenv') as mock_getenv:
            mock_getenv.return_value = None  # 模拟TUSHARE_TOKEN未设置
            
            with pytest.raises(ExtractorException) as exc_info:
                TushareExtractor(self.base_config)
            
            assert "TUSHARE_TOKEN" in str(exc_info.value)
            assert "环境变量未设置" in str(exc_info.value)
    
    def test_tushare_extractor_invalid_token_format(self):
        """测试无效Token格式检验 - F2.T1.S2"""
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'invalid_short'}):  # 过短的token
            with pytest.raises(ExtractorException) as exc_info:
                TushareExtractor(self.base_config)
            
            assert "Token格式无效" in str(exc_info.value)
    
    @patch('tushare.pro_api')
    def test_tushare_api_client_initialization(self, mock_pro_api):
        """测试TUSHARE API客户端初始化 - F2.T1.S1"""
        mock_api_client = MagicMock()
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.base_config)
            
            # 验证API客户端被正确初始化
            mock_pro_api.assert_called_once_with('valid_token_123456789012345678901234567890ab')
            assert extractor.pro_api == mock_api_client
    
    @patch('tushare.pro_api')
    def test_token_validation_method(self, mock_pro_api):
        """测试Token验证方法 - F2.T1.S2"""
        # 模拟API调用成功
        mock_api_client = MagicMock()
        mock_api_client.stock_basic.return_value = pl.DataFrame({"ts_code": ["000001.SZ"]})
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.base_config)
            
            # 验证Token验证成功
            is_valid = extractor._validate_token()
            assert is_valid is True
    
    @patch('tushare.pro_api')
    def test_token_validation_failure(self, mock_pro_api):
        """测试Token验证失败处理 - F2.T1.S2"""
        # 模拟API调用失败
        mock_api_client = MagicMock()
        mock_api_client.stock_basic.side_effect = Exception("权限不足")
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'invalid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.base_config)
            
            # 验证Token验证失败
            is_valid = extractor._validate_token()
            assert is_valid is False
    
    def test_extractor_config_validation(self):
        """测试提取器配置验证 - F2.T1.S1"""
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.base_config)
            
            validation_result = extractor.validate_config()
            
            # 验证配置验证结果
            assert validation_result["valid"] is True
            assert len(validation_result["errors"]) == 0
            
            # 验证TUSHARE特定配置检查
            assert extractor.data_source == "tushare"
    
    def test_extractor_status_information(self):
        """测试提取器状态信息 - F2.T1.S1"""
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.base_config)
            
            status = extractor.get_status()
            
            # 验证状态信息完整性
            assert status["name"] == "tushare_test_extractor"
            assert status["data_source"] == "tushare"
            assert status["target_table"] == "test_fut_daily"
            assert "stats" in status
            assert "config_summary" in status


class TestSimpleExtractorFactory:
    """SimpleExtractorFactory集成测试 - F2.T1.S3"""
    
    def setup_method(self):
        """测试设置"""
        self.tushare_config = {
            "name": "factory_tushare_extractor",
            "data_source": "tushare",
            "target_table": "fut_daily",
            "enabled": True
        }
        
        self.csv_config = {
            "name": "factory_csv_extractor", 
            "data_source": "csv",
            "target_table": "csv_fut_main_contract_kline_15min",
            "data_dir": "/tmp/test_csv"
        }
    
    def test_factory_creates_tushare_extractor(self):
        """测试工厂方法创建TUSHARE提取器 - F2.T1.S3"""
        from src.data_import.extractors.simple_extractor_factory import SimpleExtractorFactory
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = SimpleExtractorFactory.create_extractor(self.tushare_config)
            
            # 验证返回正确的提取器类型
            assert isinstance(extractor, TushareExtractor)
            assert extractor.name == "factory_tushare_extractor"
            assert extractor.data_source == "tushare"
            assert extractor.target_table == "fut_daily"
    
    def test_factory_creates_csv_extractor(self):
        """测试工厂方法创建CSV提取器（确保兼容性）- F2.T1.S3"""
        from src.data_import.extractors.simple_extractor_factory import SimpleExtractorFactory
        from src.data_import.extractors.csv_layer_extractor import CsvLayerExtractor
        import tempfile
        import os
        
        # 创建临时目录用于测试
        with tempfile.TemporaryDirectory() as temp_dir:
            test_config = self.csv_config.copy()
            test_config["data_dir"] = temp_dir
            
            extractor = SimpleExtractorFactory.create_extractor(test_config)
            
            # 验证返回正确的提取器类型
            assert isinstance(extractor, CsvLayerExtractor)
            assert extractor.name == "factory_csv_extractor"
            assert extractor.data_source == "csv"
    
    def test_factory_unsupported_data_source_error(self):
        """测试工厂方法处理不支持的数据源 - F2.T1.S3"""
        from src.data_import.extractors.simple_extractor_factory import SimpleExtractorFactory
        
        unsupported_config = {
            "name": "unsupported_extractor",
            "data_source": "unsupported_source",
            "target_table": "test_table"
        }
        
        with pytest.raises(NotImplementedError) as exc_info:
            SimpleExtractorFactory.create_extractor(unsupported_config)
        
        assert "不支持的数据源类型" in str(exc_info.value)
        assert "unsupported_source" in str(exc_info.value)
    
    def test_factory_method_is_static(self):
        """测试工厂方法是静态方法 - F2.T1.S3"""
        from src.data_import.extractors.simple_extractor_factory import SimpleExtractorFactory
        
        # 验证可以直接通过类调用，无需实例化
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = SimpleExtractorFactory.create_extractor(self.tushare_config)
            assert extractor is not None


class TestTushareExtractorDataExtraction:
    """TushareExtractor数据提取测试"""
    
    def setup_method(self):
        """测试设置"""
        self.base_config = {
            "name": "tushare_data_extractor",
            "data_source": "tushare",
            "target_table": "fut_daily",
            "enabled": True
        }
    
    @patch('tushare.pro_api')
    def test_extract_futures_daily_data(self, mock_pro_api):
        """测试期货日线数据提取 - F2.T1.S4"""
        # 模拟TUSHARE API返回的期货日线数据
        mock_api_client = MagicMock()
        mock_futures_data = pl.DataFrame({
            "ts_code": ["J2501.DCE"],
            "trade_date": ["20241201"],
            "open": [2500.0],
            "high": [2520.0], 
            "low": [2490.0],
            "close": [2510.0],
            "vol": [150000],
            "amount": [377500000.0]
        })
        mock_api_client.fut_daily.return_value = mock_futures_data
        mock_pro_api.return_value = mock_api_client
        
        # 配置期货目标表
        futures_config = self.base_config.copy()
        futures_config["target_table"] = "fut_daily"
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(futures_config)
            
            # 测试数据提取
            result = extractor.extract('auto')
            
            # 验证返回的数据格式
            assert isinstance(result, pl.DataFrame)
            assert len(result) == 1
            
            # 验证业务字段
            assert "ts_code" in result.columns
            assert "trade_date" in result.columns
            assert "open" in result.columns
            assert "close" in result.columns
            
            # 验证元数据字段
            assert "data_source" in result.columns
            assert "created_at" in result.columns
            
            # 验证数据内容
            assert result["ts_code"][0] == "J2501.DCE"
            assert result["data_source"][0] == "tushare"
    
    @patch('tushare.pro_api')
    def test_extract_stocks_daily_data(self, mock_pro_api):
        """测试股票日线数据提取 - F2.T1.S4"""
        # 模拟TUSHARE API返回的股票日线数据
        mock_api_client = MagicMock()
        mock_stocks_data = pl.DataFrame({
            "ts_code": ["000001.SZ"],
            "trade_date": ["20241201"],
            "open": [9.50],
            "high": [9.68],
            "low": [9.45],
            "close": [9.62],
            "vol": [250000],
            "amount": [2405000.0]
        })
        mock_api_client.daily.return_value = mock_stocks_data
        mock_pro_api.return_value = mock_api_client
        
        # 配置股票目标表
        stocks_config = self.base_config.copy()
        stocks_config["target_table"] = "daily"
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(stocks_config)
            
            # 测试数据提取
            result = extractor.extract('auto')
            
            # 验证返回的数据格式
            assert isinstance(result, pl.DataFrame)
            assert len(result) == 1
            
            # 验证股票特定字段
            assert result["ts_code"][0] == "000001.SZ"
            assert result["data_source"][0] == "tushare"
    
    @patch('tushare.pro_api')
    def test_extract_validate_mode(self, mock_pro_api):
        """测试验证模式不提取数据 - F2.T1.S4"""
        mock_api_client = MagicMock()
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.base_config)
            
            # 验证模式应该返回空DataFrame
            result = extractor.extract('validate')
            
            assert isinstance(result, pl.DataFrame)
            assert len(result) == 0
            
            # API不应该被调用
            mock_api_client.fut_daily.assert_not_called()
            mock_api_client.daily.assert_not_called()
    
    @patch('tushare.pro_api')
    def test_extract_unsupported_target_table_error(self, mock_pro_api):
        """测试不支持的目标表错误处理 - F2.T1.S4"""
        mock_api_client = MagicMock()
        mock_pro_api.return_value = mock_api_client
        
        # 配置不支持的目标表
        unsupported_config = self.base_config.copy()
        unsupported_config["target_table"] = "unsupported_table"
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(unsupported_config)
            
            # 应该抛出TushareApiError
            with pytest.raises(TushareApiError) as exc_info:
                extractor.extract('auto')
            
            assert "不支持的目标表类型" in str(exc_info.value)
            assert "unsupported_table" in str(exc_info.value)


class TestTushareExtractorErrors:
    """TUSHARE提取器错误和异常测试"""
    
    def test_tushare_api_error_creation(self):
        """测试TUSHARE API错误创建"""
        error = TushareApiError("API调用失败", "test_extractor", "API_ERROR")
        
        assert str(error) == "API调用失败"
        assert error.extractor_name == "test_extractor"
        assert error.error_code == "API_ERROR"
    
    def test_extractor_health_check(self):
        """测试提取器健康检查"""
        config = {
            "name": "health_test_extractor",
            "data_source": "tushare",
            "enabled": True
        }
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(config)
            
            # 健康的提取器应该返回True
            assert extractor.is_healthy() is True
    
    def test_extractor_string_representation(self):
        """测试提取器字符串表示"""
        config = {
            "name": "string_test_extractor",
            "data_source": "tushare",
            "target_table": "test_table"
        }
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(config)
            
            str_repr = str(extractor)
            assert "string_test_extractor" in str_repr
            assert "tushare" in str_repr
            assert "test_table" in str_repr


class TestTushareExtractorBudgetIntegration:
    """TushareExtractor积分预算集成测试 - F2.T2.S4"""
    
    def setup_method(self):
        """测试设置"""
        self.budget_config = {
            "name": "budget_test_extractor",
            "data_source": "tushare",
            "target_table": "fut_daily",
            "enabled": True,
            "total_points": 100  # 设置较小的预算便于测试
        }
    
    @patch('tushare.pro_api')
    def test_points_calculator_integration(self, mock_pro_api):
        """测试积分计算器集成初始化 - F2.T2.S4"""
        mock_api_client = MagicMock()
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.budget_config)
            
            # 验证积分计算器已集成
            assert extractor.points_calculator is not None
            assert isinstance(extractor.points_calculator, PointsCalculator)
            assert extractor.points_calculator.usage_stats.total_budget == 100
            assert extractor.points_calculator.usage_stats.available_points == 100
    
    @patch('tushare.pro_api')
    def test_budget_check_before_api_call(self, mock_pro_api):
        """测试API调用前的积分预算检查 - F2.T2.S4"""
        mock_api_client = MagicMock()
        mock_pro_api.return_value = mock_api_client
        
        # 创建积分不足的配置（fut_daily需要5积分，但只给3积分）
        insufficient_config = self.budget_config.copy()
        insufficient_config["total_points"] = 3
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(insufficient_config)
            
            # 应该因积分不足而抛出错误
            with pytest.raises(TushareApiError) as exc_info:
                extractor.extract('auto')
            
            assert "积分预算不足" in str(exc_info.value)
            assert "需要: 5积分" in str(exc_info.value)
            assert "可用: 3积分" in str(exc_info.value)
            assert exc_info.value.error_code == "INSUFFICIENT_POINTS"
    
    @patch('tushare.pro_api')  
    def test_points_consumption_recording(self, mock_pro_api):
        """测试积分消耗记录 - F2.T2.S4"""
        # 模拟成功的API调用
        mock_api_client = MagicMock()
        mock_futures_data = pl.DataFrame({
            "ts_code": ["J2501.DCE"],
            "trade_date": ["20241201"],
            "close": [2510.0]
        })
        mock_api_client.fut_daily.return_value = mock_futures_data
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.budget_config)
            
            # 记录调用前的积分状态
            initial_available = extractor.points_calculator.usage_stats.available_points
            initial_consumed = extractor.points_calculator.usage_stats.total_consumed
            
            # 执行数据提取
            result = extractor.extract('auto')
            
            # 验证提取成功
            assert isinstance(result, pl.DataFrame)
            assert len(result) == 1
            
            # 验证积分消耗记录
            final_available = extractor.points_calculator.usage_stats.available_points
            final_consumed = extractor.points_calculator.usage_stats.total_consumed
            
            assert final_available == initial_available - 5  # fut_daily消耗5积分
            assert final_consumed == initial_consumed + 5
            assert extractor.tushare_stats["points_consumed"] == 5
    
    @patch('tushare.pro_api')
    def test_api_name_mapping_for_target_table(self, mock_pro_api):
        """测试目标表到API名称的映射 - F2.T2.S4"""
        mock_api_client = MagicMock()
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.budget_config)
            
            # 测试不同目标表的API名称映射
            assert extractor._get_api_name_for_target_table('fut_daily') == 'fut_daily'
            assert extractor._get_api_name_for_target_table('futures_daily') == 'fut_daily'
            assert extractor._get_api_name_for_target_table('daily') == 'daily'
            assert extractor._get_api_name_for_target_table('stock_daily') == 'daily'
            assert extractor._get_api_name_for_target_table('unknown_table') == 'unknown_table'
    
    @patch('tushare.pro_api')
    def test_status_includes_budget_information(self, mock_pro_api):
        """测试状态信息包含积分预算信息 - F2.T2.S4"""
        mock_api_client = MagicMock()
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.budget_config)
            
            status = extractor.get_status()
            
            # 验证包含积分预算状态
            assert "points_budget_status" in status
            assert "budget_health" in status
            
            # 验证预算状态信息
            budget_status = status["points_budget_status"]
            assert "total_budget" in budget_status
            assert "available_points" in budget_status
            assert "total_consumed" in budget_status
            
            # 验证预算健康信息
            budget_health = status["budget_health"]
            assert "health_level" in budget_health
            assert "efficiency_level" in budget_health
    
    @patch('tushare.pro_api')
    def test_multiple_api_calls_accumulate_consumption(self, mock_pro_api):
        """测试多次API调用积分消耗累积 - F2.T2.S4"""
        # 模拟成功的API调用
        mock_api_client = MagicMock()
        mock_data = pl.DataFrame({"ts_code": ["TEST"], "close": [100.0]})
        mock_api_client.fut_daily.return_value = mock_data
        mock_api_client.daily.return_value = mock_data
        mock_pro_api.return_value = mock_api_client
        
        # 设置较大的预算支持多次调用
        large_budget_config = self.budget_config.copy()
        large_budget_config["total_points"] = 50
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            # 测试期货数据提取
            futures_extractor = TushareExtractor(large_budget_config)
            futures_extractor.extract('auto')
            
            # 测试股票数据提取
            stock_config = large_budget_config.copy()
            stock_config["target_table"] = "daily"
            stock_extractor = TushareExtractor(stock_config)
            stock_extractor.extract('auto')
            
            # 验证期货提取器的积分消耗
            assert futures_extractor.points_calculator.usage_stats.total_consumed == 5
            assert futures_extractor.tushare_stats["points_consumed"] == 5
            
            # 验证股票提取器的积分消耗
            assert stock_extractor.points_calculator.usage_stats.total_consumed == 1
            assert stock_extractor.tushare_stats["points_consumed"] == 1


class TestTushareExtractorBatchOptimization:
    """TushareExtractor数据批量获取优化测试 - F2.T1.S5"""
    
    def setup_method(self):
        """测试设置"""
        self.batch_config = {
            "name": "batch_test_extractor",
            "data_source": "tushare",
            "target_table": "fut_daily",
            "enabled": True,
            "batch_size": 1000,
            "date_range": {
                "start_date": "20241201",
                "end_date": "20241203"
            },
            "symbols": ["J2501.DCE", "M2501.DCE"]
        }
    
    @patch('tushare.pro_api')
    def test_batch_extraction_with_date_range(self, mock_pro_api):
        """测试按日期范围批量提取 - F2.T1.S5"""
        mock_api_client = MagicMock()
        
        # 模拟多日期的期货数据
        mock_futures_data = pl.DataFrame({
            "ts_code": ["J2501.DCE", "J2501.DCE", "M2501.DCE"],
            "trade_date": ["20241201", "20241202", "20241201"],
            "open": [2500.0, 2510.0, 3200.0],
            "close": [2510.0, 2520.0, 3210.0],
            "vol": [150000, 155000, 120000]
        })
        mock_api_client.fut_daily.return_value = mock_futures_data
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.batch_config)
            
            # 执行批量提取
            result = extractor.extract('auto')
            
            # 验证返回数据
            assert isinstance(result, pl.DataFrame)
            assert len(result) == 3
            
            # 验证API调用时使用了正确的参数
            mock_api_client.fut_daily.assert_called_once()
            call_kwargs = mock_api_client.fut_daily.call_args[1]
            assert call_kwargs.get('start_date') == "20241201"
            assert call_kwargs.get('end_date') == "20241203"
    
    @patch('tushare.pro_api')
    def test_batch_extraction_with_symbols_filter(self, mock_pro_api):
        """测试按合约代码批量提取 - F2.T1.S5"""
        mock_api_client = MagicMock()
        mock_futures_data = pl.DataFrame({
            "ts_code": ["J2501.DCE", "M2501.DCE"],
            "trade_date": ["20241201", "20241201"],
            "close": [2510.0, 3210.0]
        })
        mock_api_client.fut_daily.return_value = mock_futures_data
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.batch_config)
            result = extractor.extract('auto')
            
            # 验证结果包含指定合约
            assert len(result) == 2
            symbols_in_result = result["ts_code"].to_list()
            assert "J2501.DCE" in symbols_in_result
            assert "M2501.DCE" in symbols_in_result
    
    @patch('tushare.pro_api')
    def test_batch_size_limit_respected(self, mock_pro_api):
        """测试批量大小限制被正确应用 - F2.T1.S5"""
        mock_api_client = MagicMock()
        mock_futures_data = pl.DataFrame({
            "ts_code": ["J2501.DCE"] * 500,
            "trade_date": ["20241201"] * 500,
            "close": [2510.0] * 500
        })
        mock_api_client.fut_daily.return_value = mock_futures_data
        mock_pro_api.return_value = mock_api_client
        
        # 配置较小的批量大小
        small_batch_config = self.batch_config.copy()
        small_batch_config["batch_size"] = 500
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(small_batch_config)
            result = extractor.extract('auto')
            
            # 验证API调用时使用了正确的limit参数
            mock_api_client.fut_daily.assert_called_once()
            call_kwargs = mock_api_client.fut_daily.call_args[1]
            assert call_kwargs.get('limit') == 500
    
    @patch('tushare.pro_api')
    def test_stock_batch_extraction_with_symbols(self, mock_pro_api):
        """测试股票数据按代码批量提取 - F2.T1.S5"""
        mock_api_client = MagicMock()
        mock_stock_data = pl.DataFrame({
            "ts_code": ["000001.SZ", "000002.SZ"],
            "trade_date": ["20241201", "20241201"],
            "close": [9.62, 15.80]
        })
        mock_api_client.daily.return_value = mock_stock_data
        mock_pro_api.return_value = mock_api_client
        
        # 股票批量配置
        stock_batch_config = {
            "name": "stock_batch_extractor",
            "data_source": "tushare",
            "target_table": "daily",
            "enabled": True,
            "symbols": ["000001.SZ", "000002.SZ"],
            "date_range": {
                "start_date": "20241201",
                "end_date": "20241201"
            }
        }
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(stock_batch_config)
            result = extractor.extract('auto')
            
            # 验证股票数据正确返回
            assert len(result) == 2
            symbols_in_result = result["ts_code"].to_list()
            assert "000001.SZ" in symbols_in_result
            assert "000002.SZ" in symbols_in_result
    
    @patch('tushare.pro_api')
    def test_batch_extraction_parameter_validation(self, mock_pro_api):
        """测试批量提取参数验证 - F2.T1.S5"""
        mock_api_client = MagicMock()
        mock_pro_api.return_value = mock_api_client
        
        # 测试无效日期范围配置
        invalid_config = self.batch_config.copy()
        invalid_config["date_range"] = {
            "start_date": "20241203",  # 开始日期晚于结束日期
            "end_date": "20241201"
        }
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(invalid_config)
            
            # 应该抛出参数验证错误
            with pytest.raises(TushareApiError) as exc_info:
                extractor.extract('auto')
            
            assert "日期范围无效" in str(exc_info.value)
    
    @patch('tushare.pro_api')
    def test_batch_extraction_empty_symbols_handling(self, mock_pro_api):
        """测试空合约列表处理 - F2.T1.S5"""
        mock_api_client = MagicMock()
        mock_api_client.fut_daily.return_value = pl.DataFrame()
        mock_pro_api.return_value = mock_api_client
        
        # 空合约列表配置
        empty_symbols_config = self.batch_config.copy()
        empty_symbols_config["symbols"] = []
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(empty_symbols_config)
            result = extractor.extract('auto')
            
            # 空合约列表应该使用默认查询（不带ts_code参数）
            assert isinstance(result, pl.DataFrame)
            mock_api_client.fut_daily.assert_called_once()
            call_kwargs = mock_api_client.fut_daily.call_args[1]
            assert 'ts_code' not in call_kwargs or call_kwargs.get('ts_code') is None


class TestTushareExtractorFuturesBasicInfo:
    """TushareExtractor期货基础信息采集测试 - F2.T3.S1"""
    
    def setup_method(self):
        """测试设置"""
        self.futures_basic_config = {
            "name": "futures_basic_extractor",
            "data_source": "tushare", 
            "target_table": "fut_basic",
            "enabled": True,
            "symbols": ["J", "M", "RB"]  # 期货品种代码
        }
    
    @patch('tushare.pro_api')
    def test_extract_futures_basic_info(self, mock_pro_api):
        """测试期货基础信息提取 - F2.T3.S1"""
        mock_api_client = MagicMock()
        
        # 模拟TUSHARE API返回的期货基础信息数据
        mock_futures_basic_data = pl.DataFrame({
            "ts_code": ["J2501.DCE", "M2501.DCE", "RB2501.SHF"],
            "symbol": ["J", "M", "RB"],
            "name": ["焦炭主力", "豆粕主力", "螺纹钢主力"],
            "fut_code": ["J", "M", "RB"],
            "trade_market": ["DCE", "DCE", "SHF"],
            "list_date": ["20150316", "20000717", "20091027"],
            "delist_date": ["", "", ""],
            "trade_unit": ["100吨/手", "10吨/手", "10吨/手"],
            "per_unit": [100, 10, 10],
            "quote_unit": ["元/吨", "元/吨", "元/吨"],
            "quote_unit_desc": ["人民币元/吨", "人民币元/吨", "人民币元/吨"],
            "d_mode_desc": ["实物交割", "实物交割", "实物交割"],
            "trade_time_desc": ["上午09:00-10:15,10:30-11:30,下午13:30-15:00,夜盘21:00-23:00", 
                               "上午09:00-10:15,10:30-11:30,下午13:30-15:00,夜盘21:00-23:00",
                               "上午09:00-10:15,10:30-11:30,下午13:30-15:00,夜盘21:00-23:00"]
        })
        mock_api_client.fut_basic.return_value = mock_futures_basic_data
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.futures_basic_config)
            
            # 执行期货基础信息提取
            result = extractor.extract('auto')
            
            # 验证返回的数据格式
            assert isinstance(result, pl.DataFrame)
            assert len(result) == 3
            
            # 验证期货基础信息字段
            assert "ts_code" in result.columns
            assert "symbol" in result.columns
            assert "name" in result.columns
            assert "trade_market" in result.columns
            assert "trade_unit" in result.columns
            
            # 验证元数据字段
            assert "data_source" in result.columns
            assert "created_at" in result.columns
            
            # 验证数据内容
            symbols_in_result = result["symbol"].to_list()
            assert "J" in symbols_in_result
            assert "M" in symbols_in_result
            assert "RB" in symbols_in_result
            assert result["data_source"][0] == "tushare"
    
    @patch('tushare.pro_api')
    def test_futures_basic_with_exchange_filter(self, mock_pro_api):
        """测试按交易所过滤期货基础信息 - F2.T3.S1"""
        mock_api_client = MagicMock()
        mock_dce_data = pl.DataFrame({
            "ts_code": ["J2501.DCE", "M2501.DCE"],
            "symbol": ["J", "M"],
            "trade_market": ["DCE", "DCE"],
            "name": ["焦炭主力", "豆粕主力"]
        })
        mock_api_client.fut_basic.return_value = mock_dce_data
        mock_pro_api.return_value = mock_api_client
        
        # 配置大商所期货
        dce_config = self.futures_basic_config.copy()
        dce_config["exchange"] = "DCE"
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(dce_config)
            result = extractor.extract('auto')
            
            # 验证结果只包含大商所期货
            assert len(result) == 2
            exchanges = result["trade_market"].unique().to_list()
            assert exchanges == ["DCE"]
    
    @patch('tushare.pro_api')
    def test_futures_basic_api_parameters(self, mock_pro_api):
        """测试期货基础信息API参数构建 - F2.T3.S1"""
        mock_api_client = MagicMock()
        mock_api_client.fut_basic.return_value = pl.DataFrame()
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.futures_basic_config)
            result = extractor.extract('auto')
            
            # 验证API调用参数
            mock_api_client.fut_basic.assert_called_once()
            call_kwargs = mock_api_client.fut_basic.call_args[1]
            
            # 应该包含期货品种参数
            assert 'fut_type' in call_kwargs or 'exchange' in call_kwargs or 'limit' in call_kwargs
    
    @patch('tushare.pro_api')
    def test_futures_basic_points_consumption(self, mock_pro_api):
        """测试期货基础信息积分消耗 - F2.T3.S1"""
        mock_api_client = MagicMock()
        mock_data = pl.DataFrame({
            "ts_code": ["J2501.DCE"],
            "symbol": ["J"],
            "name": ["焦炭主力"]
        })
        mock_api_client.fut_basic.return_value = mock_data
        mock_pro_api.return_value = mock_api_client
        
        budget_config = self.futures_basic_config.copy()
        budget_config["total_points"] = 50
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(budget_config)
            
            # 记录调用前积分
            initial_points = extractor.points_calculator.usage_stats.available_points
            
            result = extractor.extract('auto')
            
            # 验证积分消耗（fut_basic消耗1积分）
            final_points = extractor.points_calculator.usage_stats.available_points
            assert final_points == initial_points - 1
            assert extractor.tushare_stats["points_consumed"] == 1


class TestTushareExtractorFuturesEnhanced:
    """TushareExtractor期货日线数据增强采集测试 - F2.T3.S2"""
    
    def setup_method(self):
        """测试设置"""
        self.enhanced_config = {
            "name": "futures_enhanced_extractor",
            "data_source": "tushare",
            "target_table": "fut_daily", 
            "enabled": True,
            "symbols": ["J2501.DCE", "M2501.DCE"],
            "date_range": {
                "start_date": "20241201",
                "end_date": "20241203"
            },
            "include_settlement": True,  # 包含结算价
            "include_position": True,    # 包含持仓量
            "main_contract_only": False  # 是否只获取主力合约
        }
    
    @patch('tushare.pro_api')
    def test_enhanced_futures_daily_with_settlement(self, mock_pro_api):
        """测试增强期货日线数据（含结算价）- F2.T3.S2"""
        mock_api_client = MagicMock()
        
        # 模拟包含结算价的期货日线数据
        mock_enhanced_data = pl.DataFrame({
            "ts_code": ["J2501.DCE", "M2501.DCE"],
            "trade_date": ["20241201", "20241201"],
            "open": [2500.0, 3200.0],
            "high": [2520.0, 3220.0], 
            "low": [2490.0, 3180.0],
            "close": [2510.0, 3210.0],
            "settle": [2508.0, 3205.0],  # 结算价
            "vol": [150000, 120000],
            "amount": [377500000.0, 384600000.0],
            "oi": [45000, 32000],  # 持仓量
            "oi_chg": [1200, -800]  # 持仓量变化
        })
        mock_api_client.fut_daily.return_value = mock_enhanced_data
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.enhanced_config)
            result = extractor.extract('auto')
            
            # 验证包含增强字段
            assert isinstance(result, pl.DataFrame)
            assert len(result) == 2
            assert "settle" in result.columns or "close" in result.columns  # 至少包含收盘价
            assert "oi" in result.columns or "vol" in result.columns  # 至少包含成交量
    
    @patch('tushare.pro_api')
    def test_main_contract_filtering(self, mock_pro_api):
        """测试主力合约过滤功能 - F2.T3.S2"""
        mock_api_client = MagicMock()
        
        # 模拟主力合约数据
        mock_main_contract_data = pl.DataFrame({
            "ts_code": ["J2501.DCE", "M2501.DCE"],  # 主力合约
            "trade_date": ["20241201", "20241201"],
            "close": [2510.0, 3210.0],
            "vol": [150000, 120000],
            "oi": [45000, 32000]  # 持仓量大，为主力合约特征
        })
        mock_api_client.fut_daily.return_value = mock_main_contract_data
        mock_pro_api.return_value = mock_api_client
        
        # 配置只获取主力合约
        main_config = self.enhanced_config.copy()
        main_config["main_contract_only"] = True
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(main_config)
            result = extractor.extract('auto')
            
            # 验证主力合约数据
            assert isinstance(result, pl.DataFrame)
            assert len(result) >= 1  # 至少有主力合约数据
            
            # 验证API参数包含主力合约标识
            mock_api_client.fut_daily.assert_called_once()
            call_kwargs = mock_api_client.fut_daily.call_args[1]
            # 主力合约逻辑可能通过后处理实现，这里验证基础调用成功
            assert 'limit' in call_kwargs
    
    @patch('tushare.pro_api')
    def test_futures_data_enrichment(self, mock_pro_api):
        """测试期货数据增强处理 - F2.T3.S2"""
        mock_api_client = MagicMock()
        mock_enriched_data = pl.DataFrame({
            "ts_code": ["J2501.DCE"],
            "trade_date": ["20241201"],
            "open": [2500.0],
            "close": [2510.0],
            "vol": [150000],
            "amount": [377500000.0],
            "oi": [45000]
        })
        mock_api_client.fut_daily.return_value = mock_enriched_data
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.enhanced_config)
            result = extractor.extract('auto')
            
            # 验证数据增强
            assert isinstance(result, pl.DataFrame)
            assert len(result) == 1
            
            # 验证包含期货特有字段
            columns = result.columns
            futures_specific_fields = ['ts_code', 'vol', 'amount']
            for field in futures_specific_fields:
                assert field in columns
    
    @patch('tushare.pro_api')
    def test_enhanced_error_handling(self, mock_pro_api):
        """测试增强期货数据的错误处理 - F2.T3.S2"""
        mock_api_client = MagicMock()
        # 模拟API调用失败
        mock_api_client.fut_daily.side_effect = Exception("API调用失败")
        mock_pro_api.return_value = mock_api_client
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(self.enhanced_config)
            
            # 应该抛出合适的错误
            with pytest.raises(TushareApiError) as exc_info:
                extractor.extract('auto')
            
            assert "期货日线数据获取失败" in str(exc_info.value)
            assert exc_info.value.error_code == "FUTURES_DAILY_FAILED"
    
    @patch('tushare.pro_api') 
    def test_enhanced_api_parameter_building(self, mock_pro_api):
        """测试增强API参数构建 - F2.T3.S2"""
        mock_api_client = MagicMock()
        mock_api_client.fut_daily.return_value = pl.DataFrame()
        mock_pro_api.return_value = mock_api_client
        
        # 包含多种增强参数的配置
        complex_config = {
            "name": "complex_futures_extractor",
            "data_source": "tushare",
            "target_table": "fut_daily",
            "symbols": ["J2501.DCE", "M2501.DCE", "RB2501.SHF"],
            "date_range": {
                "start_date": "20241201", 
                "end_date": "20241203"
            },
            "batch_size": 2000,
            "exchange": "DCE"
        }
        
        with patch.dict(os.environ, {'TUSHARE_TOKEN': 'valid_token_123456789012345678901234567890ab'}):
            extractor = TushareExtractor(complex_config)
            result = extractor.extract('auto')
            
            # 验证API调用参数
            mock_api_client.fut_daily.assert_called_once()
            call_kwargs = mock_api_client.fut_daily.call_args[1]
            
            # 验证各种参数都被正确传递
            assert call_kwargs.get('limit') == 2000
            assert call_kwargs.get('start_date') == "20241201"
            assert call_kwargs.get('end_date') == "20241203" 
            assert 'ts_code' in call_kwargs  # 合约代码参数