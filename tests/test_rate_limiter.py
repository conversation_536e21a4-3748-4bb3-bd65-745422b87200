#!/usr/bin/env python3
"""
频率限制控制器测试模块
针对TUSHARE API频率控制的TDD测试

测试覆盖：
- F2.T2.S2: RateLimiter频率限制控制器
- 滑动窗口算法 (200次/分钟)
- 并发请求控制 (≤2个并发)
- 请求队列管理
"""

import pytest
import asyncio
import time
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

from src.tushare.rate_limiter import RateLimiter, RequestWindow, ConcurrencyController


class TestRateLimiter:
    """频率限制控制器测试类 - F2.T2.S2"""
    
    def setup_method(self):
        """测试设置"""
        # 使用较小的限制便于测试
        self.limiter = RateLimiter(
            max_requests_per_minute=10,  # 每分钟10次
            max_concurrent_requests=2,   # 最大2个并发
            window_size_seconds=60       # 60秒窗口
        )
    
    def test_rate_limiter_initialization(self):
        """测试频率限制器初始化 - F2.T2.S2"""
        assert self.limiter.max_requests_per_minute == 10
        assert self.limiter.max_concurrent_requests == 2
        assert self.limiter.window_size_seconds == 60
        assert hasattr(self.limiter, 'request_window')
        assert hasattr(self.limiter, 'concurrency_controller')
    
    def test_can_make_request_within_limit(self):
        """测试限制内可以发起请求 - F2.T2.S2"""
        # 初始状态应该可以发起请求
        assert self.limiter.can_make_request() is True
        
        # 记录几次请求，仍在限制内
        for i in range(5):
            self.limiter.record_request()
            assert self.limiter.can_make_request() is True
    
    def test_cannot_make_request_when_limit_exceeded(self):
        """测试超过限制时不能发起请求 - F2.T2.S2"""
        # 记录到达限制
        for i in range(10):
            self.limiter.record_request()
        
        # 现在应该被限制
        assert self.limiter.can_make_request() is False
    
    def test_sliding_window_behavior(self):
        """测试滑动窗口行为 - F2.T2.S2"""
        current_time = time.time()
        
        # 在当前时间记录请求
        with patch('time.time', return_value=current_time):
            for i in range(5):
                self.limiter.record_request()
        
        # 30秒后，部分请求应该仍在窗口内
        with patch('time.time', return_value=current_time + 30):
            assert self.limiter.can_make_request() is False  # 仍有5个请求在窗口内
        
        # 70秒后，所有请求都应该超出窗口
        with patch('time.time', return_value=current_time + 70):
            assert self.limiter.can_make_request() is True
    
    def test_concurrent_request_limit(self):
        """测试并发请求限制 - F2.T2.S2"""
        # 开始两个并发请求
        token1 = self.limiter.acquire_concurrent_slot()
        assert token1 is not None
        
        token2 = self.limiter.acquire_concurrent_slot()
        assert token2 is not None
        
        # 第三个请求应该被拒绝
        token3 = self.limiter.acquire_concurrent_slot()
        assert token3 is None
        
        # 释放一个槽位后应该可以获取
        self.limiter.release_concurrent_slot(token1)
        token4 = self.limiter.acquire_concurrent_slot()
        assert token4 is not None
    
    def test_wait_time_calculation(self):
        """测试等待时间计算 - F2.T2.S2"""
        current_time = time.time()
        
        # 填满请求限制
        with patch('time.time', return_value=current_time):
            for i in range(10):
                self.limiter.record_request()
        
        # 计算需要等待的时间
        with patch('time.time', return_value=current_time + 10):
            wait_time = self.limiter.get_wait_time()
            # 应该需要等待约50秒（60-10）
            assert 45 <= wait_time <= 55
    
    def test_get_current_stats(self):
        """测试获取当前统计信息 - F2.T2.S2"""
        # 记录一些请求
        for i in range(3):
            self.limiter.record_request()
        
        stats = self.limiter.get_current_stats()
        
        # 验证统计信息
        assert stats['requests_in_window'] == 3
        assert stats['max_requests_per_minute'] == 10
        assert stats['concurrent_requests'] == 0
        assert stats['max_concurrent_requests'] == 2
        assert 'remaining_requests' in stats
        assert 'can_make_request' in stats
    
    async def test_async_wait_for_available_slot(self):
        """测试异步等待可用槽位 - F2.T2.S2"""
        # 填满请求限制
        for i in range(10):
            self.limiter.record_request()
        
        # 模拟短暂等待后限制解除
        async def mock_wait():
            await asyncio.sleep(0.1)  # 短暂等待
            self.limiter.request_window.requests.clear()  # 清除请求历史
        
        # 启动等待任务
        wait_task = asyncio.create_task(mock_wait())
        
        # 等待可用槽位（应该很快返回）
        start_time = time.time()
        await self.limiter.wait_for_available_slot(max_wait_seconds=1)
        end_time = time.time()
        
        # 验证等待时间合理
        assert end_time - start_time < 0.5
        
        await wait_task


class TestRequestWindow:
    """请求窗口测试类"""
    
    def setup_method(self):
        """测试设置"""
        self.window = RequestWindow(window_size_seconds=60)
    
    def test_request_window_initialization(self):
        """测试请求窗口初始化"""
        assert self.window.window_size_seconds == 60
        assert len(self.window.requests) == 0
    
    def test_add_request_to_window(self):
        """测试添加请求到窗口"""
        current_time = time.time()
        
        with patch('time.time', return_value=current_time):
            self.window.add_request()
        
        assert len(self.window.requests) == 1
        assert self.window.requests[0] == current_time
    
    def test_cleanup_old_requests(self):
        """测试清理过期请求"""
        current_time = time.time()
        
        # 添加一些旧请求
        old_time = current_time - 70  # 70秒前
        self.window.requests.extend([old_time, old_time + 10, current_time - 10])
        
        with patch('time.time', return_value=current_time):
            self.window.cleanup_old_requests()
        
        # 只有30秒内的请求应该保留
        assert len(self.window.requests) == 1
        assert self.window.requests[0] == current_time - 10
    
    def test_get_request_count_in_window(self):
        """测试获取窗口内请求数量"""
        current_time = time.time()
        
        # 添加不同时间的请求
        self.window.requests = [
            current_time - 70,  # 超出窗口
            current_time - 30,  # 在窗口内
            current_time - 10,  # 在窗口内
            current_time        # 当前时间
        ]
        
        with patch('time.time', return_value=current_time):
            count = self.window.get_request_count()
        
        # 应该只计算窗口内的3个请求
        assert count == 3


class TestConcurrencyController:
    """并发控制器测试类"""
    
    def setup_method(self):
        """测试设置"""
        self.controller = ConcurrencyController(max_concurrent=2)
    
    def test_concurrency_controller_initialization(self):
        """测试并发控制器初始化"""
        assert self.controller.max_concurrent == 2
        assert self.controller.current_concurrent == 0
        assert len(self.controller.active_tokens) == 0
    
    def test_acquire_and_release_slots(self):
        """测试获取和释放并发槽位"""
        # 获取第一个槽位
        token1 = self.controller.acquire_slot()
        assert token1 is not None
        assert self.controller.current_concurrent == 1
        
        # 获取第二个槽位
        token2 = self.controller.acquire_slot()
        assert token2 is not None
        assert self.controller.current_concurrent == 2
        
        # 第三个槽位应该失败
        token3 = self.controller.acquire_slot()
        assert token3 is None
        assert self.controller.current_concurrent == 2
        
        # 释放第一个槽位
        success = self.controller.release_slot(token1)
        assert success is True
        assert self.controller.current_concurrent == 1
        
        # 现在应该可以获取新槽位
        token4 = self.controller.acquire_slot()
        assert token4 is not None
        assert self.controller.current_concurrent == 2
    
    def test_release_invalid_token(self):
        """测试释放无效token"""
        # 尝试释放不存在的token
        success = self.controller.release_slot("invalid_token")
        assert success is False
        assert self.controller.current_concurrent == 0
    
    def test_get_available_slots(self):
        """测试获取可用槽位数"""
        # 初始状态
        assert self.controller.get_available_slots() == 2
        
        # 获取一个槽位
        token1 = self.controller.acquire_slot()
        assert self.controller.get_available_slots() == 1
        
        # 获取第二个槽位
        token2 = self.controller.acquire_slot()
        assert self.controller.get_available_slots() == 0
        
        # 释放一个槽位
        self.controller.release_slot(token1)
        assert self.controller.get_available_slots() == 1