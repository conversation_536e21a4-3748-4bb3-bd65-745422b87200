#!/usr/bin/env python3
"""
CSV测试数据工厂
提供统一的测试数据生成功能，消除测试代码重复
"""

import tempfile
import pandas as pd
from pathlib import Path
from typing import Dict, Any
import csv


class CSVTestDataFactory:
    """CSV测试数据工厂"""
    
    @staticmethod
    def create_futures_15min_data() -> pd.DataFrame:
        """创建期货15分钟K线测试数据"""
        return pd.DataFrame({
            'datetime': ['2024-01-01 09:00:00', '2024-01-01 09:15:00', '2024-01-01 09:30:00'],
            'open': [3800.0, 3805.0, 3810.0],
            'high': [3810.0, 3815.0, 3820.0], 
            'low': [3795.0, 3800.0, 3805.0],
            'close': [3805.0, 3810.0, 3815.0],
            'volume': [1000, 1200, 1100],
            'amount': [3800000.0, 4566000.0, 4196500.0],
            'open_interest': [50000, 50500, 51000]
        })
    
    @staticmethod
    def create_stock_daily_data() -> pd.DataFrame:
        """创建股票日K线测试数据"""
        return pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'symbol': ['000001', '000001', '000001'],
            'open': [10.50, 10.60, 10.70],
            'high': [10.80, 10.90, 11.00],
            'low': [10.40, 10.55, 10.65], 
            'close': [10.75, 10.85, 10.95],
            'volume': [1000000, 1200000, 1100000],
            'amount': [10750000.0, 13020000.0, 12045000.0]
        })
    
    @staticmethod
    def create_futures_basic_data() -> pd.DataFrame:
        """创建期货基础信息测试数据"""
        return pd.DataFrame({
            'product_code': ['AL', 'AG', 'RB'],
            'product_name': ['沪铝', '沪银', '螺纹钢'],
            'exchange': ['SHFE', 'SHFE', 'SHFE'],
            'contract_size': [5, 15, 10],
            'tick_size': [5.0, 1.0, 1.0],
            'trading_unit': ['吨', '千克', '吨']
        })
    
    @staticmethod
    def create_stock_basic_data() -> pd.DataFrame:
        """创建股票基础信息测试数据"""
        return pd.DataFrame({
            'symbol': ['000001', '000002', '600000'],
            'name': ['平安银行', '万科A', '浦发银行'],
            'market': ['SZSE', 'SZSE', 'SSE'],
            'industry': ['银行', '房地产', '银行'],
            'list_date': ['1991-04-03', '1991-01-29', '1999-11-10']
        })
    
    @classmethod
    def create_temp_csv_file(cls, data: pd.DataFrame, filename: str, 
                           encoding: str = 'utf-8', delimiter: str = ',') -> Path:
        """
        创建临时CSV文件
        
        Args:
            data: 数据DataFrame
            filename: 文件名
            encoding: 编码格式
            delimiter: 分隔符
            
        Returns:
            Path: 临时文件路径
        """
        temp_file = tempfile.NamedTemporaryFile(
            mode='w', suffix='.csv', delete=False, 
            encoding=encoding, newline=''
        )
        
        # 使用指定的文件名
        temp_path = Path(temp_file.name)
        target_path = temp_path.parent / filename
        
        # 写入数据
        data.to_csv(temp_file, index=False, sep=delimiter)
        temp_file.close()
        
        # 重命名文件
        temp_path.rename(target_path)
        
        return target_path
    
    @classmethod
    def create_test_csv_files(cls, base_dir: Path = None) -> Dict[str, Path]:
        """
        创建完整的测试CSV文件集合
        
        Args:
            base_dir: 基础目录，如果不指定则使用临时目录
            
        Returns:
            Dict[str, Path]: 文件名到路径的映射
        """
        if base_dir is None:
            base_dir = Path(tempfile.mkdtemp())
        
        files = {}
        
        # 期货15分钟K线文件
        futures_files = [
            'al_主力合约_15分钟数据.csv',
            'ag_主力合约_15分钟数据.csv',
            'rb2501_主力合约_15分钟.csv'
        ]
        
        futures_data = cls.create_futures_15min_data()
        for filename in futures_files:
            file_path = base_dir / filename
            futures_data.to_csv(file_path, index=False, encoding='utf-8')
            files[filename] = file_path
        
        # 股票日K线文件
        stock_files = [
            '000001_股票日K线.csv',
            'stock_daily_data.csv'
        ]
        
        stock_data = cls.create_stock_daily_data()
        for filename in stock_files:
            file_path = base_dir / filename
            stock_data.to_csv(file_path, index=False, encoding='utf-8')
            files[filename] = file_path
        
        # 基础信息文件
        info_files = [
            ('期货基础信息.csv', cls.create_futures_basic_data()),
            ('股票基础列表.csv', cls.create_stock_basic_data())
        ]
        
        for filename, data in info_files:
            file_path = base_dir / filename
            data.to_csv(file_path, index=False, encoding='utf-8')
            files[filename] = file_path
        
        return files
    
    @staticmethod
    def create_invalid_csv_data() -> Dict[str, Any]:
        """创建无效的CSV测试数据"""
        return {
            'negative_prices': pd.DataFrame({
                'datetime': ['2024-01-01 09:00:00'],
                'open': [-100.0],  # 负价格
                'high': [3810.0],
                'low': [3795.0],
                'close': [3805.0],
                'volume': [1000],
                'amount': [3800000.0],
                'open_interest': [50000]
            }),
            'invalid_contract_code': pd.DataFrame({
                'contract_code': ['INVALID123'],  # 无效合约代码格式
                'datetime': ['2024-01-01 09:00:00'],
                'open': [3800.0],
                'high': [3810.0],
                'low': [3795.0],
                'close': [3805.0],
                'volume': [1000],
                'amount': [3800000.0],
                'open_interest': [50000]
            }),
            'missing_columns': pd.DataFrame({
                'datetime': ['2024-01-01 09:00:00'],
                'open': [3800.0]
                # 缺少其他必需字段
            })
        }
    
    @staticmethod
    def cleanup_test_files(file_paths: list):
        """清理测试文件"""
        for file_path in file_paths:
            if isinstance(file_path, (str, Path)):
                path_obj = Path(file_path)
                if path_obj.exists():
                    path_obj.unlink()
    
    @staticmethod
    def create_malformed_csv_file(filename: str) -> Path:
        """创建格式错误的CSV文件"""
        temp_file = tempfile.NamedTemporaryFile(
            mode='w', suffix='.csv', delete=False, 
            encoding='utf-8', newline=''
        )
        
        # 写入格式错误的内容
        temp_file.write("header1,header2\n")
        temp_file.write("value1,value2,value3\n")  # 列数不匹配
        temp_file.write("value4\n")  # 列数不匹配
        temp_file.close()
        
        # 重命名文件
        temp_path = Path(temp_file.name)
        target_path = temp_path.parent / filename
        temp_path.rename(target_path)
        
        return target_path