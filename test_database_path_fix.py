#!/usr/bin/env python3
"""
测试数据库路径修复
"""

import sys
import os
from pathlib import Path
sys.path.insert(0, 'src')

def test_database_path():
    print("🔧 测试数据库路径修复")
    print("=" * 50)
    
    try:
        from utils.config_loader_v2 import ConfigLoaderV2
        
        # 重新加载配置
        config = ConfigLoaderV2()
        dev_config = config.get_config('dev')
        
        print("1. 数据库配置检查:")
        if 'database' in dev_config:
            db_config = dev_config['database']
            db_path = db_config.get('path', '未设置')
            print(f"   数据库路径: {db_path}")
            
            # 检查占位符是否被替换
            if '{' in str(db_path):
                print("❌ 占位符未被替换!")
                print(f"   原始路径: {db_path}")
            else:
                print("✅ 占位符已正确替换")
                
                # 检查路径格式
                expected_pattern = "D:/Data/duckdb/AQUA/DataCenter/aqua_dev.duckdb"
                if "D:/Data/duckdb/AQUA/DataCenter" in db_path:
                    print("✅ 路径格式正确")
                    print(f"   完整路径: {db_path}")
                    
                    # 检查目录是否存在
                    db_dir = Path(db_path).parent
                    print(f"   数据库目录: {db_dir}")
                    print(f"   目录存在: {db_dir.exists()}")
                    
                    if not db_dir.exists():
                        print("📁 创建数据库目录...")
                        db_dir.mkdir(parents=True, exist_ok=True)
                        print(f"✅ 目录创建成功: {db_dir}")
                    
                    # 检查数据库文件
                    if Path(db_path).exists():
                        print(f"✅ 数据库文件存在: {db_path}")
                        print(f"📁 文件大小: {Path(db_path).stat().st_size} bytes")
                    else:
                        print(f"ℹ️ 数据库文件不存在，这是正常的（首次运行）")
                        
                else:
                    print("❌ 路径格式不正确")
                    print(f"   期望包含: D:/Data/duckdb/AQUA/DataCenter")
                    print(f"   实际路径: {db_path}")
        else:
            print("❌ 数据库配置不存在")
        
        print(f"\n2. 平台信息:")
        platform_info = config.get_platform_info()
        print(f"   系统: {platform_info['system']}")
        print(f"   Windows: {platform_info.get('is_windows', False)}")
        
        print(f"\n3. 错误数据库检查:")
        wrong_db = Path('data/aqua.duckdb')
        if wrong_db.exists():
            print(f"⚠️ 发现错误位置的数据库: {wrong_db}")
            print(f"📁 大小: {wrong_db.stat().st_size} bytes")
            print("❌ 建议删除此文件，使用正确路径的数据库")
        else:
            print("✅ 未发现错误位置的数据库文件")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_database_path()
    print(f"\n{'✅ 修复成功' if success else '❌ 修复失败'}")
