#!/usr/bin/env python3
"""
配置加载器模块
提供统一的配置文件加载和管理功能

特性：
- 基于TOML格式的配置文件
- 多环境配置支持
- 类型安全的配置访问
- 配置验证和默认值
- 环境变量覆盖支持
"""

import os
import logging
import platform
from pathlib import Path
from typing import Dict, Optional, Union

import toml
from dotenv import load_dotenv


class ConfigLoader:
    """配置加载器"""

    def __init__(self, config_path: Union[str, Path] = "config/settings.toml"):
        """
        初始化配置加载器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        dotenv_path = self.config_path.parent / ".env"
        load_dotenv(dotenv_path=dotenv_path)  # 在初始化时从config目录加载.env文件
        self.logger = logging.getLogger(__name__)
        self._config_cache: Optional[Dict] = None

        # 验证配置文件存在
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")

    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                config = toml.load(f)

            self.logger.info(f"成功加载配置文件: {self.config_path}")
            return config

        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise

    def get_config(self, environment: str = "test") -> Dict:
        """
        获取指定环境的配置

        Args:
            environment: 环境名称

        Returns:
            Dict: 环境配置
        """
        if self._config_cache is None:
            self._config_cache = self._load_config()

        if environment not in self._config_cache:
            raise KeyError(f"环境配置不存在: {environment}")

        # 合并全局配置和环境特定配置
        config = {}

        # 首先添加全局配置
        for key, value in self._config_cache.items():
            if key not in ["dev", "test", "prod"]:  # 不是环境配置
                config[key] = value

        # 然后添加环境特定配置
        env_config = self._config_cache[environment].copy()
        config.update(env_config)

        # 应用环境变量覆盖
        config = self._apply_env_overrides(config, environment)

        # 处理路径占位符
        config = self._process_path_placeholders(config)

        return config

    def _apply_env_overrides(self, config: Dict, environment: str) -> Dict:
        """
        应用环境变量覆盖

        Args:
            config: 原始配置
            environment: 环境名称

        Returns:
            Dict: 应用覆盖后的配置
        """
        # 数据库密码覆盖
        mysql_password = os.getenv("AQUA_MYSQL_PASSWORD")
        if mysql_password:
            if "mysql" not in config.get("datasources", {}):
                config.setdefault("datasources", {}).setdefault("mysql", {})
            config["datasources"]["mysql"]["password"] = mysql_password

        # Tushare Token 覆盖
        tushare_token = os.getenv("TUSHARE_TOKEN")
        if tushare_token:
            # 使用深度字典访问，避免覆盖现有配置
            if "datasources" in config and "api" in config["datasources"] and "tushare" in config["datasources"]["api"]:
                # 如果tushare配置存在，只更新access_token
                config["datasources"]["api"]["tushare"]["access_token"] = tushare_token
            else:
                # 如果配置结构不存在，创建最小配置
                config.setdefault("datasources", {}).setdefault("api", {})["tushare"] = {
                    "access_token": tushare_token,
                    "enabled": True,
                    "name": "Tushare Pro"
                }

        # 数据库路径覆盖
        db_path = os.getenv(f"AQUA_{environment.upper()}_DB_PATH")
        if db_path:
            if "database" not in config:
                config["database"] = {}
            config["database"]["path"] = db_path

        # CSV数据目录覆盖
        csv_dir = os.getenv(f"AQUA_{environment.upper()}_CSV_DIR")
        if csv_dir:
            if "csv" not in config:
                config["csv"] = {}
            config["csv"]["data_dir"] = csv_dir

        return config

    def _process_path_placeholders(self, config: Dict) -> Dict:
        """
        递归处理配置中的路径占位符

        Args:
            config: 配置字典

        Returns:
            Dict: 处理后的配置
        """
        processed_config = {}

        for key, value in config.items():
            if isinstance(value, dict):
                # 递归处理嵌套字典
                processed_config[key] = self._process_path_placeholders(value)
            elif isinstance(value, str) and ("{platform_" in value or "{datacenter_dir}" in value or "{logs_root}" in value or "{duckdb_root}" in value):
                # 处理包含占位符的字符串
                processed_config[key] = self._resolve_platform_placeholders(value)
            else:
                # 其他值直接复制
                processed_config[key] = value

        return processed_config

    def get_database_config(self, environment: str = "test") -> Dict:
        """
        获取数据库配置

        Args:
            environment: 环境名称

        Returns:
            Dict: 数据库配置
        """
        config = self.get_config(environment)
        db_config = config.get("database", {})

        # 设置默认值
        defaults = {
            "path": "data/duckdb/AQUA/DataCenter.duckdb",
            "auto_create": True,
            "backup_dir": "data/backup",
        }

        for key, default_value in defaults.items():
            if key not in db_config:
                db_config[key] = default_value

        # 处理路径占位符
        if "path" in db_config and isinstance(db_config["path"], str):
            if "{" in db_config["path"]:
                db_config["path"] = self._resolve_platform_placeholders(db_config["path"])
        
        if "backup_dir" in db_config and isinstance(db_config["backup_dir"], str):
            if "{" in db_config["backup_dir"]:
                db_config["backup_dir"] = self._resolve_platform_placeholders(db_config["backup_dir"])

        return db_config

    def get_mysql_config(self, environment: str = "test") -> Dict:
        """
        获取MySQL配置

        Args:
            environment: 环境名称

        Returns:
            Dict: MySQL配置
        """
        config = self.get_config(environment)
        mysql_config = config.get("mysql", {})

        # 设置默认值
        defaults = {
            "host": "localhost",
            "port": 3306,
            "user": "root",
            "password": "",
            "database": "qtdb_pro",
            "charset": "utf8mb4",
            "connect_timeout": 30,
            "read_timeout": 30,
        }

        for key, default_value in defaults.items():
            if key not in mysql_config:
                mysql_config[key] = default_value

        return mysql_config

    def get_csv_config(self, environment: str = "test") -> Dict:
        """
        获取CSV配置

        Args:
            environment: 环境名称

        Returns:
            Dict: CSV配置
        """
        config = self.get_config(environment)
        csv_config = config.get("csv", {})

        # 设置默认值
        defaults = {
            "data_dir": "/Users/<USER>/Documents/Data/FromC2C",
            "file_pattern": "*.csv",
            "encoding": "utf-8",
            "delimiter": ",",
            "batch_size": 5000,
            "max_file_size_mb": 200,
            "enable_parallel": True,
        }

        for key, default_value in defaults.items():
            if key not in csv_config:
                csv_config[key] = default_value

        return csv_config

    def get_logging_config(self, environment: str = "test") -> Dict:
        """
        获取日志配置

        Args:
            environment: 环境名称

        Returns:
            Dict: 日志配置
        """
        config = self.get_config(environment)
        logging_config = config.get("logging", {})

        # 设置默认值
        defaults = {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file_path": "logs/aqua_{date}.log",
            "max_file_size": "10MB",
            "backup_count": 5,
        }

        for key, default_value in defaults.items():
            if key not in logging_config:
                logging_config[key] = default_value

        return logging_config

    def get_performance_config(self, environment: str = "test") -> Dict:
        """
        获取性能配置

        Args:
            environment: 环境名称

        Returns:
            Dict: 性能配置
        """
        config = self.get_config(environment)
        performance_config = config.get("performance", {})

        # 设置默认值
        defaults = {
            "max_workers": 4,
            "chunk_size": 1000,
            "memory_limit_mb": 1024,
            "cache_size_mb": 256,
        }

        for key, default_value in defaults.items():
            if key not in performance_config:
                performance_config[key] = default_value

        return performance_config

    def validate_config(self, environment: str = "test") -> Dict:
        """
        验证配置完整性

        Args:
            environment: 环境名称

        Returns:
            Dict: 验证结果
        """
        validation_result = {"valid": True, "errors": [], "warnings": []}

        try:
            config = self.get_config(environment)

            # 验证数据库配置
            db_config = self.get_database_config(environment)
            if not db_config.get("path"):
                validation_result["errors"].append("数据库路径未配置")

            # 验证MySQL配置
            mysql_config = self.get_mysql_config(environment)
            if not mysql_config.get("host"):
                validation_result["errors"].append("MySQL主机地址未配置")
            if not mysql_config.get("password"):
                validation_result["warnings"].append("MySQL密码未配置")

            # 验证CSV配置
            csv_config = self.get_csv_config(environment)
            csv_dir = Path(csv_config.get("data_dir", ""))
            if not csv_dir.exists():
                validation_result["warnings"].append(f"CSV数据目录不存在: {csv_dir}")

            # 验证日志配置
            logging_config = self.get_logging_config(environment)
            log_dir = Path(
                logging_config.get("file_path", "logs/").replace("{date}", "")
            ).parent
            if not log_dir.exists():
                validation_result["warnings"].append(f"日志目录不存在: {log_dir}")

            # 如果有错误，标记为无效
            if validation_result["errors"]:
                validation_result["valid"] = False

        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"配置验证异常: {str(e)}")

        return validation_result

    def get_connection_string(self, environment: str = "test") -> str:
        """
        获取数据库连接字符串

        Args:
            environment: 环境名称

        Returns:
            str: 数据库连接字符串
        """
        db_config = self.get_database_config(environment)
        return db_config["path"]

    def get_mysql_connection_string(self, environment: str = "test") -> str:
        """
        获取MySQL连接字符串

        Args:
            environment: 环境名称

        Returns:
            str: MySQL连接字符串
        """
        mysql_config = self.get_mysql_config(environment)

        connection_string = (
            f"mysql://{mysql_config['user']}:{mysql_config['password']}"
            f"@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}"
        )

        return connection_string

    def reload_config(self):
        """重新加载配置文件"""
        self._config_cache = None
        self.logger.info("配置文件已重新加载")

    def get_all_environments(self) -> list:
        """
        获取所有环境名称

        Returns:
            list: 环境名称列表
        """
        if self._config_cache is None:
            self._config_cache = self._load_config()

        return list(self._config_cache.keys())

    def environment_exists(self, environment: str) -> bool:
        """
        检查环境是否存在

        Args:
            environment: 环境名称

        Returns:
            bool: 环境是否存在
        """
        return environment in self.get_all_environments()

    def get_config_summary(self, environment: str = "test") -> Dict:
        """
        获取配置摘要

        Args:
            environment: 环境名称

        Returns:
            Dict: 配置摘要
        """
        config = self.get_config(environment)

        summary = {
            "environment": environment,
            "database_path": self.get_database_config(environment).get("path"),
            "mysql_host": self.get_mysql_config(environment).get("host"),
            "csv_data_dir": self.get_csv_config(environment).get("data_dir"),
            "logging_level": self.get_logging_config(environment).get("level"),
            "config_sections": list(config.keys()),
        }

        return summary

    # =================================================================
    # EPIC3 Task 3.0.3a: 跨平台配置驱动路径处理
    # =================================================================

    def get_cross_platform_config(self) -> Dict:
        """
        获取跨平台配置

        Returns:
            Dict: 跨平台配置
        """
        if self._config_cache is None:
            self._config_cache = self._load_config()
        return self._config_cache.get("platform", {})

    def expand_cross_platform_path(
        self, path_config: str, auto_mkdir: bool = True
    ) -> Path:
        """
        跨平台路径展开和处理 - 支持Windows D:/Data和Unix ~/Documents/Data

        Args:
            path_config: 配置中的路径字符串（支持占位符、~和相对路径）
            auto_mkdir: 是否自动创建目录

        Returns:
            Path: 展开后的绝对路径

        Examples:
            >>> loader = ConfigLoader()
            >>> # Windows: D:/Data/FromC2C, Unix: ~/Documents/Data/FromC2C
            >>> path = loader.expand_cross_platform_path("{platform_csv_data_dir}")
        """
        cross_platform_config = self.get_cross_platform_config()

        # 处理平台特定占位符
        if "{" in path_config:
            path_config = self._resolve_platform_placeholders(path_config)
            
            # 🚨 Windows占位符问题修复：检查是否还有未解析的占位符
            import re
            remaining_placeholders = re.findall(r'\{[^}]+\}', path_config)
            if remaining_placeholders:
                self.logger.error(f"❌ Windows平台发现未解析的占位符: {remaining_placeholders} in path: {path_config}")
                # 🔧 使用新的路径管理系统作为后备方案
                try:
                    from .paths import resolve_path
                    path_config = resolve_path(path_config)
                    self.logger.info(f"✅ 使用新路径系统成功解析: {path_config}")
                except Exception as e:
                    self.logger.error(f"❌ 新路径系统也无法解析，跳过目录创建: {e}")
                    return Path(path_config)  # 返回未解析的路径，但不创建目录

        # 🔧 修复：在创建Path对象之前先检查用户目录展开
        original_path_str = path_config  # 保存原始字符串
        
        # 路径展开处理
        if cross_platform_config.get("path_expansion", True):
            # 用户目录展开 (~) - 优先处理，直接在字符串上操作
            if original_path_str.startswith("~"):
                # 使用os.path.expanduser而不是Path.expanduser，更可靠
                import os
                expanded_path_str = os.path.expanduser(original_path_str)
                path = Path(expanded_path_str)
                self.logger.debug(f"用户目录展开: {original_path_str} -> {path}")
            else:
                # 转换为Path对象
                path = Path(path_config)
                
                # 只对真正的相对路径进行处理，避免已展开的绝对路径被误处理
                if not path.is_absolute():
                    if original_path_str.startswith("./"):
                        # 项目相对路径 (./xxx -> 项目根目录/xxx)
                        project_root = Path.cwd()
                        path = (project_root / path.relative_to(".")).resolve()
                        self.logger.debug(
                            f"项目相对路径展开: {original_path_str} -> {path}"
                        )
                    else:
                        # 普通相对路径处理
                        path = path.resolve()
                        self.logger.debug(f"相对路径解析: {original_path_str} -> {path}")
        else:
            # 如果禁用路径展开，直接创建Path对象
            path = Path(path_config)

        # 🚨 Windows占位符问题修复：阻止创建包含未解析占位符的目录
        if "{" in str(path) and "}" in str(path):
            self.logger.error(f"❌ 阻止创建包含未解析占位符的目录: {path}")
            return path  # 返回路径但不创建目录

        # 路径验证和目录创建
        if cross_platform_config.get("path_validation", True):
            # 检查路径的父目录是否存在（对于文件路径）
            if "." in path.name:  # 可能是文件路径
                parent_dir = path.parent
                if not parent_dir.exists() and auto_mkdir:
                    try:
                        parent_dir.mkdir(parents=True, exist_ok=True)
                        self.logger.info(f"自动创建目录: {parent_dir}")
                    except OSError as e:
                        self.logger.error(f"创建目录失败 {parent_dir}: {e}")
                        raise
            else:  # 目录路径
                if not path.exists() and auto_mkdir:
                    try:
                        path.mkdir(parents=True, exist_ok=True)
                        self.logger.info(f"自动创建目录: {path}")
                    except OSError as e:
                        self.logger.error(f"创建目录失败 {path}: {e}")
                        raise

        return path

    def _resolve_platform_placeholders(self, path_config: str) -> str:
        """
        解析平台特定占位符

        Args:
            path_config: 包含占位符的路径配置

        Returns:
            str: 解析后的路径配置
        """
        cross_platform_config = self.get_cross_platform_config()
        platform_info = self.get_platform_info()

        # 确定使用哪个平台配置
        if platform_info["is_windows"]:
            platform_paths = cross_platform_config.get("windows", {}).get("paths", {})
        else:
            platform_paths = cross_platform_config.get("unix", {}).get("paths", {})

        # 如果没有找到平台特定配置，使用默认配置
        if not platform_paths:
            platform_paths = cross_platform_config.get("default_paths", {})

        # 获取数据源配置
        datasources_config = {}
        if platform_info["is_windows"]:
            datasources_config = cross_platform_config.get("windows", {}).get("datasources", {})
        else:
            datasources_config = cross_platform_config.get("unix", {}).get("datasources", {})
        
        # 🚨 Windows占位符问题修复：使用正确的平台配置默认值
        if platform_info["is_windows"]:
            # Windows平台使用D:/Data路径结构
            default_logs_root = "D:/Data/duckdb/AQUA/DataCenter/logs"
            default_cache_root = "D:/Data/duckdb/AQUA/DataCenter/cache"
            default_backup_root = "D:/Data/duckdb/AQUA/DataCenter/backup"
        else:
            # Unix平台使用~/Documents/Data路径结构
            default_logs_root = "~/Documents/Data/duckdb/AQUA/DataCenter/logs"
            default_cache_root = "~/Documents/Data/duckdb/AQUA/DataCenter/cache"
            default_backup_root = "~/Documents/Data/duckdb/AQUA/DataCenter/backup"
        
        # 替换占位符
        placeholder_mapping = {
            "{platform_data_root}": cross_platform_config.get("windows" if platform_info["is_windows"] else "unix", {}).get("data_root", "~/Documents/Data"),
            "{platform_csv_data_dir}": datasources_config.get("csv_root", "~/Documents/Data/FromC2C"),
            "{platform_duckdb_dir}": platform_paths.get("duckdb_root", "~/Documents/Data/duckdb/AQUA"),
            "{datacenter_dir}": platform_paths.get("datacenter_dir", "D:/Data/duckdb/AQUA/DataCenter" if platform_info["is_windows"] else "~/Documents/Data/duckdb/AQUA/DataCenter"),
            "{logs_root}": platform_paths.get("logs_root", default_logs_root),
            "{platform_backup_dir}": platform_paths.get("backup_root", default_backup_root),  
            "{platform_cache_dir}": platform_paths.get("cache_root", default_cache_root),
            "{platform_temp_dir}": platform_paths.get("temp_dir", "./temp"), 
            "{platform_logs_dir}": platform_paths.get("logs_root", default_logs_root),
            # 添加缺失的占位符映射
            "{csv_root}": datasources_config.get("csv_root", "~/Documents/Data/duckdb/AQUA/DataCenter/datasources/csv"),
            "{fromc2c_data}": datasources_config.get("fromc2c_data", "~/Documents/Data/FromC2C"),
            "{cache_root}": platform_paths.get("cache_root", "./cache"),
            "{backup_root}": platform_paths.get("backup_root", "./data/backup"),
            # 添加日期占位符处理
            "{date}": self._get_current_date_string(),
        }

        resolved_path = path_config
        for placeholder, replacement in placeholder_mapping.items():
            if placeholder in resolved_path:
                resolved_path = resolved_path.replace(placeholder, replacement)
                self.logger.debug(f"占位符替换: {placeholder} -> {replacement}")

        # 检查是否还有未解析的占位符
        import re
        remaining_placeholders = re.findall(r'\{[^}]+\}', resolved_path)
        if remaining_placeholders:
            self.logger.warning(f"发现未解析的占位符: {remaining_placeholders} in path: {resolved_path}")
            # 对于未知占位符，使用默认路径避免创建错误目录
            for placeholder in remaining_placeholders:
                resolved_path = resolved_path.replace(placeholder, "./unknown_placeholder")
                self.logger.warning(f"未知占位符 {placeholder} 已替换为 ./unknown_placeholder")

        return resolved_path

    def _get_current_date_string(self) -> str:
        """
        获取当前日期字符串，用于日志文件名等
        
        Returns:
            str: 格式为YYYY-MM-DD的日期字符串
        """
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d")

    def get_data_layers_config(self) -> Dict:
        """
        获取DATA_DICTIONARY.md v3.0分层数据架构配置

        Returns:
            Dict: 分层数据配置
        """
        if self._config_cache is None:
            self._config_cache = self._load_config()
        data_layers = self._config_cache.get("data_layers", {})

        # 处理每一层的路径配置
        processed_layers = {}
        for layer_name, layer_config in data_layers.items():
            if isinstance(layer_config, dict) and "data_dir" in layer_config:
                processed_config = layer_config.copy()
                # 🚩 WINDOWS_VERIFICATION_FLAG: 验证分层数据路径展开
                processed_config["data_dir_resolved"] = str(
                    self.expand_cross_platform_path(layer_config["data_dir"])
                )
                processed_layers[layer_name] = processed_config
            else:
                processed_layers[layer_name] = layer_config

        return processed_layers

    def get_platform_info(self) -> Dict:
        """
        获取当前平台信息

        Returns:
            Dict: 平台信息
        """
        return {
            "system": platform.system(),  # 'Darwin', 'Windows', 'Linux'
            "platform": platform.platform(),  # 详细平台信息
            "architecture": platform.architecture()[0],  # 'x86_64', 'arm64'
            "python_version": platform.python_version(),
            "is_windows": platform.system() == "Windows",
            "is_macos": platform.system() == "Darwin",
            "is_linux": platform.system() == "Linux",
        }


# 便捷函数
def load_config(environment: str = "test") -> Dict:
    """
    加载配置的便捷函数

    Args:
        environment: 环境名称

    Returns:
        Dict: 配置字典
    """
    loader = ConfigLoader()
    return loader.get_config(environment)


def load_database_config(environment: str = "test") -> Dict:
    """
    加载数据库配置的便捷函数

    Args:
        environment: 环境名称

    Returns:
        Dict: 数据库配置字典
    """
    loader = ConfigLoader()
    return loader.get_database_config(environment)


def load_mysql_config(environment: str = "test") -> Dict:
    """
    加载MySQL配置的便捷函数

    Args:
        environment: 环境名称

    Returns:
        Dict: MySQL配置字典
    """
    loader = ConfigLoader()
    return loader.get_mysql_config(environment)


def load_csv_config(environment: str = "test") -> Dict:
    """
    加载CSV配置的便捷函数

    Args:
        environment: 环境名称

    Returns:
        Dict: CSV配置字典
    """
    loader = ConfigLoader()
    return loader.get_csv_config(environment)


def validate_environment_config(environment: str = "test") -> Dict:
    """
    验证环境配置的便捷函数

    Args:
        environment: 环境名称

    Returns:
        Dict: 验证结果
    """
    loader = ConfigLoader()
    return loader.validate_config(environment)
