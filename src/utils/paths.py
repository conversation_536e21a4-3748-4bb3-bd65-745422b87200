#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AQUA统一路径管理系统

解决占位符问题，提供跨平台一致的路径管理
版本: 1.0.0
创建时间: 2025-07-31
"""

import os
import platform
from pathlib import Path
from typing import Dict, Optional


class Paths:
    """统一路径管理类 - 解决占位符问题的核心解决方案"""
    
    # 核心路径定义 - 基于项目根目录的绝对路径
    ROOT = Path(__file__).resolve().parent.parent.parent
    SRC = ROOT / "src"
    CONFIG = ROOT / "config"
    DATA = ROOT / "data"
    LOGS = ROOT / "logs"
    CACHE = ROOT / "cache"
    TEMP = ROOT / "temp"
    TESTS = ROOT / "tests"
    DOCS = ROOT / "docs"
    
    # 数据相关路径
    DATACENTER = DATA / "datacenter"
    BACKUP = DATA / "backup"
    RAW_DATA = DATA / "raw"
    PROCESSED_DATA = DATA / "processed"
    
    # 虚拟环境路径
    VENV = ROOT / ".venv"
    VENV_SCRIPTS = VENV / ("Scripts" if platform.system() == "Windows" else "bin")
    VENV_PYTHON = VENV_SCRIPTS / ("python.exe" if platform.system() == "Windows" else "python")
    
    @classmethod
    def ensure_dirs(cls) -> None:
        """确保所有核心目录存在，自动创建缺失的目录"""
        core_dirs = [
            cls.CONFIG, cls.DATA, cls.LOGS, cls.CACHE, cls.TEMP,
            cls.DATACENTER, cls.BACKUP, cls.RAW_DATA, cls.PROCESSED_DATA
        ]
        
        for dir_path in core_dirs:
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
            except (PermissionError, OSError) as e:
                print(f"⚠️ 无法创建目录 {dir_path}: {e}")
    
    @classmethod
    def resolve_placeholder(cls, path_template: str) -> str:
        """
        解析路径占位符 - 彻底解决{datacenter_dir}和{logs_root}问题
        
        Args:
            path_template: 包含占位符的路径模板
            
        Returns:
            str: 解析后的绝对路径
        """
        # 占位符映射表 - 解决所有已知占位符问题
        placeholder_map = {
            '{root}': str(cls.ROOT),
            '{src}': str(cls.SRC),
            '{config}': str(cls.CONFIG),
            '{data}': str(cls.DATA),
            '{logs}': str(cls.LOGS),
            '{logs_root}': str(cls.LOGS),  # 解决logs_root占位符问题
            '{cache}': str(cls.CACHE),
            '{cache_root}': str(cls.CACHE),
            '{temp}': str(cls.TEMP),
            '{datacenter}': str(cls.DATACENTER),
            '{datacenter_dir}': str(cls.DATACENTER),  # 解决datacenter_dir占位符问题
            '{backup}': str(cls.BACKUP),
            '{backup_dir}': str(cls.BACKUP),
            '{raw_data}': str(cls.RAW_DATA),
            '{processed_data}': str(cls.PROCESSED_DATA),
        }
        
        # 添加日期和环境占位符
        from datetime import datetime
        current_date = datetime.now().strftime("%Y%m%d")
        current_env = os.getenv('AQUA_ENV', 'dev')
        
        placeholder_map.update({
            '{date}': current_date,
            '{env}': current_env,
            '{environment}': current_env,
        })
        
        # 执行占位符替换
        resolved_path = path_template
        for placeholder, replacement in placeholder_map.items():
            resolved_path = resolved_path.replace(placeholder, replacement)
        
        # 验证是否还有未解析的占位符
        import re
        remaining_placeholders = re.findall(r'\{[^}]+\}', resolved_path)
        if remaining_placeholders:
            print(f"⚠️ 发现未解析的占位符: {remaining_placeholders} in path: {resolved_path}")
            
        return resolved_path
    
    @classmethod
    def get_absolute_path(cls, relative_path: str) -> Path:
        """
        获取基于项目根目录的绝对路径
        
        Args:
            relative_path: 相对路径字符串
            
        Returns:
            Path: 绝对路径对象
        """
        if relative_path.startswith('/') or (len(relative_path) > 1 and relative_path[1] == ':'):
            # 已经是绝对路径
            return Path(relative_path)
        
        # 解析占位符
        resolved = cls.resolve_placeholder(relative_path)
        
        # 如果仍然是相对路径，基于ROOT解析
        if not Path(resolved).is_absolute():
            return cls.ROOT / resolved
        
        return Path(resolved)
    
    @classmethod
    def clean_placeholder_dirs(cls) -> None:
        """
        清理错误创建的占位符目录
        
        这个方法专门用于清理{datacenter_dir}和{logs_root}等占位符目录
        """
        error_dirs = [
            cls.ROOT / "{datacenter_dir}",
            cls.ROOT / "{logs_root}",
            cls.ROOT / "{cache_root}",
            cls.ROOT / "{backup_dir}",
            cls.ROOT / "{temp_dir}",
        ]
        
        cleaned_count = 0
        for error_dir in error_dirs:
            if error_dir.exists():
                try:
                    if error_dir.is_dir():
                        # 检查目录是否为空
                        if not any(error_dir.iterdir()):
                            error_dir.rmdir()
                            print(f"✅ 已清理空的占位符目录: {error_dir}")
                            cleaned_count += 1
                        else:
                            print(f"⚠️ 占位符目录不为空，需要手动处理: {error_dir}")
                    else:
                        error_dir.unlink()
                        print(f"✅ 已清理占位符文件: {error_dir}")
                        cleaned_count += 1
                except (PermissionError, OSError) as e:
                    print(f"❌ 无法清理占位符目录 {error_dir}: {e}")
        
        if cleaned_count > 0:
            print(f"🎉 成功清理了 {cleaned_count} 个错误的占位符目录/文件")
        else:
            print("✅ 未发现需要清理的占位符目录")
    
    @classmethod
    def get_platform_info(cls) -> Dict[str, str]:
        """
        获取平台信息，用于路径处理决策
        
        Returns:
            Dict[str, str]: 平台信息字典
        """
        return {
            'system': platform.system(),
            'platform': platform.platform(),
            'architecture': platform.architecture()[0],
            'python_version': platform.python_version(),
            'is_windows': platform.system() == "Windows",
            'is_macos': platform.system() == "Darwin",
            'is_linux': platform.system() == "Linux",
            'path_separator': os.sep,
            'path_delimiter': os.pathsep,
        }


# 便捷函数
def ensure_directory(path: str) -> Path:
    """
    确保目录存在的便捷函数
    
    Args:
        path: 目录路径（支持占位符）
        
    Returns:
        Path: 创建后的目录路径对象
    """
    resolved_path = Paths.get_absolute_path(path)
    resolved_path.mkdir(parents=True, exist_ok=True)
    return resolved_path


def resolve_path(path_template: str) -> str:
    """
    解析路径占位符的便捷函数
    
    Args:
        path_template: 包含占位符的路径模板
        
    Returns:
        str: 解析后的路径字符串
    """
    return Paths.resolve_placeholder(path_template)


# 模块初始化时执行
if __name__ == '__main__':
    # 执行自检和清理
    print("🔧 AQUA路径管理系统初始化...")
    print(f"📁 项目根目录: {Paths.ROOT}")
    
    # 确保核心目录存在
    Paths.ensure_dirs()
    
    # 清理错误的占位符目录
    Paths.clean_placeholder_dirs()
    
    # 显示平台信息
    platform_info = Paths.get_platform_info()
    print(f"💻 运行平台: {platform_info['system']} ({platform_info['architecture']})")
    
    print("✅ 路径管理系统初始化完成")