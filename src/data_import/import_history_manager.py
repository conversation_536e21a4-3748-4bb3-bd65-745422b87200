#!/usr/bin/env python3
"""
数据导入历史记录管理器

提供导入历史记录和统计信息的管理功能
基于AQUA宪法的配置驱动和复用优先原则
"""

import logging
import json
from typing import Dict, List, Optional
from datetime import datetime
from uuid import uuid4

from ..database.connection_manager import DuckDBConnectionManager
from ..database.data_dictionary_schema import DataDictionarySchema
from ..utils.time_utils import get_beijing_time_now
from ..utils.config_loader import ConfigLoader


class ImportHistoryManager:
    """导入历史记录管理器"""

    def __init__(self, environment: str = "dev"):
        """
        初始化导入历史记录管理器

        Args:
            environment: 环境名称
        """
        self.environment = environment
        self.config_loader = ConfigLoader()
        self.connection_manager = DuckDBConnectionManager(environment)
        self.schema_manager = DataDictionarySchema(environment)
        self.logger = logging.getLogger(__name__)

        # 确保历史记录表存在
        self._initialize_history_tables()

    def _initialize_history_tables(self):
        """初始化历史记录表"""
        try:
            with self.connection_manager.get_cursor() as cursor:
                # 创建导入历史表
                self.schema_manager.create_data_dictionary_table(
                    "data_import_history", cursor
                )
                # 创建导入统计表
                self.schema_manager.create_data_dictionary_table(
                    "data_import_stats", cursor
                )
            self.logger.info("导入历史记录表初始化完成")
        except Exception as e:
            self.logger.error(f"初始化历史记录表失败: {e}")

    def record_import_start(
        self,
        import_type: str,
        source_info: Dict,
        target_tables: Optional[List[str]] = None,
        import_config: Optional[Dict] = None,
        session_info: Optional[Dict] = None,
    ) -> str:
        """
        记录导入开始

        Args:
            import_type: 导入类型 (csv, mysql, fromc2c, tushare)
            source_info: 数据源信息
            target_tables: 目标表列表
            import_config: 导入配置
            session_info: 会话信息

        Returns:
            str: 导入记录ID
        """
        import_id = str(uuid4())

        try:
            insert_sql = """
            INSERT INTO data_import_history (
                import_id, import_type, environment, source_info,
                target_tables, import_status, start_time,
                import_config, session_info
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            values = [
                import_id,
                import_type,
                self.environment,
                json.dumps(source_info, ensure_ascii=False),
                json.dumps(target_tables) if target_tables else None,
                "running",
                get_beijing_time_now(),
                (
                    json.dumps(import_config, ensure_ascii=False)
                    if import_config
                    else None
                ),
                json.dumps(session_info, ensure_ascii=False) if session_info else None,
            ]

            self.connection_manager.execute_update(insert_sql, values)
            self.logger.info(f"记录导入开始: {import_id}")

            return import_id

        except Exception as e:
            self.logger.error(f"记录导入开始失败: {e}")
            raise

    def update_import_progress(
        self,
        import_id: str,
        total_records: Optional[int] = None,
        success_records: Optional[int] = None,
        error_records: Optional[int] = None,
    ):
        """
        更新导入进度

        Args:
            import_id: 导入记录ID
            total_records: 总记录数
            success_records: 成功记录数
            error_records: 错误记录数
        """
        try:
            update_fields = []
            values = []

            if total_records is not None:
                update_fields.append("total_records = ?")
                values.append(total_records)

            if success_records is not None:
                update_fields.append("success_records = ?")
                values.append(success_records)

            if error_records is not None:
                update_fields.append("error_records = ?")
                values.append(error_records)

            update_fields.append("updated_at = ?")
            values.append(get_beijing_time_now())

            values.append(import_id)

            if update_fields:
                update_sql = f"""
                UPDATE data_import_history 
                SET {', '.join(update_fields)}
                WHERE import_id = ?
                """

                self.connection_manager.execute_update(update_sql, values)
                self.logger.debug(f"更新导入进度: {import_id}")

        except Exception as e:
            self.logger.error(f"更新导入进度失败: {e}")

    def complete_import(
        self,
        import_id: str,
        status: str,
        total_records: int = 0,
        success_records: int = 0,
        error_records: int = 0,
        error_message: Optional[str] = None,
    ):
        """
        完成导入记录

        Args:
            import_id: 导入记录ID
            status: 最终状态 (completed, failed, cancelled)
            total_records: 总记录数
            success_records: 成功记录数
            error_records: 错误记录数
            error_message: 错误消息
        """
        try:
            end_time = get_beijing_time_now()

            # 获取开始时间以计算持续时间
            start_time_sql = (
                "SELECT start_time FROM data_import_history WHERE import_id = ?"
            )
            result = self.connection_manager.execute_query(start_time_sql, [import_id])

            duration_seconds = 0
            if result:
                start_time = result[0][0]
                if isinstance(start_time, str):
                    start_time = datetime.fromisoformat(
                        start_time.replace("Z", "+00:00")
                    )
                # 确保两个时间都是相同时区
                if hasattr(start_time, "tzinfo") and start_time.tzinfo is not None:
                    if end_time.tzinfo is None:
                        end_time = end_time.replace(tzinfo=start_time.tzinfo)
                elif end_time.tzinfo is not None:
                    if hasattr(start_time, "replace"):
                        start_time = start_time.replace(tzinfo=end_time.tzinfo)
                duration_seconds = int((end_time - start_time).total_seconds())

            update_sql = """
            UPDATE data_import_history 
            SET 
                import_status = ?,
                total_records = ?,
                success_records = ?,
                error_records = ?,
                end_time = ?,
                duration_seconds = ?,
                error_message = ?,
                updated_at = ?
            WHERE import_id = ?
            """

            values = [
                status,
                total_records,
                success_records,
                error_records,
                end_time,
                duration_seconds,
                error_message,
                end_time,
                import_id,
            ]

            self.connection_manager.execute_update(update_sql, values)
            self.logger.info(f"完成导入记录: {import_id} - {status}")

            # 更新统计信息
            self._update_daily_stats(import_id)

        except Exception as e:
            self.logger.error(f"完成导入记录失败: {e}")

    def get_import_history(
        self,
        import_type: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> Dict:
        """
        获取导入历史记录

        Args:
            import_type: 导入类型筛选
            start_date: 开始日期
            end_date: 结束日期
            limit: 记录数限制
            offset: 偏移量

        Returns:
            Dict: 历史记录列表和统计信息
        """
        try:
            # 构建查询条件
            where_conditions = ["environment = ?"]
            params = [self.environment]

            if import_type:
                where_conditions.append("import_type = ?")
                params.append(import_type)

            if start_date:
                where_conditions.append("start_time >= ?")
                params.append(start_date)

            if end_date:
                where_conditions.append("start_time <= ?")
                params.append(end_date)

            where_clause = " AND ".join(where_conditions)

            # 获取总数
            count_sql = f"""
            SELECT COUNT(*) 
            FROM data_import_history 
            WHERE {where_clause}
            """

            count_result = self.connection_manager.execute_query(count_sql, params)
            total_count = count_result[0][0] if count_result else 0

            # 获取记录
            data_sql = f"""
            SELECT 
                import_id, import_type, source_info, target_tables,
                import_status, total_records, success_records, error_records,
                start_time, end_time, duration_seconds, error_message,
                import_config, session_info, created_at
            FROM data_import_history 
            WHERE {where_clause}
            ORDER BY start_time DESC
            LIMIT ? OFFSET ?
            """

            data_params = params + [limit, offset]
            data_result = self.connection_manager.execute_query(data_sql, data_params)

            # 格式化结果
            records = []
            for row in data_result:
                record = {
                    "import_id": row[0],
                    "import_type": row[1],
                    "source_info": json.loads(row[2]) if row[2] else {},
                    "target_tables": json.loads(row[3]) if row[3] else [],
                    "import_status": row[4],
                    "total_records": row[5] or 0,
                    "success_records": row[6] or 0,
                    "error_records": row[7] or 0,
                    "start_time": row[8].isoformat() if row[8] else None,
                    "end_time": row[9].isoformat() if row[9] else None,
                    "duration_seconds": row[10] or 0,
                    "error_message": row[11],
                    "import_config": json.loads(row[12]) if row[12] else {},
                    "session_info": json.loads(row[13]) if row[13] else {},
                    "created_at": row[14].isoformat() if row[14] else None,
                }
                records.append(record)

            return {
                "success": True,
                "data": {
                    "records": records,
                    "total_count": total_count,
                    "limit": limit,
                    "offset": offset,
                    "has_next": offset + limit < total_count,
                },
                "message": f"成功获取{len(records)}条导入历史记录",
            }

        except Exception as e:
            self.logger.error(f"获取导入历史失败: {e}")
            return {
                "success": False,
                "message": f"获取导入历史失败: {str(e)}",
                "data": {"records": [], "total_count": 0},
            }

    def get_import_stats(
        self,
        period: str = "day",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        import_type: Optional[str] = None,
    ) -> Dict:
        """
        获取导入统计信息

        Args:
            period: 统计周期 (day, week, month)
            start_date: 开始日期
            end_date: 结束日期
            import_type: 导入类型筛选

        Returns:
            Dict: 统计信息
        """
        try:
            # 构建查询条件
            where_conditions = ["environment = ?"]
            params = [self.environment]

            if import_type:
                where_conditions.append("import_type = ?")
                params.append(import_type)

            if start_date:
                where_conditions.append("stat_date >= ?")
                params.append(start_date)

            if end_date:
                where_conditions.append("stat_date <= ?")
                params.append(end_date)

            where_clause = " AND ".join(where_conditions)

            # 获取统计数据
            stats_sql = f"""
            SELECT 
                stat_date, import_type, 
                total_imports, successful_imports, failed_imports,
                total_records, avg_duration_seconds, success_rate,
                error_summary
            FROM data_import_stats 
            WHERE {where_clause} AND stat_period = ?
            ORDER BY stat_date DESC, import_type
            """

            stats_params = params + [period]
            stats_result = self.connection_manager.execute_query(
                stats_sql, stats_params
            )

            # 获取汇总统计
            summary_sql = f"""
            SELECT 
                COUNT(*) as total_periods,
                SUM(total_imports) as total_imports,
                SUM(successful_imports) as successful_imports,
                SUM(failed_imports) as failed_imports,
                SUM(total_records) as total_records,
                AVG(success_rate) as avg_success_rate
            FROM data_import_stats 
            WHERE {where_clause} AND stat_period = ?
            """

            summary_result = self.connection_manager.execute_query(
                summary_sql, stats_params
            )

            # 格式化结果
            stats_data = []
            for row in stats_result:
                stat_record = {
                    "stat_date": row[0].isoformat() if row[0] else None,
                    "import_type": row[1],
                    "total_imports": row[2] or 0,
                    "successful_imports": row[3] or 0,
                    "failed_imports": row[4] or 0,
                    "total_records": row[5] or 0,
                    "avg_duration_seconds": float(row[6]) if row[6] else 0.0,
                    "success_rate": float(row[7]) if row[7] else 0.0,
                    "error_summary": json.loads(row[8]) if row[8] else {},
                }
                stats_data.append(stat_record)

            summary_data = {}
            if summary_result:
                summary_row = summary_result[0]
                summary_data = {
                    "total_periods": summary_row[0] or 0,
                    "total_imports": summary_row[1] or 0,
                    "successful_imports": summary_row[2] or 0,
                    "failed_imports": summary_row[3] or 0,
                    "total_records": summary_row[4] or 0,
                    "avg_success_rate": (
                        float(summary_row[5]) if summary_row[5] else 0.0
                    ),
                }

            return {
                "success": True,
                "data": {
                    "period": period,
                    "stats": stats_data,
                    "summary": summary_data,
                },
                "message": f"成功获取{len(stats_data)}条统计记录",
            }

        except Exception as e:
            self.logger.error(f"获取导入统计失败: {e}")
            return {
                "success": False,
                "message": f"获取导入统计失败: {str(e)}",
                "data": {"stats": [], "summary": {}},
            }

    def _update_daily_stats(self, import_id: str):
        """更新每日统计数据"""
        try:
            # 获取导入记录信息
            record_sql = """
            SELECT import_type, import_status, total_records, 
                   duration_seconds, start_time, error_message
            FROM data_import_history 
            WHERE import_id = ?
            """

            record_result = self.connection_manager.execute_query(
                record_sql, [import_id]
            )
            if not record_result:
                return

            record = record_result[0]
            import_type = record[0]
            import_status = record[1]
            total_records = record[2] or 0
            duration_seconds = record[3] or 0
            start_time = record[4]
            error_message = record[5]

            # 确定统计日期
            if isinstance(start_time, str):
                stat_date = datetime.fromisoformat(start_time).date()
            else:
                stat_date = start_time.date()

            stat_id = f"{self.environment}_{import_type}_day_{stat_date.isoformat()}"

            # 检查统计记录是否存在
            existing_sql = """
            SELECT stat_id FROM data_import_stats 
            WHERE stat_id = ?
            """

            existing_result = self.connection_manager.execute_query(
                existing_sql, [stat_id]
            )

            if existing_result:
                # 更新现有统计
                update_sql = """
                UPDATE data_import_stats 
                SET 
                    total_imports = total_imports + 1,
                    successful_imports = successful_imports + ?,
                    failed_imports = failed_imports + ?,
                    total_records = total_records + ?,
                    total_duration_seconds = total_duration_seconds + ?,
                    updated_at = ?
                WHERE stat_id = ?
                """

                success_count = 1 if import_status == "completed" else 0
                failed_count = 1 if import_status == "failed" else 0

                update_values = [
                    success_count,
                    failed_count,
                    total_records,
                    duration_seconds,
                    get_beijing_time_now(),
                    stat_id,
                ]

                self.connection_manager.execute_update(update_sql, update_values)

                # 重新计算成功率和平均时长
                self._recalculate_stat_metrics(stat_id)

            else:
                # 创建新统计记录
                success_count = 1 if import_status == "completed" else 0
                failed_count = 1 if import_status == "failed" else 0
                success_rate = 1.0 if import_status == "completed" else 0.0

                insert_sql = """
                INSERT INTO data_import_stats (
                    stat_id, environment, import_type, stat_period,
                    stat_date, total_imports, successful_imports, failed_imports,
                    total_records, total_duration_seconds, avg_duration_seconds,
                    success_rate, error_summary
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                error_summary = {}
                if error_message:
                    error_summary = {"errors": [error_message]}

                insert_values = [
                    stat_id,
                    self.environment,
                    import_type,
                    "day",
                    stat_date,
                    1,
                    success_count,
                    failed_count,
                    total_records,
                    duration_seconds,
                    duration_seconds,
                    success_rate,
                    json.dumps(error_summary),
                ]

                self.connection_manager.execute_update(insert_sql, insert_values)

            self.logger.debug(f"更新每日统计: {stat_id}")

        except Exception as e:
            self.logger.error(f"更新每日统计失败: {e}")

    def _recalculate_stat_metrics(self, stat_id: str):
        """重新计算统计指标"""
        try:
            update_sql = """
            UPDATE data_import_stats 
            SET 
                success_rate = CASE 
                    WHEN total_imports > 0 THEN CAST(successful_imports AS DECIMAL) / total_imports 
                    ELSE 0.0 
                END,
                avg_duration_seconds = CASE 
                    WHEN total_imports > 0 THEN CAST(total_duration_seconds AS DECIMAL) / total_imports 
                    ELSE 0.0 
                END
            WHERE stat_id = ?
            """

            self.connection_manager.execute_update(update_sql, [stat_id])

        except Exception as e:
            self.logger.error(f"重新计算统计指标失败: {e}")

    def delete_import_record(self, import_id: str) -> Dict:
        """删除导入记录"""
        try:
            delete_sql = "DELETE FROM data_import_history WHERE import_id = ?"
            rows_affected = self.connection_manager.execute_update(
                delete_sql, [import_id]
            )

            if rows_affected > 0:
                return {"success": True, "message": f"成功删除导入记录: {import_id}"}
            else:
                return {"success": False, "message": f"导入记录不存在: {import_id}"}

        except Exception as e:
            self.logger.error(f"删除导入记录失败: {e}")
            return {"success": False, "message": f"删除导入记录失败: {str(e)}"}
