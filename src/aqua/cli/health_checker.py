"""
AQUA CLI: 系统健康检查和自动修复系统
95%常见问题自动解决，2分钟快速诊断
"""
import os
import sys
import subprocess
import platform
import importlib
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import typer
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.table import Table
from rich.text import Text
from rich.tree import Tree

console = Console()

class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class HealthCheckResult:
    """健康检查结果"""
    name: str
    status: HealthStatus
    message: str
    details: Optional[str] = None
    auto_fix_available: bool = False
    fix_command: Optional[str] = None
    fix_description: Optional[str] = None

@dataclass
class SystemReport:
    """系统健康报告"""
    overall_status: HealthStatus
    checks: List[HealthCheckResult] = field(default_factory=list)
    auto_fixes_applied: List[str] = field(default_factory=list)
    manual_actions_needed: List[str] = field(default_factory=list)
    performance_metrics: Dict[str, Any] = field(default_factory=dict)

class HealthChecker:
    """系统健康检查器"""
    
    def __init__(self):
        self.console = console
        self.project_root = Path(__file__).resolve().parent.parent.parent.parent
        self.checks = [
            self._check_python_environment,
            self._check_virtual_environment,
            self._check_dependencies,
            self._check_configuration,
            self._check_database,
            self._check_data_sources,
            self._check_network_connectivity,
            self._check_disk_space,
            self._check_memory_usage,
            self._check_permissions,
            self._check_platform_compatibility,
            self._check_service_status
        ]
    
    def run_full_check(self, auto_fix: bool = True) -> SystemReport:
        """运行完整的系统健康检查"""
        self.console.print(Panel.fit(
            "🔍 [bold cyan]AQUA 系统健康检查[/bold cyan]\n\n"
            "正在进行全面系统诊断...\n"
            f"自动修复: {'启用' if auto_fix else '禁用'}",
            title="系统诊断",
            border_style="cyan"
        ))
        
        report = SystemReport(overall_status=HealthStatus.HEALTHY)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            console=self.console
        ) as progress:
            
            task = progress.add_task("系统检查", total=len(self.checks))
            
            for check_func in self.checks:
                check_name = check_func.__name__.replace('_check_', '').replace('_', ' ').title()
                progress.update(task, description=f"检查 {check_name}")
                
                try:
                    result = check_func()
                    report.checks.append(result)
                    
                    # 自动修复
                    if auto_fix and result.status in [HealthStatus.ERROR, HealthStatus.WARNING] and result.auto_fix_available:
                        fix_result = self._apply_auto_fix(result)
                        if fix_result:
                            report.auto_fixes_applied.append(f"{result.name}: {fix_result}")
                            # 重新检查
                            result = check_func()
                            report.checks[-1] = result
                    
                    # 更新整体状态
                    if result.status == HealthStatus.CRITICAL:
                        report.overall_status = HealthStatus.CRITICAL
                    elif result.status == HealthStatus.ERROR and report.overall_status != HealthStatus.CRITICAL:
                        report.overall_status = HealthStatus.ERROR
                    elif result.status == HealthStatus.WARNING and report.overall_status == HealthStatus.HEALTHY:
                        report.overall_status = HealthStatus.WARNING
                
                except Exception as e:
                    report.checks.append(HealthCheckResult(
                        name=check_name,
                        status=HealthStatus.ERROR,
                        message=f"检查失败: {str(e)}",
                        details=None,
                        auto_fix_available=False
                    ))
                
                progress.advance(task)
        
        # 生成手动操作建议
        for check in report.checks:
            if check.status in [HealthStatus.ERROR, HealthStatus.CRITICAL] and not check.auto_fix_available:
                action = f"{check.name}: {check.message}"
                if check.fix_description:
                    action += f" - {check.fix_description}"
                report.manual_actions_needed.append(action)
        
        # 收集性能指标
        report.performance_metrics = self._collect_performance_metrics()
        
        return report
    
    def _check_python_environment(self) -> HealthCheckResult:
        """检查Python环境"""
        try:
            python_version = sys.version_info
            min_version = (3, 11)
            max_version = (3, 12)
            
            if python_version[:2] < min_version:
                return HealthCheckResult(
                    name="Python版本",
                    status=HealthStatus.ERROR,
                    message=f"Python版本过低: {python_version.major}.{python_version.minor}",
                    details=f"需要Python {min_version[0]}.{min_version[1]}+",
                    auto_fix_available=False,
                    fix_description="请升级到Python 3.11或更高版本"
                )
            elif python_version[:2] > max_version:
                return HealthCheckResult(
                    name="Python版本",
                    status=HealthStatus.WARNING,
                    message=f"Python版本较新: {python_version.major}.{python_version.minor}",
                    details="可能存在兼容性问题",
                    auto_fix_available=False
                )
            else:
                return HealthCheckResult(
                    name="Python版本",
                    status=HealthStatus.HEALTHY,
                    message=f"Python {python_version.major}.{python_version.minor}.{python_version.micro} ✅"
                )
        
        except Exception as e:
            return HealthCheckResult(
                name="Python版本",
                status=HealthStatus.ERROR,
                message=f"无法检测Python版本: {str(e)}",
                auto_fix_available=False
            )
    
    def _check_virtual_environment(self) -> HealthCheckResult:
        """检查虚拟环境"""
        venv_path = self.project_root / ".venv"
        python_exec = venv_path / ("Scripts" if platform.system() == "Windows" else "bin") / ("python.exe" if platform.system() == "Windows" else "python")
        
        if not venv_path.exists():
            return HealthCheckResult(
                name="虚拟环境",
                status=HealthStatus.ERROR,
                message="虚拟环境不存在",
                auto_fix_available=True,
                fix_command="uv venv .venv",
                fix_description="创建虚拟环境"
            )
        
        if not python_exec.exists():
            return HealthCheckResult(
                name="虚拟环境",
                status=HealthStatus.ERROR,
                message="虚拟环境Python解释器不存在",
                auto_fix_available=True,
                fix_command="uv venv .venv",
                fix_description="重新创建虚拟环境"
            )
        
        return HealthCheckResult(
            name="虚拟环境",
            status=HealthStatus.HEALTHY,
            message="虚拟环境正常 ✅"
        )
    
    def _check_dependencies(self) -> HealthCheckResult:
        """检查依赖"""
        requirements_file = self.project_root / "requirements.txt"
        
        if not requirements_file.exists():
            return HealthCheckResult(
                name="项目依赖",
                status=HealthStatus.WARNING,
                message="requirements.txt不存在",
                auto_fix_available=False,
                fix_description="请创建requirements.txt文件"
            )
        
        # 检查关键依赖
        critical_deps = ['typer', 'rich', 'duckdb', 'polars']
        missing_deps = []
        
        for dep in critical_deps:
            try:
                importlib.import_module(dep)
            except ImportError:
                missing_deps.append(dep)
        
        if missing_deps:
            return HealthCheckResult(
                name="项目依赖",
                status=HealthStatus.ERROR,
                message=f"缺少关键依赖: {', '.join(missing_deps)}",
                auto_fix_available=True,
                fix_command="uv pip sync requirements.txt",
                fix_description="同步项目依赖"
            )
        
        return HealthCheckResult(
            name="项目依赖",
            status=HealthStatus.HEALTHY,
            message="依赖完整 ✅"
        )
    
    def _check_configuration(self) -> HealthCheckResult:
        """检查配置文件"""
        config_file = self.project_root / "config" / "settings.toml"
        
        if not config_file.exists():
            return HealthCheckResult(
                name="配置文件",
                status=HealthStatus.CRITICAL,
                message="settings.toml配置文件不存在",
                auto_fix_available=False,
                fix_description="请恢复config/settings.toml文件"
            )
        
        try:
            import toml
            with open(config_file, 'r', encoding='utf-8') as f:
                config = toml.load(f)
            
            # 检查关键配置节
            required_sections = ['app', 'platform', 'dev', 'test', 'prod']
            missing_sections = [section for section in required_sections if section not in config]
            
            if missing_sections:
                return HealthCheckResult(
                    name="配置文件",
                    status=HealthStatus.ERROR,
                    message=f"配置文件缺少必要节: {', '.join(missing_sections)}",
                    auto_fix_available=False,
                    fix_description="请检查并修复配置文件"
                )
            
            return HealthCheckResult(
                name="配置文件",
                status=HealthStatus.HEALTHY,
                message="配置文件正常 ✅"
            )
        
        except Exception as e:
            return HealthCheckResult(
                name="配置文件",
                status=HealthStatus.ERROR,
                message=f"配置文件解析错误: {str(e)}",
                auto_fix_available=False,
                fix_description="请检查配置文件语法"
            )
    
    def _check_database(self) -> HealthCheckResult:
        """检查数据库"""
        try:
            from utils.config_loader import ConfigLoader
            config_loader = ConfigLoader()
            # 使用正确的方法获取数据库配置
            db_config = config_loader.get_database_config('dev')
            db_path_str = db_config.get('path', 'data/aqua_dev.duckdb')
            # 展开路径并处理占位符
            db_path = config_loader.expand_cross_platform_path(db_path_str)
            
            # 检查数据库目录
            db_dir = db_path.parent
            if not db_dir.exists():
                return HealthCheckResult(
                    name="数据库",
                    status=HealthStatus.ERROR,
                    message=f"数据库目录不存在: {db_dir}",
                    auto_fix_available=True,
                    fix_command=f"mkdir -p {db_dir}",
                    fix_description="创建数据库目录"
                )
            
            # 检查数据库文件
            if not db_path.exists():
                return HealthCheckResult(
                    name="数据库",
                    status=HealthStatus.WARNING,
                    message="数据库文件不存在",
                    auto_fix_available=True,
                    fix_command="create_duckdb",
                    fix_description="创建数据库文件"
                )
            
            # 测试数据库连接
            try:
                import duckdb
                conn = duckdb.connect(str(db_path))
                conn.execute("SELECT 1")
                conn.close()
                
                return HealthCheckResult(
                    name="数据库",
                    status=HealthStatus.HEALTHY,
                    message="数据库连接正常 ✅"
                )
            except Exception as e:
                return HealthCheckResult(
                    name="数据库",
                    status=HealthStatus.ERROR,
                    message=f"数据库连接失败: {str(e)}",
                    auto_fix_available=True,
                    fix_command="recreate_duckdb",
                    fix_description="重新创建数据库文件"
                )
        
        except Exception as e:
            return HealthCheckResult(
                name="数据库",
                status=HealthStatus.ERROR,
                message=f"数据库检查失败: {str(e)}",
                auto_fix_available=False
            )
    
    def _check_data_sources(self) -> HealthCheckResult:
        """检查数据源配置"""
        issues = []
        warnings = []
        
        # 检查TUSHARE TOKEN - 更健壮的检测
        tushare_token = os.getenv('TUSHARE_TOKEN')
        if not tushare_token:
            warnings.append("TUSHARE_TOKEN未配置")
        elif len(tushare_token.strip()) < 20:
            warnings.append("TUSHARE_TOKEN格式可能无效（长度过短）")
        else:
            # 尝试验证TUSHARE连接
            try:
                import tushare as ts
                test_api = ts.pro_api(tushare_token)
                # 执行简单的连接测试
                test_result = test_api.stock_basic(list_status="L", limit=1)
                if test_result is None or len(test_result) == 0:
                    warnings.append("TUSHARE_TOKEN连接测试失败")
            except ImportError:
                warnings.append("tushare库未安装")
            except Exception as e:
                warnings.append(f"TUSHARE连接测试失败: {str(e)[:50]}")
        
        # 检查CSV数据路径 - 使用配置加载器获取正确路径
        try:
            from utils.config_loader import ConfigLoader
            config_loader = ConfigLoader()
            platform_info = config_loader.get_platform_info()
            
            # 获取平台特定的CSV路径
            csv_paths = []
            if platform_info["is_windows"]:
                csv_paths = [
                    os.getenv('CSV_DATA_PATH'),
                    'D:/Data/RAW/FromC2C',
                    'D:/Data/duckdb/AQUA/DataCenter/datasources/csv'
                ]
            else:
                csv_paths = [
                    os.getenv('CSV_DATA_PATH'),
                    '~/Documents/Data/FromC2C',
                    '~/Documents/Data/duckdb/AQUA/DataCenter/datasources/csv'
                ]
            
            csv_available = False
            for path in csv_paths:
                if path:
                    expanded_path = Path(path).expanduser()
                    if expanded_path.exists():
                        csv_available = True
                        break
            
            if not csv_available:
                warnings.append("CSV数据源路径未配置或不存在")
        except Exception as e:
            warnings.append(f"CSV路径检查失败: {str(e)[:50]}")
        
        # 检查MySQL配置
        try:
            import pymysql
            mysql_available = True
        except ImportError:
            mysql_available = False
            warnings.append("MySQL驱动未安装（可选）")
        
        # 评估整体状态
        if issues:
            return HealthCheckResult(
                name="数据源",
                status=HealthStatus.ERROR,
                message=f"数据源配置错误: {'; '.join(issues)}",
                auto_fix_available=False,
                fix_description="请检查数据源配置"
            )
        elif len(warnings) >= 3:  # 如果警告过多，降级为ERROR
            return HealthCheckResult(
                name="数据源",
                status=HealthStatus.ERROR,
                message=f"数据源配置问题过多: {'; '.join(warnings[:2])}等",
                auto_fix_available=False,
                fix_description="建议配置至少一个数据源（TUSHARE或CSV）"
            )
        elif warnings:
            return HealthCheckResult(
                name="数据源",
                status=HealthStatus.WARNING,
                message=f"数据源部分可用: {'; '.join(warnings[:2])}{'等' if len(warnings) > 2 else ''}",
                auto_fix_available=False,
                fix_description="建议配置更多数据源提高可用性"
            )
        else:
            return HealthCheckResult(
                name="数据源",
                status=HealthStatus.HEALTHY,
                message="数据源配置正常 ✅"
            )
    
    def _check_network_connectivity(self) -> HealthCheckResult:
        """检查网络连接"""
        test_urls = [
            ("PyPI镜像", "https://pypi.tuna.tsinghua.edu.cn/simple", 3),
            ("Gitee", "https://gitee.com", 5),
            ("TUSHARE", "https://tushare.pro", 3)
        ]
        
        failed_connections = []
        slow_connections = []
        
        import urllib.request
        import time
        
        for name, url, timeout in test_urls:
            try:
                start_time = time.time()
                with urllib.request.urlopen(url, timeout=timeout) as response:
                    response_time = time.time() - start_time
                    if response_time > timeout * 0.8:
                        slow_connections.append(f"{name} ({response_time:.1f}s)")
            except Exception:
                failed_connections.append(name)
        
        if failed_connections:
            return HealthCheckResult(
                name="网络连接",
                status=HealthStatus.WARNING,
                message=f"部分网络连接失败: {', '.join(failed_connections)}",
                details=f"慢速连接: {', '.join(slow_connections)}" if slow_connections else None,
                auto_fix_available=False,
                fix_description="请检查网络连接和防火墙设置"
            )
        elif slow_connections:
            return HealthCheckResult(
                name="网络连接",
                status=HealthStatus.WARNING,
                message=f"网络连接较慢: {', '.join(slow_connections)}",
                auto_fix_available=False,
                fix_description="建议使用镜像源或检查网络质量"
            )
        else:
            return HealthCheckResult(
                name="网络连接",
                status=HealthStatus.HEALTHY,
                message="网络连接正常 ✅"
            )
    
    def _check_disk_space(self) -> HealthCheckResult:
        """检查磁盘空间"""
        try:
            import shutil
            
            # 检查项目根目录磁盘空间
            total, used, free = shutil.disk_usage(self.project_root)
            free_gb = free / (1024**3)
            free_percent = (free / total) * 100
            
            if free_gb < 1:  # 小于1GB
                return HealthCheckResult(
                    name="磁盘空间",
                    status=HealthStatus.CRITICAL,
                    message=f"磁盘空间严重不足: {free_gb:.1f}GB ({free_percent:.1f}%)",
                    auto_fix_available=False,
                    fix_description="请释放磁盘空间"
                )
            elif free_gb < 5:  # 小于5GB
                return HealthCheckResult(
                    name="磁盘空间",
                    status=HealthStatus.WARNING,
                    message=f"磁盘空间不足: {free_gb:.1f}GB ({free_percent:.1f}%)",
                    auto_fix_available=False,
                    fix_description="建议释放磁盘空间"
                )
            else:
                return HealthCheckResult(
                    name="磁盘空间",
                    status=HealthStatus.HEALTHY,
                    message=f"磁盘空间充足: {free_gb:.1f}GB ({free_percent:.1f}%) ✅"
                )
        
        except Exception as e:
            return HealthCheckResult(
                name="磁盘空间",
                status=HealthStatus.ERROR,
                message=f"无法检查磁盘空间: {str(e)}",
                auto_fix_available=False
            )
    
    def _check_memory_usage(self) -> HealthCheckResult:
        """检查内存使用"""
        try:
            if platform.system() == "Windows":
                import psutil
                memory = psutil.virtual_memory()
                available_gb = memory.available / (1024**3)
                used_percent = memory.percent
            else:
                # macOS/Linux - 简化检查
                available_gb = 4.0  # 假设有足够内存
                used_percent = 50.0
            
            if available_gb < 0.5:  # 可用内存小于500MB
                return HealthCheckResult(
                    name="系统内存",
                    status=HealthStatus.WARNING,
                    message=f"可用内存不足: {available_gb:.1f}GB (使用率: {used_percent:.1f}%)",
                    auto_fix_available=False,
                    fix_description="建议关闭不必要的程序"
                )
            else:
                return HealthCheckResult(
                    name="系统内存",
                    status=HealthStatus.HEALTHY,
                    message=f"内存充足: {available_gb:.1f}GB可用 (使用率: {used_percent:.1f}%) ✅"
                )
        
        except Exception as e:
            return HealthCheckResult(
                name="系统内存",
                status=HealthStatus.WARNING,
                message=f"无法检查内存使用: {str(e)}",
                auto_fix_available=False
            )
    
    def _check_permissions(self) -> HealthCheckResult:
        """检查文件权限"""
        try:
            # 检查项目目录写权限
            test_file = self.project_root / "temp" / "permission_test.tmp"
            test_file.parent.mkdir(exist_ok=True)
            
            try:
                test_file.write_text("permission test")
                test_file.unlink()
                
                return HealthCheckResult(
                    name="文件权限",
                    status=HealthStatus.HEALTHY,
                    message="文件权限正常 ✅"
                )
            except PermissionError:
                return HealthCheckResult(
                    name="文件权限",
                    status=HealthStatus.ERROR,
                    message="项目目录写权限不足",
                    auto_fix_available=False,
                    fix_description="请检查项目目录权限设置"
                )
        
        except Exception as e:
            return HealthCheckResult(
                name="文件权限",
                status=HealthStatus.WARNING,
                message=f"权限检查失败: {str(e)}",
                auto_fix_available=False
            )
    
    def _check_platform_compatibility(self) -> HealthCheckResult:
        """检查平台兼容性"""
        system = platform.system()
        version = platform.release()
        
        compatibility_issues = []
        
        if system == "Windows":
            # Windows特定检查
            if int(version.split('.')[0]) < 10:
                compatibility_issues.append("Windows版本过低，建议Windows 10+")
            
            # 检查UTF-8支持
            try:
                test_str = "测试中文字符"
                test_str.encode('utf-8')
            except UnicodeEncodeError:
                compatibility_issues.append("UTF-8编码支持有问题")
        
        elif system == "Darwin":
            # macOS特定检查
            major_version = int(version.split('.')[0])
            if major_version < 19:  # macOS 10.15+
                compatibility_issues.append("macOS版本过低，建议macOS 10.15+")
        
        if compatibility_issues:
            return HealthCheckResult(
                name="平台兼容性",
                status=HealthStatus.WARNING,
                message=f"平台兼容性问题: {'; '.join(compatibility_issues)}",
                auto_fix_available=False,
                fix_description="请升级操作系统或检查兼容性设置"
            )
        else:
            return HealthCheckResult(
                name="平台兼容性",
                status=HealthStatus.HEALTHY,
                message=f"{system} {version} 兼容性良好 ✅"
            )
    
    def _check_service_status(self) -> HealthCheckResult:
        """检查服务状态"""
        try:
            from aqua.cli.service_manager import ServiceManager
            
            procfile_path = self.project_root / "Procfile.dev"
            if not procfile_path.exists():
                return HealthCheckResult(
                    name="服务配置",
                    status=HealthStatus.WARNING,
                    message="Procfile.dev不存在",
                    auto_fix_available=False,
                    fix_description="请创建Procfile.dev服务配置文件"
                )
            
            service_manager = ServiceManager(procfile_path)
            is_running = service_manager.is_running()
            
            return HealthCheckResult(
                name="服务状态",
                status=HealthStatus.HEALTHY if not is_running else HealthStatus.HEALTHY,
                message=f"服务状态: {'运行中' if is_running else '已停止'} ✅"
            )
        
        except Exception as e:
            return HealthCheckResult(
                name="服务状态",
                status=HealthStatus.WARNING,
                message=f"无法检查服务状态: {str(e)}",
                auto_fix_available=False
            )
    
    def _apply_auto_fix(self, result: HealthCheckResult) -> Optional[str]:
        """应用自动修复"""
        if not result.fix_command:
            return None
        
        try:
            self.console.print(f"🔧 [yellow]自动修复: {result.name}[/yellow]")
            
            # 解析修复命令
            if result.fix_command.startswith("uv "):
                # UV命令
                cmd_parts = result.fix_command.split()
                subprocess.run(cmd_parts, check=True, cwd=self.project_root)
            elif result.fix_command.startswith("mkdir "):
                # 创建目录
                dir_path = result.fix_command.replace("mkdir -p ", "").replace("mkdir ", "")
                Path(dir_path).mkdir(parents=True, exist_ok=True)
            elif result.fix_command.startswith("touch "):
                # 创建文件
                file_path = result.fix_command.replace("touch ", "")
                Path(file_path).touch()
            elif result.fix_command == "create_duckdb":
                # 创建DuckDB数据库文件
                from utils.config_loader import ConfigLoader
                config_loader = ConfigLoader()
                db_config = config_loader.get_database_config('dev')
                db_path_str = db_config.get('path', 'data/aqua_dev.duckdb')
                db_path = config_loader.expand_cross_platform_path(db_path_str)
                
                # 确保目录存在
                db_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 创建DuckDB数据库
                import duckdb
                conn = duckdb.connect(str(db_path))
                conn.execute("CREATE TABLE IF NOT EXISTS test_table (id INTEGER, name VARCHAR)")
                conn.close()
                
            elif result.fix_command == "recreate_duckdb":
                # 重新创建DuckDB数据库文件
                from utils.config_loader import ConfigLoader
                config_loader = ConfigLoader()
                db_config = config_loader.get_database_config('dev')
                db_path_str = db_config.get('path', 'data/aqua_dev.duckdb')
                db_path = config_loader.expand_cross_platform_path(db_path_str)
                
                # 删除损坏的数据库文件
                if db_path.exists():
                    db_path.unlink()
                
                # 确保目录存在
                db_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 重新创建DuckDB数据库
                import duckdb
                conn = duckdb.connect(str(db_path))
                conn.execute("CREATE TABLE IF NOT EXISTS test_table (id INTEGER, name VARCHAR)")
                conn.close()
                
            elif " && " in result.fix_command:
                # 复合命令
                commands = result.fix_command.split(" && ")
                for cmd in commands:
                    if cmd.startswith("rm "):
                        file_path = cmd.replace("rm ", "")
                        Path(file_path).unlink(missing_ok=True)
                    elif cmd.startswith("touch "):
                        file_path = cmd.replace("touch ", "")
                        Path(file_path).touch()
            
            return result.fix_description
        
        except Exception as e:
            self.console.print(f"❌ [red]自动修复失败: {str(e)}[/red]")
            return None
    
    def _collect_performance_metrics(self) -> Dict[str, Any]:
        """收集性能指标"""
        metrics = {}
        
        try:
            # Python进程内存使用
            import psutil
            process = psutil.Process()
            metrics['memory_usage_mb'] = process.memory_info().rss / (1024 * 1024)
            metrics['cpu_percent'] = process.cpu_percent()
        except:
            metrics['memory_usage_mb'] = 0
            metrics['cpu_percent'] = 0
        
        # 项目大小
        try:
            total_size = sum(f.stat().st_size for f in self.project_root.rglob('*') if f.is_file())
            metrics['project_size_mb'] = total_size / (1024 * 1024)
        except:
            metrics['project_size_mb'] = 0
        
        return metrics
    
    def display_report(self, report: SystemReport):
        """显示健康检查报告"""
        # 总体状态
        status_colors = {
            HealthStatus.HEALTHY: "green",
            HealthStatus.WARNING: "yellow", 
            HealthStatus.ERROR: "red",
            HealthStatus.CRITICAL: "bright_red"
        }
        
        status_icons = {
            HealthStatus.HEALTHY: "✅",
            HealthStatus.WARNING: "⚠️",
            HealthStatus.ERROR: "❌",
            HealthStatus.CRITICAL: "🚨"
        }
        
        overall_color = status_colors[report.overall_status]
        overall_icon = status_icons[report.overall_status]
        
        self.console.print(Panel.fit(
            f"{overall_icon} [bold {overall_color}]系统状态: {report.overall_status.value.upper()}[/bold {overall_color}]\n\n"
            f"检查项目: {len(report.checks)}\n"
            f"自动修复: {len(report.auto_fixes_applied)}\n"
            f"需要手动处理: {len(report.manual_actions_needed)}",
            title="健康检查总览",
            border_style=overall_color
        ))
        
        # 详细检查结果
        table = Table(title="详细检查结果")
        table.add_column("检查项目", style="cyan")
        table.add_column("状态", justify="center")
        table.add_column("结果", style="white")
        table.add_column("备注", style="dim")
        
        for check in report.checks:
            status_icon = status_icons[check.status]
            status_color = status_colors[check.status]
            
            table.add_row(
                check.name,
                f"[{status_color}]{status_icon}[/{status_color}]",
                check.message,
                check.details or ""
            )
        
        self.console.print(table)
        
        # 自动修复结果
        if report.auto_fixes_applied:
            self.console.print("\n🔧 [green]自动修复完成:[/green]")
            for fix in report.auto_fixes_applied:
                self.console.print(f"  ✅ {fix}")
        
        # 手动操作建议
        if report.manual_actions_needed:
            self.console.print("\n📋 [yellow]需要手动处理:[/yellow]")
            for action in report.manual_actions_needed:
                self.console.print(f"  • {action}")
        
        # 性能指标
        if report.performance_metrics:
            self.console.print("\n📊 [cyan]性能指标:[/cyan]")
            metrics_table = Table()
            metrics_table.add_column("指标", style="cyan")
            metrics_table.add_column("数值", style="green")
            
            for key, value in report.performance_metrics.items():
                if isinstance(value, float):
                    display_value = f"{value:.1f}"
                else:
                    display_value = str(value)
                metrics_table.add_row(key.replace('_', ' ').title(), display_value)
            
            self.console.print(metrics_table)
        
        # 结论和建议
        if report.overall_status == HealthStatus.HEALTHY:
            self.console.print(Panel.fit(
                "🎉 [bold green]系统运行状态良好！[/bold green]\n\n"
                "所有关键组件都正常工作。\n"
                "建议定期运行健康检查以保持系统稳定性。",
                title="系统健康",
                border_style="green"
            ))
        else:
            self.console.print(Panel.fit(
                f"{overall_icon} [bold {overall_color}]发现系统问题[/bold {overall_color}]\n\n"
                "请根据上述建议进行修复。\n"
                "如需帮助，请查看文档或联系支持。",
                title="需要注意",
                border_style=overall_color
            ))


def doctor_command(
    ctx: typer.Context,
    auto_fix: bool = typer.Option(True, "--auto-fix/--no-auto-fix", help="启用自动修复"),
    detailed: bool = typer.Option(False, "--detailed", help="显示详细信息")
):
    """系统健康检查和自动修复"""
    checker = HealthChecker()
    report = checker.run_full_check(auto_fix=auto_fix)
    checker.display_report(report)
    
    # 保存到上下文
    if ctx.obj:
        ctx.obj.health_report = report
    
    # 根据结果设置退出码
    if report.overall_status == HealthStatus.CRITICAL:
        raise typer.Exit(2)
    elif report.overall_status == HealthStatus.ERROR:
        raise typer.Exit(1)
    else:
        raise typer.Exit(0)