#!/usr/bin/env python3
"""
DuckDB数据库初始化检查器
提供数据库连接检查、结构验证、数据完整性检查等功能

特性：
- 基于数据字典自动创建表结构
- 支持数据库连接健康检查
- 提供完整的数据库管理接口
- 详细的错误处理和日志记录
- 自动备份和恢复功能
"""

import re
import logging
from pathlib import Path
from typing import Dict, List, Optional, NamedTuple
from dataclasses import dataclass

import toml

from .connection_manager import DuckDBConnectionManager
from ..utils.time_utils import get_beijing_time_now


class CheckResult(NamedTuple):
    """检查结果"""

    success: bool
    message: str
    data: Optional[Dict] = None


@dataclass
class TableSchema:
    """表结构定义"""

    name: str
    columns: List[Dict[str, str]]
    constraints: List[str] = None
    indexes: List[str] = None


class DuckDBInitChecker:
    """DuckDB数据库初始化检查器"""

    def __init__(
        self,
        config_path: str = "config/settings.toml",
        environment: str = "test",
        dict_path: str = "docs/database/DATA_DICTIONARY.md",
    ):
        """
        初始化检查器

        Args:
            config_path: 配置文件路径
            environment: 环境名称
            dict_path: 数据字典文件路径
        """
        self.config_path = Path(config_path)
        self.environment = environment
        self.dict_path = Path(dict_path)

        # 加载配置
        self.config = self._load_config()

        # 初始化日志
        self.logger = logging.getLogger(__name__)

        # 初始化连接管理器
        self.connection_manager = DuckDBConnectionManager(environment)

        # 表结构缓存
        self._table_schemas: Optional[List[TableSchema]] = None

    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")

            with open(self.config_path, "r", encoding="utf-8") as f:
                config = toml.load(f)

            if self.environment not in config:
                raise KeyError(f"环境配置不存在: {self.environment}")

            return config[self.environment]

        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise

    def _parse_data_dictionary(self) -> List[TableSchema]:
        """解析数据字典文件"""
        if not self.dict_path.exists():
            raise FileNotFoundError(f"数据字典文件不存在: {self.dict_path}")

        with open(self.dict_path, "r", encoding="utf-8") as f:
            content = f.read()

        tables = []

        # 解析表结构 - 只匹配数据表定义段落
        table_pattern = r"### (\w+)\s*\n([^#]*?)(?=### |## |###|\Z)"
        table_matches = re.findall(table_pattern, content, re.DOTALL)

        for table_name, table_content in table_matches:
            # 跳过非表定义的section
            if "表" not in table_content and "字段" not in table_content:
                continue

            # 跳过非实际表名的section
            if table_name in [
                "表关系说明",
                "数据量预估表格",
                "主要索引",
                "表创建SQL示例",
                "维护说明",
            ]:
                continue

            # 解析列定义
            columns = []
            column_pattern = (
                r"\|\s*(\w+)\s*\|\s*(\w+(?:\(\d+,?\d*\))?)\s*\|\s*([^|]*)\s*\|"
            )
            column_matches = re.findall(column_pattern, table_content)

            for col_name, col_type, col_desc in column_matches:
                # 跳过表头行
                if col_name.lower() in ["字段名", "column", "field", "表名", "table"]:
                    continue

                # 跳过分隔符行
                if col_name.strip().startswith("-"):
                    continue

                # 清理和转换数据类型
                col_type = col_type.strip()
                col_desc = col_desc.strip()

                # 转换为DuckDB类型
                duckdb_type = self._convert_to_duckdb_type(col_type)

                columns.append(
                    {"name": col_name, "type": duckdb_type, "description": col_desc}
                )

            if columns:  # 只有当解析到列时才添加表
                tables.append(
                    TableSchema(
                        name=table_name, columns=columns, constraints=[], indexes=[]
                    )
                )

        return tables

    def _convert_to_duckdb_type(self, original_type: str) -> str:
        """将原始类型转换为DuckDB类型"""
        type_mapping = {
            "varchar": "VARCHAR",
            "text": "VARCHAR",
            "int": "INTEGER",
            "integer": "INTEGER",
            "bigint": "BIGINT",
            "float": "DOUBLE",
            "double": "DOUBLE",
            "decimal": "DECIMAL",
            "datetime": "TIMESTAMP",
            "timestamp": "TIMESTAMP",
            "date": "DATE",
            "time": "TIME",
            "bool": "BOOLEAN",
            "boolean": "BOOLEAN",
            "json": "JSON",
        }

        # 处理带长度的类型
        if "(" in original_type:
            base_type = original_type.split("(")[0].lower()
            if base_type in type_mapping:
                return original_type.upper()

        return type_mapping.get(original_type.lower(), "VARCHAR")

    def get_table_schemas(self) -> List[TableSchema]:
        """获取表结构列表"""
        if self._table_schemas is None:
            self._table_schemas = self._parse_data_dictionary()
        return self._table_schemas

    def check_database_connection(self) -> CheckResult:
        """检查数据库连接"""
        try:
            with self.connection_manager.get_cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()

                if result and result[0] == 1:
                    return CheckResult(
                        success=True,
                        message="数据库连接正常",
                        data={
                            "database_path": self.connection_manager._get_database_path()
                        },
                    )
                else:
                    return CheckResult(success=False, message="数据库连接测试失败")
        except Exception as e:
            return CheckResult(success=False, message=f"数据库连接失败: {str(e)}")

    def validate_database_structure(self) -> CheckResult:
        """验证数据库结构"""
        try:
            expected_tables = self.get_table_schemas()
            if not expected_tables:
                return CheckResult(success=False, message="数据字典中未找到表定义")

            existing_tables = self.connection_manager.get_all_tables()

            missing_tables = []
            for expected_table in expected_tables:
                if expected_table.name not in existing_tables:
                    missing_tables.append(expected_table.name)

            if missing_tables:
                return CheckResult(
                    success=False,
                    message=f"缺少表: {', '.join(missing_tables)}",
                    data={"missing_tables": missing_tables},
                )

            # 验证表结构
            structure_issues = []
            for expected_table in expected_tables:
                table_info = self.connection_manager.get_table_info(expected_table.name)
                existing_columns = {
                    col["column_name"]: col["column_type"] for col in table_info
                }

                for expected_col in expected_table.columns:
                    col_name = expected_col["name"]
                    if col_name not in existing_columns:
                        structure_issues.append(
                            f"表 {expected_table.name} 缺少列 {col_name}"
                        )

            if structure_issues:
                return CheckResult(
                    success=False,
                    message=f"表结构不匹配: {'; '.join(structure_issues)}",
                    data={"structure_issues": structure_issues},
                )

            return CheckResult(
                success=True,
                message="数据库结构验证通过",
                data={"table_count": len(existing_tables)},
            )

        except Exception as e:
            return CheckResult(success=False, message=f"数据库结构验证失败: {str(e)}")

    def check_data_integrity(self) -> CheckResult:
        """检查数据完整性"""
        try:
            tables = self.connection_manager.get_all_tables()
            integrity_report = {}

            for table_name in tables:
                count = self.connection_manager.get_table_count(table_name)
                integrity_report[table_name] = {
                    "record_count": count,
                    "status": "ok" if count >= 0 else "error",
                }

            total_records = sum(
                report["record_count"] for report in integrity_report.values()
            )

            return CheckResult(
                success=True,
                message=f"数据完整性检查完成，总记录数: {total_records:,}",
                data={
                    "table_reports": integrity_report,
                    "total_records": total_records,
                },
            )

        except Exception as e:
            return CheckResult(success=False, message=f"数据完整性检查失败: {str(e)}")

    def _check_production_safety(self, force: bool = False) -> CheckResult:
        """检查生产环境安全性"""
        if self.environment == "prod":
            db_path = Path(self.config.get("database", {}).get("path", ""))

            # 检查是否为生产数据库文件
            if "DataCenter.duckdb" in str(db_path):
                if not force:
                    return CheckResult(
                        success=False,
                        message="生产环境数据库操作需要显式force=True参数",
                    )

                # 生产环境需要三次确认
                self.logger.warning("⚠️ 生产环境数据库初始化操作")
                return CheckResult(
                    success=True, message="生产环境安全检查通过，但需要用户三次确认"
                )

        return CheckResult(success=True, message="非生产环境，安全检查通过")

    def initialize_database(
        self, force: bool = False, production_confirmed: bool = False
    ) -> CheckResult:
        """初始化数据库

        Args:
            force: 是否强制重建已存在的表
            production_confirmed: 生产环境是否已经过三次确认
        """
        try:
            # 生产环境安全检查
            safety_check = self._check_production_safety(force)
            if not safety_check.success:
                return safety_check

            # 生产环境需要特殊确认
            if self.environment == "prod" and not production_confirmed:
                return CheckResult(
                    success=False,
                    message="生产环境数据库初始化需要通过专用接口并完成三次确认",
                )

            # 获取表结构
            table_schemas = self.get_table_schemas()
            if not table_schemas:
                return CheckResult(success=False, message="数据字典中未找到表定义")

            # 创建表
            created_tables = []
            for table_schema in table_schemas:
                try:
                    # 检查表是否已存在
                    if self.connection_manager.table_exists(table_schema.name):
                        if not force:
                            self.logger.info(f"表 {table_schema.name} 已存在，跳过创建")
                            continue
                        else:
                            # 强制模式下删除现有表
                            self.connection_manager.drop_table(table_schema.name)

                    # 构建CREATE TABLE语句
                    columns_sql = []
                    for column in table_schema.columns:
                        col_sql = f"{column['name']} {column['type']}"
                        columns_sql.append(col_sql)

                    if not columns_sql:
                        self.logger.warning(
                            f"表 {table_schema.name} 没有定义列，跳过创建"
                        )
                        continue

                    columns_joined = ",\n    ".join(columns_sql)
                    create_sql = (
                        f"CREATE TABLE {table_schema.name} (\n    {columns_joined}\n)"
                    )

                    # 执行创建表语句
                    with self.connection_manager.get_cursor() as cursor:
                        cursor.execute(create_sql)

                    created_tables.append(table_schema.name)
                    self.logger.info(f"创建表 {table_schema.name} 成功")

                except Exception as e:
                    self.logger.error(f"创建表 {table_schema.name} 失败: {e}")
                    return CheckResult(
                        success=False,
                        message=f"创建表 {table_schema.name} 失败: {str(e)}",
                    )

            # 记录操作审计日志
            self._log_database_operation(
                "initialize",
                {
                    "environment": self.environment,
                    "created_tables": created_tables,
                    "total_tables": len(table_schemas),
                    "force_mode": force,
                    "production_confirmed": production_confirmed,
                },
            )

            return CheckResult(
                success=True,
                message=f"数据库初始化完成，创建了 {len(created_tables)} 个表",
                data={
                    "created_tables": created_tables,
                    "total_tables": len(table_schemas),
                },
            )

        except Exception as e:
            # 记录失败的操作审计日志
            self._log_database_operation(
                "initialize_failed",
                {
                    "environment": self.environment,
                    "error": str(e),
                    "force_mode": force,
                    "production_confirmed": production_confirmed,
                },
            )

            return CheckResult(success=False, message=f"数据库初始化失败: {str(e)}")

    def _log_database_operation(self, operation: str, details: dict):
        """记录数据库操作审计日志"""
        try:
            import json
            from datetime import datetime

            audit_log = {
                "timestamp": datetime.now().isoformat(),
                "operation": operation,
                "environment": self.environment,
                "details": details,
            }

            # 确保日志目录存在
            log_dir = Path("logs/audit")
            log_dir.mkdir(parents=True, exist_ok=True)

            # 写入审计日志
            log_file = (
                log_dir
                / f"database_operations_{datetime.now().strftime('%Y%m%d')}.jsonl"
            )
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(audit_log, ensure_ascii=False) + "\n")

            self.logger.info(f"数据库操作已记录到审计日志: {log_file}")

        except Exception as e:
            self.logger.error(f"记录审计日志失败: {str(e)}")

    def initialize_production_database_with_confirmation(self) -> CheckResult:
        """生产环境数据库初始化（带三次确认）"""
        if self.environment != "prod":
            return CheckResult(success=False, message="此方法仅适用于生产环境")

        print("\n" + "=" * 60)
        print("⚠️  AQUA生产数据库初始化 - 安全确认")
        print("=" * 60)

        # 显示数据库路径
        db_path = Path(self.config.get("database", {}).get("path", ""))
        print(f"数据库文件: {db_path}")
        print(f"当前环境: {self.environment}")

        # 第一次确认
        try:
            confirm1 = input(
                "\n[1/3] 确认要初始化生产数据库吗？这将影响生产数据！(yes/no): "
            )
            if confirm1.lower() != "yes":
                return CheckResult(success=False, message="用户取消操作（第一次确认）")

            # 第二次确认
            confirm2 = input(
                "\n[2/3] 再次确认：您了解这个操作的风险吗？(I understand/no): "
            )
            if confirm2 != "I understand":
                return CheckResult(success=False, message="用户取消操作（第二次确认）")

            # 第三次确认
            confirm3 = input("\n[3/3] 最终确认：请输入 'INIT PROD DB' 来确认执行: ")
            if confirm3 != "INIT PROD DB":
                return CheckResult(success=False, message="用户取消操作（第三次确认）")

        except (KeyboardInterrupt, EOFError):
            return CheckResult(success=False, message="用户中断操作")

        print("\n✅ 三次确认完成，开始初始化生产数据库...")

        # 执行初始化
        result = self.initialize_database(force=True, production_confirmed=True)

        # 额外的生产环境日志
        if result.success:
            print("\n🎉 生产数据库初始化完成！")
            print("📋 请检查审计日志确认操作记录")

        return result

    def _ensure_production_backup(self) -> CheckResult:
        """确保生产环境数据库备份"""
        if self.environment == "prod":
            db_path = Path(self.config.get("database", {}).get("path", ""))
            if db_path.exists():
                # 强制备份生产数据库
                backup_result = self.backup_database()
                if not backup_result.success:
                    return CheckResult(
                        success=False,
                        message=f"生产环境强制备份失败: {backup_result.message}",
                    )
                self.logger.info(f"生产环境自动备份完成: {backup_result.message}")

        return CheckResult(success=True, message="备份检查完成")

    def backup_database(self) -> CheckResult:
        """备份数据库"""
        try:
            backup_dir = Path(
                self.config.get("database", {}).get("backup_dir", "data/backup")
            )
            backup_dir.mkdir(parents=True, exist_ok=True)

            db_path = Path(self.connection_manager._get_database_path())
            if not db_path.exists():
                return CheckResult(success=False, message="数据库文件不存在，无法备份")

            # 创建备份文件名
            timestamp = get_beijing_time_now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_dir / f"aqua_backup_{timestamp}.duckdb"

            # 复制数据库文件
            import shutil

            shutil.copy2(db_path, backup_file)

            return CheckResult(
                success=True,
                message=f"数据库备份成功: {backup_file}",
                data={"backup_file": str(backup_file)},
            )

        except Exception as e:
            return CheckResult(success=False, message=f"数据库备份失败: {str(e)}")

    def get_database_health_report(self) -> Dict:
        """获取数据库健康报告"""
        report = {
            "check_time": get_beijing_time_now().isoformat(),
            "environment": self.environment,
            "database_path": self.connection_manager._get_database_path(),
            "connection_status": "unknown",
            "structure_status": "unknown",
            "integrity_status": "unknown",
            "table_count": 0,
            "total_records": 0,
            "issues": [],
        }

        # 检查连接
        conn_result = self.check_database_connection()
        report["connection_status"] = "ok" if conn_result.success else "error"
        if not conn_result.success:
            report["issues"].append(f"连接问题: {conn_result.message}")

        # 检查结构
        struct_result = self.validate_database_structure()
        report["structure_status"] = "ok" if struct_result.success else "error"
        if not struct_result.success:
            report["issues"].append(f"结构问题: {struct_result.message}")
        elif struct_result.data:
            report["table_count"] = struct_result.data.get("table_count", 0)

        # 检查数据完整性
        integrity_result = self.check_data_integrity()
        report["integrity_status"] = "ok" if integrity_result.success else "error"
        if not integrity_result.success:
            report["issues"].append(f"完整性问题: {integrity_result.message}")
        elif integrity_result.data:
            report["total_records"] = integrity_result.data.get("total_records", 0)

        return report

    def display_health_report(self):
        """显示健康报告"""
        report = self.get_database_health_report()

        print("\n" + "=" * 60)
        print("AQUA数据库健康报告".center(60))
        print("=" * 60)

        print(f"检查时间: {report['check_time']}")
        print(f"环境: {report['environment']}")
        print(f"数据库路径: {report['database_path']}")
        print()

        # 状态显示
        status_symbols = {"ok": "✅", "error": "❌", "unknown": "❓"}
        print(
            f"连接状态: {status_symbols.get(report['connection_status'], '❓')} {report['connection_status']}"
        )
        print(
            f"结构状态: {status_symbols.get(report['structure_status'], '❓')} {report['structure_status']}"
        )
        print(
            f"完整性状态: {status_symbols.get(report['integrity_status'], '❓')} {report['integrity_status']}"
        )
        print()

        # 统计信息
        print(f"表数量: {report['table_count']}")
        print(f"总记录数: {report['total_records']:,}")

        # 问题列表
        if report["issues"]:
            print("\n⚠️  发现问题:")
            for issue in report["issues"]:
                print(f"  - {issue}")
        else:
            print("\n🎉 数据库状态正常！")

        print("=" * 60)

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.connection_manager.close_connection()
