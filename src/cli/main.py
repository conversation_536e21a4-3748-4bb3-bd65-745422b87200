"""
AQUA CLI主入口点
基于Click框架的现代化CLI界面
"""
import sys
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.traceback import install

# 安装Rich traceback处理
install(show_locals=True)

# 初始化Rich控制台
console = Console()

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.config_loader import ConfigLoader
from src.utils.logger import get_logger
from src.utils.exceptions import AquaException


# 全局配置和日志
config_loader = ConfigLoader()
# 暂时延迟日志初始化
logger = None


class AquaConfig:
    """AQUA CLI全局配置"""
    
    def __init__(self):
        self.verbose = False
        self.config_file = None
        self.console = console


# 全局配置对象
pass_config = click.make_pass_decorator(AquaConfig, ensure=True)


@click.group(context_settings={'help_option_names': ['-h', '--help']})
@click.option('--verbose', '-v', is_flag=True, help='启用详细输出')
@click.option('--config-file', '-c', type=click.Path(exists=True), 
              help='指定配置文件路径')
@click.version_option(version='1.0.0', prog_name='AQUA CLI')
@pass_config
def aqua(config: AquaConfig, verbose: bool, config_file: Optional[str]):
    """
    AQUA量化分析平台命令行工具
    
    个人开发者专用的跨平台金融数据管理和分析系统
    """
    config.verbose = verbose
    config.config_file = config_file
    
    if verbose:
        console.print("[green]AQUA CLI 启动成功 - 详细模式[/green]")
        # 初始化日志
        global logger
        logger = get_logger(__name__)
        logger.setLevel("DEBUG")
    
    # 加载配置文件
    if config_file:
        try:
            config_loader.load_config(config_file)
            if verbose:
                console.print(f"[blue]已加载配置文件: {config_file}[/blue]")
        except Exception as e:
            console.print(f"[red]配置文件加载失败: {e}[/red]")
            sys.exit(1)


# 导入并注册子命令
try:
    from .commands.collect import collect_command
    from .commands.status import status_command
    from .commands.init import init_command
    from .commands.config import config_command
    from .commands.export import export_command
    from .commands.analyze import analyze_command
    
    aqua.add_command(collect_command, name='collect')
    aqua.add_command(status_command, name='status')
    aqua.add_command(init_command, name='init')
    aqua.add_command(config_command, name='config')
    aqua.add_command(export_command, name='export')
    aqua.add_command(analyze_command, name='analyze')
    
except ImportError as e:
    # 命令还未实现时的处理
    if logger:
        logger.debug(f"部分命令未实现: {e}")
    else:
        # 静默处理导入错误
        pass


@aqua.command()
@pass_config
def version(config: AquaConfig):
    """显示版本信息"""
    config.console.print("[bold blue]AQUA CLI v1.0.0[/bold blue]")
    config.console.print("个人开发者量化分析平台")
    config.console.print("支持平台: Windows 10/11, macOS 10.15+")


def handle_exceptions():
    """全局异常处理"""
    def exception_handler(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, AquaException):
            console.print(f"[red]错误: {exc_value}[/red]")
            if config_loader.get('debug', False):
                console.print_exception()
        else:
            console.print(f"[red]未知错误: {exc_value}[/red]")
            console.print_exception()
        sys.exit(1)
    
    sys.excepthook = exception_handler


def main():
    """CLI主入口"""
    handle_exceptions()
    try:
        aqua()
    except KeyboardInterrupt:
        console.print("\n[yellow]用户中断操作[/yellow]")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]CLI启动失败: {e}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    main()