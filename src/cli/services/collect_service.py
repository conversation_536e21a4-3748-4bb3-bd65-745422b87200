"""
数据采集服务 - Real Data Only版本
完全移除Mock机制，仅使用真实数据源
"""
import sys
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import yaml

from rich.console import Console
from rich.progress import Progress, TaskID
from rich.table import Table

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.data_import.extractors.tushare_extractor import TushareExtractor
from src.data_import.fromC2C_csv_main_contract_importer import FromC2C_csv_main_contract_importer
from src.data_import.mysql_importer import MySQLImporter
from src.storage.unified_storage_manager import UnifiedStorageManager
from src.utils.config_loader import ConfigLoader
from src.utils.logger import get_logger
from src.utils.exceptions import AquaException
from src.config.data_source_config import (
    DataSourceConfig, 
    DataSourceNotConfiguredError, 
    DataSourceUnavailableError,
    DataCollectionError,
    UnsupportedDataSourceError
)


class CollectService:
    """数据采集服务 - Real Data Only版本"""
    
    def __init__(self):
        """初始化采集服务"""
        self.console = Console()
        self.config_loader = ConfigLoader()
        # 暂时禁用日志初始化
        self.logger = None
        
        # 数据源配置
        self.data_config = DataSourceConfig()
        
        # 初始化数据源（仅真实数据源）
        self._tushare_extractor = None
        self._csv_importer = None
        self._mysql_importer = None
        self._storage_manager = None
        
        # 性能优化配置
        self._batch_size = 100  # 个人开发者友好的批次大小
        self._max_concurrent = 2  # 个人环境并发限制
        
    def _get_tushare_extractor(self) -> TushareExtractor:
        """获取TushareExtractor实例 - 仅真实数据源"""
        if self._tushare_extractor is None:
            # 预检查：确保数据源已配置
            self.data_config.validate()
            
            try:
                # 使用真实TushareExtractor实例
                tushare_config = self.data_config.get_config_dict()
                self._tushare_extractor = TushareExtractor(tushare_config)
                
                # 验证连接
                if not self._tushare_extractor.is_connected():
                    raise DataSourceUnavailableError(
                        "TUSHARE数据源连接失败。\n"
                        "可能原因：\n"
                        "1. 网络连接问题\n"
                        "2. TUSHARE Token无效或过期\n"
                        "3. TUSHARE服务暂时不可用\n"
                        "4. 账户积分不足\n\n"
                        "解决方案：\n"
                        "- 检查网络连接\n"
                        "- 验证Token有效性\n"
                        "- 访问 https://tushare.pro/ 查看服务状态"
                    )
                    
            except Exception as e:
                raise DataSourceUnavailableError(f"TUSHARE数据源初始化失败: {e}")
        
        return self._tushare_extractor
    
    def _get_csv_importer(self) -> FromC2C_csv_main_contract_importer:
        """获取CSV导入器实例 - 仅真实数据源"""
        if self._csv_importer is None:
            try:
                # 使用真实FromC2C_csv_main_contract_importer实例
                self._csv_importer = FromC2C_csv_main_contract_importer()
                
            except Exception as e:
                raise DataSourceUnavailableError(f"CSV数据源初始化失败: {e}")
        
        return self._csv_importer
    
    def _get_mysql_importer(self) -> MySQLImporter:
        """获取MySQL导入器实例 - 仅真实数据源"""
        if self._mysql_importer is None:
            try:
                # 使用真实MySQLImporter实例
                self._mysql_importer = MySQLImporter()
                
            except Exception as e:
                raise DataSourceUnavailableError(f"MySQL数据源初始化失败: {e}")
                
        return self._mysql_importer
    
    def _get_storage_manager(self) -> UnifiedStorageManager:
        """获取存储管理器实例"""
        if self._storage_manager is None:
            try:
                # 使用真实UnifiedStorageManager实例
                storage_config = {
                    'database': {
                        'environments': {
                            'real_data': {
                                'path': 'data/aqua_real_data.duckdb',
                                'backup_dir': 'backups/real_data'
                            }
                        },
                        'default_environment': 'real_data'
                    },
                    'logs_root': 'logs/'
                }
                
                self._storage_manager = UnifiedStorageManager(storage_config)
            except Exception as e:
                raise DataCollectionError(f"存储管理器初始化失败: {e}")
        
        return self._storage_manager
    
    def check_capabilities(self, source: str = 'tushare') -> Dict[str, Any]:
        """检查数据源能力 - Real Data Only版本（支持多种真实数据源）"""
        capabilities = {
            'source': source,
            'supported_types': [],
            'supported_frequencies': [],
            'status': 'unknown',
            'data_source_type': 'real_only'
        }
        
        try:
            if source == 'tushare':
                # 检查配置
                if not self.data_config.is_configured:
                    capabilities.update({
                        'status': 'not_configured',
                        'error': '未配置TUSHARE_TOKEN环境变量'
                    })
                    return capabilities
                
                # 检查连接
                extractor = self._get_tushare_extractor()
                capabilities.update({
                    'supported_types': ['stock', 'futures'],
                    'supported_frequencies': ['daily', 'weekly', 'monthly', '1min', '5min'],
                    'status': 'available',
                    'connection_status': 'connected',
                    'api_limit': '120/min',
                    'data_source_type': 'real_webapi'
                })
                
                # 尝试获取账户信息
                try:
                    account_info = extractor.get_account_info() if hasattr(extractor, 'get_account_info') else {}
                    capabilities['account_info'] = account_info
                except:
                    pass
            
            elif source == 'csv':
                # 检查CSV导入器
                csv_importer = self._get_csv_importer()
                capabilities.update({
                    'supported_types': ['futures', 'stock'],
                    'supported_frequencies': ['daily', '1min', '5min', '15min'],
                    'status': 'available',
                    'connection_status': 'ready',
                    'data_source_type': 'real_localfile',
                    'description': 'FROMC2C格式CSV文件导入器'
                })
            
            elif source == 'mysql':
                # 检查MySQL导入器
                mysql_importer = self._get_mysql_importer()
                capabilities.update({
                    'supported_types': ['stock', 'futures', 'options', 'bonds'],
                    'supported_frequencies': ['daily', 'weekly', 'monthly'],
                    'status': 'available',
                    'connection_status': 'ready',
                    'data_source_type': 'real_database',
                    'description': 'MySQL数据库到DuckDB迁移器'
                })
                    
            else:
                raise UnsupportedDataSourceError(f"不支持的数据源: {source}")
                
        except (DataSourceNotConfiguredError, DataSourceUnavailableError) as e:
            capabilities.update({
                'status': 'error',
                'error': str(e)
            })
        except Exception as e:
            capabilities.update({
                'status': 'error',
                'error': f"检查数据源能力时发生错误: {e}"
            })
            
        return capabilities
    
    def preview_data(self, symbols: List[str], source: str = 'tushare', 
                    data_type: str = 'stock', freq: str = 'daily',
                    **kwargs) -> Dict[str, Any]:
        """预览数据概览 - 基于真实数据源"""
        preview_info = {
            'symbols': symbols,
            'source': source,
            'data_type': data_type,
            'frequency': freq,
            'estimated_rows': 0,
            'estimated_size': '0 MB',
            'time_range': 'N/A',
            'fields': [],
            'data_source_type': 'real_tushare'
        }
        
        try:
            # 预检查数据源
            if source not in ['tushare', 'csv', 'mysql']:
                raise UnsupportedDataSourceError(f"不支持的数据源: {source}")
            
            # 确保数据源可用
            capabilities = self.check_capabilities(source)
            if capabilities['status'] != 'available':
                raise DataSourceUnavailableError(f"数据源不可用: {capabilities.get('error', '未知错误')}")
            
            # 更新数据源类型
            preview_info['data_source_type'] = capabilities.get('data_source_type', 'real_only')
            
            # 估算数据量
            if symbols:
                symbol_count = len(symbols)
                
                # 根据频率估算行数
                if freq == 'daily':
                    days_per_symbol = 250  # 一年交易日
                elif freq == 'weekly': 
                    days_per_symbol = 52
                elif freq == 'monthly':
                    days_per_symbol = 12
                elif freq in ['1min', '5min', '15min', '30min', '60min']:
                    days_per_symbol = 250 * 240  # 每日240分钟
                else:
                    days_per_symbol = 250
                
                estimated_rows = symbol_count * days_per_symbol
                estimated_size_mb = estimated_rows * 0.5 / 1024  # 估算每行0.5KB
                
                preview_info.update({
                    'estimated_rows': estimated_rows,
                    'estimated_size': f'{estimated_size_mb:.1f} MB',
                    'symbols_count': symbol_count
                })
            
            # 获取字段信息
            if data_type == 'stock':
                preview_info['fields'] = ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'vol', 'amount']
            elif data_type == 'futures':
                preview_info['fields'] = ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'vol', 'amount', 'oi']
                    
        except Exception as e:
            preview_info['error'] = str(e)
            
        return preview_info
    
    def validate_parameters(self, symbols: List[str], source: str, data_type: str,
                          freq: str, **kwargs) -> Dict[str, Any]:
        """验证参数"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        # 验证数据源
        if source not in ['tushare', 'csv', 'mysql']:
            validation_result['errors'].append(f'不支持的数据源: {source}')
            validation_result['valid'] = False
        
        # 验证符号格式
        if symbols:
            for symbol in symbols:
                if data_type == 'stock':
                    # 简单的股票代码验证
                    if not (symbol.endswith('.SZ') or symbol.endswith('.SH')):
                        validation_result['warnings'].append(
                            f'股票代码 {symbol} 可能需要添加后缀 .SZ 或 .SH'
                        )
                        validation_result['suggestions'].append(
                            f'尝试: {symbol}.SZ 或 {symbol}.SH'
                        )
        
        # 验证日期参数
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        if start_date and end_date:
            try:
                start = datetime.strptime(start_date, '%Y-%m-%d')
                end = datetime.strptime(end_date, '%Y-%m-%d')
                if start > end:
                    validation_result['errors'].append('开始日期不能晚于结束日期')
                    validation_result['valid'] = False
            except ValueError:
                validation_result['errors'].append('日期格式错误，请使用 YYYY-MM-DD 格式')
                validation_result['valid'] = False
        
        # 验证数据源能力
        capabilities = self.check_capabilities(source)
        if capabilities['status'] != 'available':
            validation_result['errors'].append(f"数据源不可用: {capabilities.get('error', '未知错误')}")
            validation_result['valid'] = False
        
        return validation_result
    
    def parse_time_range(self, **kwargs) -> Dict[str, str]:
        """解析时间范围参数"""
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        
        # 处理相对时间
        if kwargs.get('last_days'):
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=kwargs['last_days'])).strftime('%Y-%m-%d')
        elif kwargs.get('last_weeks'):
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(weeks=kwargs['last_weeks'])).strftime('%Y-%m-%d')
        elif kwargs.get('last_months'):
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=kwargs['last_months']*30)).strftime('%Y-%m-%d')
        
        # 处理预设周期
        period = kwargs.get('period')
        if period:
            now = datetime.now()
            end_date = now.strftime('%Y-%m-%d')
            
            if period == 'ytd':  # Year to date
                start_date = f"{now.year}-01-01"
            elif period == 'qtd':  # Quarter to date
                quarter_start_month = ((now.month - 1) // 3) * 3 + 1
                start_date = f"{now.year}-{quarter_start_month:02d}-01"
            elif period == 'mtd':  # Month to date
                start_date = f"{now.year}-{now.month:02d}-01"
            elif period == 'wtd':  # Week to date
                week_start = now - timedelta(days=now.weekday())
                start_date = week_start.strftime('%Y-%m-%d')
        
        # 默认值
        if not start_date:
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
            
        return {
            'start_date': start_date,
            'end_date': end_date
        }
    
    def collect_data(self, symbols: List[str], source: str = 'tushare',
                    data_type: str = 'stock', freq: str = 'daily',
                    preview: bool = False, **kwargs) -> Dict[str, Any]:
        """执行数据采集 - Real Data Only版本（支持多种真实数据源）"""
        
        # 预检查：根据数据源类型进行配置验证
        if source == 'tushare':
            self.data_config.validate()
        # CSV和MySQL数据源在实际执行时验证
        
        # 解析时间范围
        time_range = self.parse_time_range(**kwargs)
        
        # 参数验证
        validation = self.validate_parameters(symbols, source, data_type, freq, **time_range)
        if not validation['valid']:
            return {
                'success': False,
                'errors': validation['errors'],
                'warnings': validation['warnings']
            }
        
        # 预览模式
        if preview:
            preview_info = self.preview_data(symbols, source, data_type, freq, **kwargs)
            return {
                'success': True,
                'mode': 'preview',
                'preview': preview_info,
                'warnings': validation['warnings'],
                'data_source_type': preview_info.get('data_source_type', 'real_only')
            }
        
        # 实际数据采集
        result = {
            'success': False,
            'collected_symbols': [],
            'failed_symbols': [],
            'total_rows': 0,
            'warnings': validation['warnings'],
            'data_source_type': 'real_tushare'
        }
        
        try:
            if source not in ['tushare', 'csv', 'mysql']:
                raise UnsupportedDataSourceError(f"不支持的数据源: {source}")
            
            storage = self._get_storage_manager()
            
            # 根据数据源类型选择不同的采集逻辑
            if source == 'tushare':
                collect_result = self._collect_from_tushare(symbols, data_type, freq, time_range, storage)
            elif source == 'csv':
                collect_result = self._collect_from_csv(symbols, data_type, freq, time_range, storage, **kwargs)
            elif source == 'mysql':
                collect_result = self._collect_from_mysql(symbols, data_type, freq, time_range, storage, **kwargs)
            
            # 更新结果
            result.update(collect_result)
                
        except (DataSourceNotConfiguredError, DataSourceUnavailableError, UnsupportedDataSourceError) as e:
            raise  # 重新抛出这些特定异常
        except Exception as e:
            raise DataCollectionError(f'数据采集失败: {str(e)}')
            
        return result
    
    def load_config_file(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_path}")
                
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    config = yaml.safe_load(f)
                else:
                    raise ValueError("仅支持YAML格式配置文件")
            
            return config
            
        except Exception as e:
            raise AquaException(f"配置文件加载失败: {e}")
    
    def get_predefined_templates(self) -> Dict[str, Dict[str, Any]]:
        """获取预定义模板"""
        templates = {
            'bank_stocks_daily': {
                'description': '银行股日线数据采集',
                'symbols': ['000001.SZ', '600036.SH', '600000.SH', '002142.SZ'],
                'source': 'tushare',
                'type': 'stock',
                'freq': 'daily',
                'fields': 'full'
            },
            'futures_main_contracts': {
                'description': '期货主力合约数据',
                'symbols': ['IF00.CFX', 'IC00.CFX', 'IH00.CFX'],
                'source': 'tushare', 
                'type': 'futures',
                'freq': 'daily',
                'fields': 'full'
            },
            'market_index': {
                'description': '市场指数数据',
                'symbols': ['000001.SH', '399001.SZ', '399006.SZ'],
                'source': 'tushare',
                'type': 'stock',
                'freq': 'daily',
                'fields': 'ohlcv'
            }
        }
        
        return templates
    
    def _collect_from_tushare(self, symbols: List[str], data_type: str, freq: str, 
                             time_range: Dict[str, str], storage) -> Dict[str, Any]:
        """从TUSHARE数据源采集数据"""
        extractor = self._get_tushare_extractor()
        
        # 简化版进度显示
        print(f"开始从TUSHARE采集{len(symbols)}只{data_type}数据...")
        
        collected_symbols = []
        failed_symbols = []
        total_rows = 0
        
        # 批量处理
        batches = [symbols[i:i+self._batch_size] for i in range(0, len(symbols), self._batch_size)]
        
        for batch_idx, batch in enumerate(batches):
            for symbol_idx, symbol in enumerate(batch):
                current_progress = batch_idx * self._batch_size + symbol_idx + 1
                print(f"正在处理 {symbol} ({current_progress}/{len(symbols)})")
                
                try:
                    # 使用真实TushareExtractor进行数据采集
                    extract_params = {
                        'symbol': symbol,
                        'start_date': time_range.get('start_date'),
                        'end_date': time_range.get('end_date')
                    }
                    
                    # 调用真实提取器
                    data = extractor.extract(**extract_params)
                    
                    if data is not None and len(data) > 0:
                        # 保存数据到存储
                        if hasattr(storage, 'save_data'):
                            storage.save_data(data, f"{data_type}_{freq}")
                        collected_symbols.append(symbol)
                        total_rows += len(data)
                        print(f"  ✅ {symbol}: {len(data)}行数据")
                    else:
                        failed_symbols.append(symbol)
                        print(f"  ❌ {symbol}: 无数据")
                        
                except Exception as e:
                    failed_symbols.append(symbol)
                    print(f"  ❌ {symbol}: 采集失败 - {e}")
                    if self.logger:
                        self.logger.error(f"采集{symbol}失败: {e}")
            
            # 批次间短暂休息，避免API限制
            if batch_idx < len(batches) - 1:
                time.sleep(0.1)
        
        return {
            'success': len(collected_symbols) > 0,
            'collected_symbols': collected_symbols,
            'failed_symbols': failed_symbols,
            'total_rows': total_rows,
            'message': f'成功采集 {len(collected_symbols)} 个标的的 {data_type} 数据'
                + (f'，失败 {len(failed_symbols)} 个' if failed_symbols else ''),
            'database_path': 'data/aqua_real_data.duckdb',
            'data_source_type': 'real_webapi'
        }
    
    def _collect_from_csv(self, symbols: List[str], data_type: str, freq: str,
                         time_range: Dict[str, str], storage, **kwargs) -> Dict[str, Any]:
        """从CSV文件数据源采集数据"""
        csv_importer = self._get_csv_importer()
        
        print(f"开始从CSV文件采集{len(symbols)}只{data_type}数据...")
        
        collected_symbols = []
        failed_symbols = []
        total_rows = 0
        
        try:
            # CSV导入器的具体实现取决于FromC2CCsvMainContractImporter的接口
            csv_path = kwargs.get('csv_path', 'data/csv/')  # 默认CSV路径
            
            for symbol in symbols:
                print(f"正在处理CSV文件: {symbol}")
                try:
                    # 调用CSV导入器
                    # 这里需要根据实际的FromC2CCsvMainContractImporter接口调整
                    result = csv_importer.import_data(
                        csv_path=csv_path,
                        symbol=symbol,
                        start_date=time_range.get('start_date'),
                        end_date=time_range.get('end_date')
                    )
                    
                    if result and result['success']:
                        collected_symbols.append(symbol)
                        total_rows += result.get('rows_imported', 0)
                        print(f"  ✅ {symbol}: {result.get('rows_imported', 0)}行数据")
                    else:
                        failed_symbols.append(symbol)
                        print(f"  ❌ {symbol}: 导入失败")
                        
                except Exception as e:
                    failed_symbols.append(symbol)
                    print(f"  ❌ {symbol}: 导入失败 - {e}")
                    if self.logger:
                        self.logger.error(f"CSV导入{symbol}失败: {e}")
                        
        except Exception as e:
            print(f"CSV导入过程失败: {e}")
            return {
                'success': False,
                'error': f'CSV导入失败: {e}',
                'data_source_type': 'real_localfile'
            }
        
        return {
            'success': len(collected_symbols) > 0,
            'collected_symbols': collected_symbols,
            'failed_symbols': failed_symbols,
            'total_rows': total_rows,
            'message': f'成功导入 {len(collected_symbols)} 个CSV文件的 {data_type} 数据'
                + (f'，失败 {len(failed_symbols)} 个' if failed_symbols else ''),
            'database_path': 'data/aqua_real_data.duckdb',
            'data_source_type': 'real_localfile'
        }
    
    def _collect_from_mysql(self, symbols: List[str], data_type: str, freq: str,
                           time_range: Dict[str, str], storage, **kwargs) -> Dict[str, Any]:
        """从MySQL数据库数据源采集数据"""
        mysql_importer = self._get_mysql_importer()
        
        print(f"开始从MySQL数据库迁移{len(symbols)}只{data_type}数据...")
        
        collected_symbols = []
        failed_symbols = []
        total_rows = 0
        
        try:
            # MySQL导入器的具体实现取决于MySQLImporter的接口
            mysql_config = kwargs.get('mysql_config', {})
            
            for symbol in symbols:
                print(f"正在迁移数据库表: {symbol}")
                try:
                    # 调用MySQL导入器
                    # 这里需要根据实际的MySQLImporter接口调整
                    result = mysql_importer.migrate_data(
                        symbol=symbol,
                        data_type=data_type,
                        start_date=time_range.get('start_date'),
                        end_date=time_range.get('end_date'),
                        **mysql_config
                    )
                    
                    if result and result['success']:
                        collected_symbols.append(symbol)
                        total_rows += result.get('rows_migrated', 0)
                        print(f"  ✅ {symbol}: {result.get('rows_migrated', 0)}行数据")
                    else:
                        failed_symbols.append(symbol)
                        print(f"  ❌ {symbol}: 迁移失败")
                        
                except Exception as e:
                    failed_symbols.append(symbol)
                    print(f"  ❌ {symbol}: 迁移失败 - {e}")
                    if self.logger:
                        self.logger.error(f"MySQL迁移{symbol}失败: {e}")
                        
        except Exception as e:
            print(f"MySQL迁移过程失败: {e}")
            return {
                'success': False,
                'error': f'MySQL迁移失败: {e}',
                'data_source_type': 'real_database'
            }
        
        return {
            'success': len(collected_symbols) > 0,
            'collected_symbols': collected_symbols,
            'failed_symbols': failed_symbols,
            'total_rows': total_rows,
            'message': f'成功迁移 {len(collected_symbols)} 个数据库表的 {data_type} 数据'
                + (f'，失败 {len(failed_symbols)} 个' if failed_symbols else ''),
            'database_path': 'data/aqua_real_data.duckdb',
            'data_source_type': 'real_database'
        }