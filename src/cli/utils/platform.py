"""
跨平台兼容性工具
"""
import sys
import os
import locale
from pathlib import Path
from typing import Dict, Any


def get_platform_config(platform: str = None) -> Dict[str, Any]:
    """
    获取平台特定配置
    
    Args:
        platform: 平台名称，如果为None则自动检测
        
    Returns:
        平台配置字典
    """
    if platform is None:
        platform = sys.platform
    
    # 先计算基础配置目录
    base_config_dir = _get_base_config_directory(platform)
    
    config = {
        'platform': platform,
        'path_separator': os.sep,
        'line_separator': os.linesep,
        'encoding': get_system_encoding(),
        'config_dir': base_config_dir,
        'supports_color': supports_color_output(),
        'terminal_width': get_terminal_width(),
    }
    
    # 平台特定配置
    if platform.startswith('win'):
        config.update({
            'shell': 'cmd',
            'supports_ansi': supports_ansi_colors(),
        })
    elif platform == 'darwin':
        config.update({
            'shell': 'zsh',
            'supports_ansi': True,
        })
    else:  # Linux和其他Unix系统
        config.update({
            'shell': 'bash',
            'supports_ansi': True,
        })
    
    return config


def get_system_encoding() -> str:
    """获取系统编码"""
    try:
        return locale.getpreferredencoding()
    except:
        return 'utf-8'


def _get_base_config_directory(platform: str) -> Path:
    """获取基础配置目录路径"""
    if platform.startswith('win'):
        config_dir = Path.home() / 'AppData' / 'Local' / 'AQUA'
    elif platform == 'darwin':
        config_dir = Path.home() / '.config' / 'aqua'
    else:  # Linux和其他Unix系统
        config_dir = Path.home() / '.config' / 'aqua'
    
    return config_dir


def get_config_directory() -> Path:
    """获取配置目录路径"""
    config_dir = _get_base_config_directory(sys.platform)
    
    # 确保目录存在
    config_dir.mkdir(parents=True, exist_ok=True)
    
    return config_dir


def supports_color_output() -> bool:
    """检查终端是否支持颜色输出"""
    # 检查环境变量
    if os.environ.get('NO_COLOR'):
        return False
    
    if os.environ.get('FORCE_COLOR'):
        return True
    
    # 检查是否是TTY
    if not hasattr(sys.stdout, 'isatty') or not sys.stdout.isatty():
        return False
    
    # Windows特殊处理
    if sys.platform.startswith('win'):
        return supports_ansi_colors()
    
    return True


def supports_ansi_colors() -> bool:
    """检查Windows终端是否支持ANSI颜色"""
    if not sys.platform.startswith('win'):
        return True
    
    try:
        import colorama
        colorama.init()
        return True
    except ImportError:
        # 检查Windows 10版本是否支持ANSI
        try:
            import ctypes
            kernel32 = ctypes.windll.kernel32
            kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)
            return True
        except:
            return False


def get_terminal_width() -> int:
    """获取终端宽度"""
    try:
        import shutil
        return shutil.get_terminal_size().columns
    except:
        return 80  # 默认宽度


def normalize_path(path: str) -> Path:
    """
    标准化路径，处理跨平台差异
    
    Args:
        path: 路径字符串
        
    Returns:
        标准化的Path对象
    """
    return Path(path).resolve()


def get_executable_extension() -> str:
    """获取可执行文件扩展名"""
    if sys.platform.startswith('win'):
        return '.exe'
    return ''


def is_admin() -> bool:
    """检查是否具有管理员权限"""
    try:
        if sys.platform.startswith('win'):
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        else:
            return os.geteuid() == 0
    except:
        return False