# =====================================================================
# AQUA 躺赢平台配置文件 v2.0
# Quantitative Analysis Platform Configuration
# =====================================================================

# =====================================================================
# 项目基础配置
# =====================================================================
[app]
name = "AQUA"
version = "2.0.0"
description = "Advanced Quantitative Analysis Platform"
author = "AQUA Development Team"
license = "MIT"
default_environment = "dev"
config_version = "2.0"

# 支持的环境列表
environments = ["dev", "test", "prod"]

# 项目核心模块
core_modules = [
    "data_warehouse",     # 数据仓库
    "strategy_drawer",    # 策略构建
    "backtest_workshop",  # 回测引擎
    "simulator",          # 模拟交易
    "ai_agent"           # AI智能助手
]

# =====================================================================
# 跨平台配置管理 (个人开发者优化)
# =====================================================================
[platform]
# 平台检测和适配
auto_detect = true
normalize_paths = true
auto_create_dirs = true
path_validation = true

# 支持的平台
supported_platforms = ["windows", "darwin", "linux"]

# 个人开发者跨平台优化
[platform.personal_dev]
# 统一环境变量
env_vars = ["AQUA_CONFIG_DIR", "AQUA_DATA_DIR", "TUSHARE_TOKEN"]
# 字符编码统一
default_encoding = "utf-8"
# 路径分隔符自动处理
auto_path_separator = true
# 权限检查
check_write_permissions = true
# 中文路径支持
utf8_path_support = true

# =====================================================================
# 平台特定路径配置
# =====================================================================

# Windows平台路径配置
[platform.windows]
# AQUA数据根目录 - DataCenter作为统一数据根目录
data_root = "D:/Data/duckdb/AQUA/DataCenter"

# 核心路径配置 - 所有路径都在DataCenter下
[platform.windows.paths] 
duckdb_root = "D:/Data/duckdb/AQUA"                      # DuckDB根目录
datacenter_dir = "D:/Data/duckdb/AQUA/DataCenter"        # DataCenter目录(数据根目录)
backup_root = "D:/Data/duckdb/AQUA/DataCenter/backup"    # 备份在DataCenter下
cache_root = "D:/Data/duckdb/AQUA/DataCenter/cache"      # 缓存在DataCenter下
logs_root = "D:/Data/duckdb/AQUA/DataCenter/logs"        # 日志在DataCenter下

# 数据源路径配置
[platform.windows.datasources]
csv_root = "D:/Data/duckdb/AQUA/DataCenter/datasources/csv"    # CSV数据源
fromc2c_data = "D:/Data/RAW/FromC2C"                           # FromC2C历史数据(原始数据位置)
realtime_data = "D:/Data/duckdb/AQUA/DataCenter/realtime"      # 实时数据
user_data = "D:/Data/duckdb/AQUA/DataCenter/user_data"         # 用户数据
export_data = "D:/Data/duckdb/AQUA/DataCenter/exports"         # 导出数据

# Unix平台(macOS/Linux)路径配置
[platform.unix]
# AQUA数据根目录 - DataCenter作为统一数据根目录
data_root = "~/Documents/Data/duckdb/AQUA/DataCenter"

# 核心路径配置 - 所有路径都在DataCenter下
[platform.unix.paths]
duckdb_root = "~/Documents/Data/duckdb/AQUA"                       # DuckDB根目录
datacenter_dir = "~/Documents/Data/duckdb/AQUA/DataCenter"         # DataCenter目录(数据根目录)
backup_root = "~/Documents/Data/duckdb/AQUA/DataCenter/backup"     # 备份在DataCenter下
cache_root = "~/Documents/Data/duckdb/AQUA/DataCenter/cache"       # 缓存在DataCenter下
logs_root = "~/Documents/Data/duckdb/AQUA/DataCenter/logs"         # 日志在DataCenter下

# 数据源路径配置
[platform.unix.datasources]
csv_root = "~/Documents/Data/duckdb/AQUA/DataCenter/datasources/csv"  # CSV数据源
fromc2c_data = "~/Documents/Data/FromC2C"                             # FromC2C历史数据(原始数据位置)
realtime_data = "~/Documents/Data/duckdb/AQUA/DataCenter/realtime"     # 实时数据
user_data = "~/Documents/Data/duckdb/AQUA/DataCenter/user_data"        # 用户数据
export_data = "~/Documents/Data/duckdb/AQUA/DataCenter/exports"        # 导出数据

# =====================================================================
# 数据源统一配置
# =====================================================================
[datasources]
# 支持的数据源类型
supported_types = ["csv", "mysql", "api", "websocket"]
default_encoding = "utf-8"
connection_timeout = 30
retry_attempts = 3
retry_delay = 1.0

# CSV数据源配置
[datasources.csv]
name = "CSV文件数据源"
description = "本地CSV文件数据导入"
enabled = true
file_patterns = ["*.csv", "*.CSV"]
encoding_fallback = ["utf-8", "utf-8-sig", "gbk", "gb2312"]
delimiter_detection = true
default_delimiter = ","
max_file_size_mb = 500
batch_size = 10000
parallel_processing = true
validation_enabled = true

# FromC2C特定配置
[datasources.csv.fromc2c]
name = "FromC2C历史数据"
enabled = true
frequencies = ["5min", "15min", "30min"]
date_format = "%Y-%m-%d %H:%M:%S"
auto_detect_frequency = true
contract_code_format = "CCFX"
data_quality_check = true

# MySQL数据源配置
[datasources.mysql]
name = "MySQL数据库"
description = "关系型数据库连接"
enabled = true
driver = "pymysql"
charset = "utf8mb4"
autocommit = false
pool_size = 10
max_overflow = 20
pool_timeout = 30
pool_recycle = 3600
connect_timeout = 30
read_timeout = 30
write_timeout = 30

# API数据源配置(预留扩展)
[datasources.api]
name = "API数据接口"
description = "外部API数据获取"
enabled = false
default_timeout = 30
max_requests_per_second = 5
retry_strategy = "exponential_backoff"
cache_responses = true
cache_ttl = 300

# Tushare API配置
[datasources.api.tushare]
name = "Tushare Pro"
enabled = true
base_url = "https://tushare.pro"
access_token = "${TUSHARE_TOKEN}"  # 从环境变量读取
rate_limit = 200
points_per_minute = 2000

# 多级积分预算控制
[datasources.api.tushare.budget]
total_points = 2100  # 总积分预算
daily_limit = 50     # 每日积分限制
emergency_reserve = 200  # 紧急预留积分

# 积分预算模式
[datasources.api.tushare.budget.modes]
conservative = { daily_limit = 30, priority = "期货主力合约" }
normal = { daily_limit = 50, priority = "期货>股票>其他" }  
aggressive = { daily_limit = 80, priority = "全量数据" }

# 积分使用监控
[datasources.api.tushare.monitoring]
enable_tracking = true
efficiency_target = 0.95  # 积分使用效率≥95%
warning_threshold = 0.8   # 80%预算时警告
auto_downgrade = true     # 自动降级策略

# AKShare API配置(预留)
[datasources.api.akshare]
name = "AKShare"
enabled = false
timeout = 30
retry_count = 3

# WebSocket数据源配置(预留扩展)
[datasources.websocket]
name = "WebSocket实时数据"
description = "实时数据流接入"
enabled = false
reconnect_attempts = 5
heartbeat_interval = 30
buffer_size = 10000

# =====================================================================
# 开发环境配置 (DEV)
# =====================================================================
[dev]
description = "开发环境配置"
debug = true
log_level = "DEBUG"
enable_profiling = true
hot_reload = true

# 开发环境数据库配置
[dev.database]
# 数据库文件路径 (使用平台特定占位符)
path = "{datacenter_dir}/aqua_dev.duckdb"
auto_create = true
auto_backup = true
backup_interval_hours = 24
max_backup_files = 7
vacuum_on_startup = false
enable_wal = true
memory_limit = "2GB"
threads = 4

# 连接池配置
[dev.database.pool]
max_connections = 5
min_connections = 1
connection_timeout = 30
idle_timeout = 300

# 开发环境数据源配置
[dev.datasources]

# CSV数据源配置
[dev.datasources.csv]
data_dir = "{csv_root}/dev"
fromc2c_dir = "{fromc2c_data}"
batch_size = 1000
max_files_per_batch = 10
enable_parallel = false
validation_level = "strict"

# MySQL数据源配置
[dev.datasources.mysql]
host = "127.0.0.1"
port = 3306
username = "root"
db_connection_auth = ""  # 生产环境应使用环境变量
database = "qtdb_pro"
pool_size = 3
max_overflow = 5

# 开发环境日志配置
[dev.logging]
level = "DEBUG"
console_output = true
file_output = true
file_path = "{logs_root}/dev/aqua_dev_{date}.log"
max_file_size = "10MB"
backup_count = 5
format = "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s"

# 开发环境性能配置 (个人开发者优化)
[dev.performance]
enable_monitoring = true
profiling_enabled = true
memory_limit_mb = 1536  # 个人开发者友好：峰值≤1.5GB
startup_target_seconds = 2  # 启动时间≤2秒
max_workers = 2
chunk_size = 500
gc_threshold = 1000
# 个人开发者内存管理
personal_dev_mode = true
memory_warning_threshold_mb = 1200  # 80%峰值预警
auto_gc_trigger_mb = 50  # 每50MB触发GC
idle_memory_limit_mb = 800  # 平均使用≤800MB

# 开发环境缓存配置
[dev.cache]
enabled = true
type = "memory"  # memory, file, redis
l1_enabled = true
l1_max_size = 1000
l1_ttl = 1800
l2_enabled = true
l2_cache_dir = "{cache_root}/dev"
l2_max_size_mb = 100
l2_ttl = 3600

# 开发环境网络配置
[dev.network]
# 网络检测和优化
china_detection_enabled = true
china_detection_timeout = 2
connection_retries = 3
request_timeout = 30

# 中国网络镜像源配置 (按稳定性和可用性排序)
china_mirrors = [
    # 第一梯队：最高稳定性和同步频率
    { name = "清华大学", host = "pypi.tuna.tsinghua.edu.cn", url = "https://pypi.tuna.tsinghua.edu.cn/simple", priority = 1, region = "华北", features = ["高同步频率", "覆盖全面"] },
    
    # 第二梯队：企业级支持，速度优异
    { name = "阿里云", host = "mirrors.aliyun.com", url = "http://mirrors.aliyun.com/pypi/simple", priority = 2, region = "全国", features = ["速度快", "企业级支持"] },
    { name = "华为云", host = "repo.huaweicloud.com", url = "https://repo.huaweicloud.com/repository/pypi/simple", priority = 2, region = "华南", features = ["华南地区优化", "云原生"] },
    { name = "腾讯云", host = "mirrors.cloud.tencent.com", url = "http://mirrors.cloud.tencent.com/pypi/simple", priority = 2, region = "华南", features = ["云服务集成", "CDN加速"] },
    
    # 第三梯队：备用镜像源  
    { name = "中科大", host = "pypi.mirrors.ustc.edu.cn", url = "https://pypi.mirrors.ustc.edu.cn/simple/", priority = 3, region = "华东", features = ["学术网络优化"] },
    { name = "豆瓣", host = "pypi.douban.com", url = "https://pypi.douban.com/simple/", priority = 3, region = "全国", features = ["老牌稳定"] }
]

# 默认PyPI源 (海外网络环境)
default_pypi = { name = "PyPI官方", host = "pypi.org", url = "https://pypi.org/simple/", priority = 0, region = "海外", features = ["官方源", "最新包"] }

# 智能镜像源选择策略
[dev.network.mirror_selection]
strategy = "auto"
connection_timeout = 5
speed_test_timeout = 10
failover_retries = 3
cache_duration = 24
concurrent_tests = 3

# 镜像源健康检查
[dev.network.health_check]
enabled = true
check_interval = 6
check_timeout = 3
failure_threshold = 3
recovery_check_interval = 30

# =====================================================================
# 测试环境配置 (TEST)
# =====================================================================
[test]
description = "测试环境配置"
debug = false
log_level = "INFO"
enable_profiling = false
load_test_data = true

# 测试环境数据库配置
[test.database]
path = "{datacenter_dir}/aqua_test.duckdb"
auto_create = true
auto_backup = true
backup_interval_hours = 12
max_backup_files = 10
vacuum_on_startup = true
enable_wal = true
memory_limit = "4GB"
threads = 6

# 连接池配置
[test.database.pool]
max_connections = 10
min_connections = 2
connection_timeout = 30
idle_timeout = 600

# 测试环境数据源配置
[test.datasources]

# CSV数据源配置
[test.datasources.csv]
data_dir = "{csv_root}/test"
fromc2c_dir = "{fromc2c_data}"
batch_size = 5000
max_files_per_batch = 50
enable_parallel = true
validation_level = "normal"

# MySQL数据源配置
[test.datasources.mysql]
host = "127.0.0.1"
port = 3306
username = "root"
connection_auth = ""
database = "qtdb_pro"
pool_size = 5
max_overflow = 10

# 测试环境日志配置
[test.logging]
level = "INFO"
console_output = true
file_output = true
file_path = "{logs_root}/test/aqua_test_{date}.log"
max_file_size = "20MB"
backup_count = 7
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 测试环境性能配置
[test.performance]
enable_monitoring = true
profiling_enabled = false
memory_limit_mb = 4096
max_workers = 4
chunk_size = 1000
gc_threshold = 2000

# 测试环境缓存配置
[test.cache]
enabled = true
type = "file"
l1_enabled = true
l1_max_size = 2000
l1_ttl = 3600
l2_enabled = true
l2_cache_dir = "{cache_root}/test"
l2_max_size_mb = 200
l2_ttl = 7200

# =====================================================================
# 生产环境配置 (PROD)
# =====================================================================
[prod]
description = "生产环境配置"
debug = false
log_level = "WARNING"
enable_profiling = false
strict_mode = true

# 生产环境数据库配置
[prod.database]
path = "{datacenter_dir}/aqua_prod.duckdb"
auto_create = false  # 生产环境不自动创建
auto_backup = true
backup_interval_hours = 6
max_backup_files = 24
vacuum_on_startup = true
enable_wal = true
memory_limit = "8GB"
threads = 8

# 连接池配置
[prod.database.pool]
max_connections = 20
min_connections = 5
connection_timeout = 60
idle_timeout = 1800

# 生产环境数据源配置
[prod.datasources]

# CSV数据源配置
[prod.datasources.csv]
data_dir = "{csv_root}/prod"
fromc2c_dir = "{fromc2c_data}"
batch_size = 10000
max_files_per_batch = 100
enable_parallel = true
validation_level = "strict"

# MySQL数据源配置
[prod.datasources.mysql]
host = "127.0.0.1"
port = 3306
username = "root"
connection_auth = ""  # 必须通过环境变量设置
database = "qtdb_pro"
pool_size = 10
max_overflow = 20

# 生产环境日志配置
[prod.logging]
level = "WARNING"
console_output = false
file_output = true
file_path = "{logs_root}/prod/aqua_prod_{date}.log"
max_file_size = "50MB"
backup_count = 30
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 生产环境性能配置
[prod.performance]
enable_monitoring = true
profiling_enabled = false
memory_limit_mb = 8192
max_workers = 8
chunk_size = 5000
gc_threshold = 5000

# 生产环境缓存配置
[prod.cache]
enabled = true
type = "file"
l1_enabled = true
l1_max_size = 5000
l1_ttl = 7200
l2_enabled = true
l2_cache_dir = "{cache_root}/prod"
l2_max_size_mb = 1024
l2_ttl = 14400

# =====================================================================
# AQUA核心模块配置
# =====================================================================

# 数据仓库模块配置
[modules.data_warehouse]
name = "数据仓库模块"
enabled = true
description = "核心数据存储和管理"
priority = 1

# 数据层配置
[modules.data_warehouse.layers]
csv_layer = true
mysql_layer = true
api_layer = false
realtime_layer = false

# 数据质量配置
[modules.data_warehouse.quality]
enable_validation = true
duplicate_check = true
null_value_check = true
data_type_check = true
range_check = true
consistency_check = true

# 策略构建模块配置
[modules.strategy_drawer]
name = "策略构建模块"
enabled = true
description = "量化策略设计和构建"
priority = 2

# 策略类型支持
[modules.strategy_drawer.strategies]
trend_following = true
mean_reversion = true
arbitrage = true
market_making = true
machine_learning = true

# 回测引擎模块配置
[modules.backtest_workshop]
name = "回测引擎模块"
enabled = true
description = "策略回测和性能分析"
priority = 3

# 回测配置
[modules.backtest_workshop.settings]
default_commission = 0.0003
default_slippage = 0.0001
benchmark = "CSI300"
risk_free_rate = 0.03
max_drawdown_threshold = 0.2

# 模拟交易模块配置
[modules.simulator]
name = "模拟交易模块"
enabled = true
description = "实盘模拟交易环境"
priority = 4

# 模拟交易配置
[modules.simulator.settings]
initial_capital = 1000000
max_position_size = 0.1
max_single_order = 0.05
enable_risk_control = true

# AI智能助手模块配置
[modules.ai_agent]
name = "AI智能助手模块"
enabled = false  # 预留扩展
description = "智能分析和决策辅助"
priority = 5

# AI配置(预留)
[modules.ai_agent.settings]
model_type = "transformer"
enable_learning = false
confidence_threshold = 0.8

# =====================================================================
# 网络配置
# =====================================================================
[network]
# 网络检测和优化
china_detection_enabled = true
china_detection_timeout = 2
connection_retries = 3
request_timeout = 30

# 中国网络镜像源配置 (按稳定性和可用性排序)
china_mirrors = [
    # 第一梯队：最高稳定性和同步频率
    { name = "清华大学", host = "pypi.tuna.tsinghua.edu.cn", url = "https://pypi.tuna.tsinghua.edu.cn/simple", priority = 1, region = "华北", features = ["高同步频率", "覆盖全面"] },
    
    # 第二梯队：企业级支持，速度优异
    { name = "阿里云", host = "mirrors.aliyun.com", url = "http://mirrors.aliyun.com/pypi/simple", priority = 2, region = "全国", features = ["速度快", "企业级支持"] },
    { name = "华为云", host = "repo.huaweicloud.com", url = "https://repo.huaweicloud.com/repository/pypi/simple", priority = 2, region = "华南", features = ["华南地区优化", "云原生"] },
    { name = "腾讯云", host = "mirrors.cloud.tencent.com", url = "http://mirrors.cloud.tencent.com/pypi/simple", priority = 2, region = "华南", features = ["云服务集成", "CDN加速"] },
    
    # 第三梯队：备用镜像源  
    { name = "中科大", host = "pypi.mirrors.ustc.edu.cn", url = "https://pypi.mirrors.ustc.edu.cn/simple/", priority = 3, region = "华东", features = ["学术网络优化"] },
    { name = "豆瓣", host = "pypi.douban.com", url = "https://pypi.douban.com/simple/", priority = 3, region = "全国", features = ["老牌稳定"] }
]

# 默认PyPI源 (海外网络环境)
default_pypi = { name = "PyPI官方", host = "pypi.org", url = "https://pypi.org/simple/", priority = 0, region = "海外", features = ["官方源", "最新包"] }

# 智能镜像源选择策略
[network.mirror_selection]
# 选择策略：auto(自动), speed(速度优先), stability(稳定性优先), region(地区优先)
strategy = "auto"
# 连接超时时间 (秒)
connection_timeout = 5
# 下载测试超时时间 (秒)  
speed_test_timeout = 10
# 故障转移重试次数
failover_retries = 3
# 缓存有效期 (小时)
cache_duration = 24
# 并发测试镜像源数量
concurrent_tests = 3

# 镜像源健康检查
[network.health_check]
# 是否启用健康检查
enabled = true
# 检查间隔 (小时)
check_interval = 6
# 健康检查超时 (秒)
check_timeout = 3
# 连续失败阈值
failure_threshold = 3
# 恢复检查间隔 (分钟)
recovery_check_interval = 30

# 代理配置
[network.proxy]
enabled = false
http_proxy = ""
https_proxy = ""
no_proxy = "localhost,127.0.0.1"

# =====================================================================
# 安全配置
# =====================================================================
[security]
# 加密配置
encryption_enabled = true
encryption_algorithm = "AES-256"
key_derivation = "PBKDF2"

# 访问控制
access_control_enabled = true
session_timeout = 3600
max_login_attempts = 5
lockout_duration = 300

# 数据保护
data_anonymization = true
audit_log_enabled = true
backup_encryption = true

# =====================================================================
# 监控和告警配置
# =====================================================================
[monitoring]
# 性能监控
performance_monitoring = true
memory_monitoring = true
disk_monitoring = true
network_monitoring = false

# 监控指标
[monitoring.metrics]
cpu_threshold = 80
memory_threshold = 85
disk_threshold = 90
response_time_threshold = 5.0

# 告警配置
[monitoring.alerts]
enabled = false
email_notifications = false
webhook_notifications = false

# =====================================================================
# 备份和恢复配置
# =====================================================================
[backup]
# 自动备份
auto_backup_enabled = true
backup_schedule = "daily"  # daily, weekly, monthly
backup_retention_days = 30
compress_backups = true

# 备份存储
[backup.storage]
local_backup = true
cloud_backup = false
backup_verification = true

# =====================================================================
# 开发工具配置
# =====================================================================
[development]
# 开发环境
hot_reload = true
debug_mode = true
profiling = true
code_analysis = true

# 测试配置
[development.testing]
unit_tests = true
integration_tests = true
performance_tests = false
coverage_threshold = 0.8

# 代码质量
[development.code_quality]
linting = true
type_checking = true
code_formatting = true
documentation_check = false

# =====================================================================
# 环境变量覆盖配置
# =====================================================================
[environment_variables]
# 环境变量映射
database_auth = "AQUA_DB_PASSWORD"
mysql_auth = "AQUA_MYSQL_PASSWORD"
tushare_api_env = "TUSHARE_TOKEN"
encryption_key = "AQUA_ENCRYPTION_KEY"

# 环境检测变量
environment_detection = ["AQUA_ENV", "ENV", "ENVIRONMENT"]
debug_mode = ["AQUA_DEBUG", "DEBUG"]
log_level = ["AQUA_LOG_LEVEL", "LOG_LEVEL"]

# =====================================================================
# 兼容性配置
# =====================================================================
[compatibility]
# 向后兼容性
legacy_config_support = true
migration_warnings = true
deprecated_feature_warnings = true

# 版本兼容性
min_python_version = "3.11"
max_python_version = "3.12"
config_version_check = true

# API兼容性
api_version = "2.0"
backward_compatibility = true