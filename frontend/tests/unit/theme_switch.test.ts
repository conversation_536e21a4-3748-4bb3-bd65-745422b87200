import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { defineComponent, ref } from 'vue';

// 假设有一个主题store或全局状态
const useThemeStore = () => {
  const theme = ref('light');
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light';
    window.localStorage.setItem('theme', theme.value);
  };
  return { theme, toggleTheme };
};

describe('主题切换入口与逻辑', () => {
  beforeEach(() => {
    window.localStorage.clear();
  });

  it('应存在主题切换按钮', () => {
    const Comp = defineComponent({
      template: `<button @click="toggleTheme">切换主题</button>`,
      setup() {
        const { toggleTheme } = useThemeStore();
        return { toggleTheme };
      }
    });
    const wrapper = mount(Comp);
    expect(wrapper.find('button').exists()).toBe(true);
    expect(wrapper.find('button').text()).toContain('切换主题');
  });

  it('点击按钮后主题状态应切换', async () => {
    const Comp = defineComponent({
      template: `<button @click="toggleTheme">切换主题</button><span>{{ theme }}</span>`,
      setup() {
        const { theme, toggleTheme } = useThemeStore();
        return { theme, toggleTheme };
      }
    });
    const wrapper = mount(Comp);
    expect(wrapper.find('span').text()).toBe('light');
    await wrapper.find('button').trigger('click');
    expect(wrapper.find('span').text()).toBe('dark');
  });

  it('切换后主题状态应持久化到localStorage', async () => {
    const Comp = defineComponent({
      template: `<button @click="toggleTheme">切换主题</button>`,
      setup() {
        const { toggleTheme } = useThemeStore();
        return { toggleTheme };
      }
    });
    const wrapper = mount(Comp);
    await wrapper.find('button').trigger('click');
    expect(window.localStorage.getItem('theme')).toBe('dark');
  });
});
