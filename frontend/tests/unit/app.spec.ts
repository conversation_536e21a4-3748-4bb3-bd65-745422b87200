import { describe, it, expect } from 'vitest';
import fs from 'fs';
import path from 'path';

const appVuePath = path.resolve(__dirname, '../../src/App.vue');

describe('App.vue骨架', () => {
  it('应存在App.vue文件', () => {
    expect(fs.existsSync(appVuePath)).toBe(true);
  });

  it('应包含<router-view />', () => {
    const content = fs.readFileSync(appVuePath, 'utf-8');
    expect(content.includes('<router-view')).toBe(true);
  });

  it('应有中文注释说明入口职责', () => {
    const content = fs.readFileSync(appVuePath, 'utf-8');
    // 检查是否有"入口"、"职责"等中文注释
    expect(/(入口|职责|全局|挂载)[^\n]*[\n\r]/.test(content)).toBe(true);
  });
});
