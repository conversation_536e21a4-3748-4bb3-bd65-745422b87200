import { describe, it, expect } from 'vitest';
import routes from '@/router/index';

describe('路由懒加载与优化', () => {
  it('所有主路由组件应为懒加载函数', () => {
    // 检查component为函数（即懒加载）
    const allLazy = routes.every((r) => typeof r.component === 'function');
    expect(allLazy).toBe(true);
  });

  it('嵌套路由组件也应为懒加载函数', () => {
    const nested = routes.flatMap((r) => r.children || []);
    const allNestedLazy = nested.every((r) => typeof r.component === 'function');
    expect(allNestedLazy).toBe(true);
  });

  it('路由配置文件应有详细中文注释（人工检查）', () => {
    expect(true).toBe(true);
  });
});
