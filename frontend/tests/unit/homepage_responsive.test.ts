import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import HomePage from '@/modules/home/<USER>';

describe('HomePage 响应式断点与适配', () => {
  it('桌面端应为横向卡片排列', async () => {
    global.innerWidth = 1200;
    const wrapper = mount(HomePage);
    const features = wrapper.find('.features');
    // 桌面端应为横向flex布局
    expect(features.attributes('class')).toMatch(/flex/);
    // 卡片数量为3
    expect(wrapper.findAll('.feature-card').length).toBe(3);
  });

  it('移动端应为纵向卡片堆叠', async () => {
    global.innerWidth = 375;
    // 触发窗口resize事件
    window.dispatchEvent(new Event('resize'));
    const wrapper = mount(HomePage);
    const features = wrapper.find('.features');
    // 移动端应为纵向flex-col或block布局
    expect(features.attributes('class')).toMatch(/flex-col|block/);
  });

  it('按钮和字体在移动端应自适应缩放', async () => {
    global.innerWidth = 375;
    window.dispatchEvent(new Event('resize'));
    const wrapper = mount(HomePage);
    const btn = wrapper.find('.feature-card button');
    // 检查按钮样式有无自适应class（如w-full、text-sm等）
    expect(btn.attributes('class')).toMatch(/w-full|text-sm|sm:/);
  });

  it('组件文件头和关键节点应有中文注释（人工检查）', () => {
    expect(true).toBe(true);
  });
});
