import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { nextTick } from 'vue';
import { createPinia, setActivePinia } from 'pinia';
import UnifiedDataImport from '@/modules/data-center/components/UnifiedDataImport.vue';
import { useDataCenterStore } from '@/stores/data_center_store';
import * as dataCenterApi from '@/api/data_center_api';

// Mock API
vi.mock('@/api/data_center_api', () => ({
  dataCenterAPI: {
    importCSVEnhanced: vi.fn(),
    testMySQLConnection: vi.fn(),
    getMySQLTables: vi.fn(),
    importMySQLData: vi.fn(),
    getImportHistory: vi.fn(),
    getImportStats: vi.fn(),
    cancelImport: vi.fn(),
    pauseImport: vi.fn(),
    resumeImport: vi.fn(),
    resolveConflict: vi.fn(),
    getImportSessionStatus: vi.fn()
  }
}));

// Mock Vue Router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn()
};

const mockRoute = {
  params: {},
  query: {},
  path: '/data-center'
};

describe('UnifiedDataImport组件', () => {
  let wrapper: any;
  let store: any;
  let pinia: any;

  const mockImportHistory = [
    {
      id: 'import_1',
      type: 'csv',
      environment: 'test',
      fileName: 'test_data.csv',
      recordCount: 1000,
      success: true,
      timestamp: '2025-07-22T10:00:00Z',
      duration: 30000
    },
    {
      id: 'import_2',
      type: 'mysql',
      environment: 'test',
      tableNames: ['table1', 'table2'],
      recordCount: 5000,
      success: false,
      error: '连接超时',
      timestamp: '2025-07-22T09:00:00Z',
      duration: 15000
    }
  ];

  const mockMysqlTables = [
    {
      name: 'stock_data',
      record_count: 10000,
      last_updated: '2025-07-22T08:00:00Z',
      comment: '股票数据表'
    },
    {
      name: 'futures_data',
      record_count: 5000,
      last_updated: '2025-07-22T07:00:00Z',
      comment: '期货数据表'
    }
  ];

  beforeEach(() => {
    // 创建新的Pinia实例
    pinia = createPinia();
    setActivePinia(pinia);

    // 重置Mock
    vi.clearAllMocks();

    // 设置API Mock返回值
    vi.mocked(dataCenterApi.dataCenterAPI.importCSVEnhanced).mockResolvedValue({
      data: {
        success: true,
        sessionId: 'session_123',
        message: 'CSV导入已启动'
      }
    });

    vi.mocked(dataCenterApi.dataCenterAPI.testMySQLConnection).mockResolvedValue({
      data: {
        success: true,
        message: '连接成功'
      }
    });

    vi.mocked(dataCenterApi.dataCenterAPI.getMySQLTables).mockResolvedValue({
      data: {
        success: true,
        data: mockMysqlTables
      }
    });

    vi.mocked(dataCenterApi.dataCenterAPI.importMySQLData).mockResolvedValue({
      data: {
        success: true,
        sessionId: 'mysql_session_456',
        message: 'MySQL导入已启动'
      }
    });

    vi.mocked(dataCenterApi.dataCenterAPI.getImportHistory).mockResolvedValue({
      data: {
        success: true,
        data: mockImportHistory,
        total: mockImportHistory.length
      }
    });

    vi.mocked(dataCenterApi.dataCenterAPI.getImportStats).mockResolvedValue({
      data: {
        success: true,
        data: {
          totalImports: 100,
          successRate: 85,
          totalRecords: 1000000,
          averageDuration: 25000
        }
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  it('应该正确渲染统一导入组件', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 验证组件基本结构
    expect(wrapper.find('.unified-data-import').exists()).toBe(true);
    expect(wrapper.find('[data-value="csv"]').exists()).toBe(true);
    expect(wrapper.find('[data-value="mysql"]').exists()).toBe(true);
    expect(wrapper.find('[data-value="history"]').exists()).toBe(true);
  });

  it('应该正确渲染所有导入类型Tab', () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    // 验证Tab标签存在
    const tabPanes = wrapper.findAll('[role="tabpanel"]');
    expect(tabPanes.length).toBeGreaterThanOrEqual(3);

    // 验证CSV导入Tab
    expect(wrapper.text()).toContain('CSV文件导入');
    // 验证MySQL导入Tab
    expect(wrapper.text()).toContain('MySQL数据导入');
    // 验证历史记录Tab
    expect(wrapper.text()).toContain('导入历史');
  });

  it('应该正确处理CSV导入模式切换', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 测试模式切换
    const csvModeSelect = wrapper.find('[data-testid="csv-mode-select"]');
    if (csvModeSelect.exists()) {
      await csvModeSelect.setValue('fromC2C');
      await nextTick();

      expect(wrapper.vm.csvFormData.importMode).toBe('fromC2C');
      // 验证FromC2C专用配置显示
      expect(wrapper.find('.fromC2C-config').exists()).toBe(true);
    }
  });

  it('应该正确处理CSV文件上传', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 模拟文件上传
    const file = new File(['test,data\n1,value'], 'test.csv', { type: 'text/csv' });
    const fileList = [{ file, status: 'ready' }];

    // 模拟上传事件
    const uploadComponent = wrapper.findComponent({ name: 'NUpload' });
    if (uploadComponent.exists()) {
      await uploadComponent.vm.$emit('change', { fileList });
      await nextTick();

      expect(wrapper.vm.csvFileList.length).toBeGreaterThan(0);
    }
  });

  it('应该正确处理CSV导入提交', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 设置表单数据
    wrapper.vm.csvFormData = {
      importMode: 'single',
      environment: 'test',
      enableValidation: true
    };

    // 模拟文件列表
    wrapper.vm.csvFileList = [{ file: new File(['test'], 'test.csv'), status: 'ready' }];

    // 触发导入
    const submitButton = wrapper.find('[data-testid="csv-submit-button"]');
    if (submitButton.exists()) {
      await submitButton.trigger('click');
      await flushPromises();

      expect(dataCenterApi.dataCenterAPI.importCSVEnhanced).toHaveBeenCalled();
      expect(wrapper.vm.importInProgress).toBe(true);
    }
  });

  it('应该正确处理MySQL连接测试', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 切换到MySQL Tab
    wrapper.vm.activeImportTab = 'mysql';
    await nextTick();

    // 设置MySQL配置
    wrapper.vm.mysqlFormData = {
      host: 'localhost',
      port: 3306,
      database: 'test_db',
      username: 'test_user',
      password: 'test_pass'
    };

    // 触发连接测试
    const testButton = wrapper.find('[data-testid="mysql-test-connection"]');
    if (testButton.exists()) {
      await testButton.trigger('click');
      await flushPromises();

      expect(dataCenterApi.dataCenterAPI.testMySQLConnection).toHaveBeenCalledWith({
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_pass'
      });

      expect(wrapper.vm.mysqlConnectionStatus).toBe('success');
    }
  });

  it('应该正确加载MySQL表列表', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 切换到MySQL Tab
    wrapper.vm.activeImportTab = 'mysql';
    await nextTick();

    // 设置连接成功状态
    wrapper.vm.mysqlConnectionStatus = 'success';

    // 触发加载表列表
    const loadTablesButton = wrapper.find('[data-testid="mysql-load-tables"]');
    if (loadTablesButton.exists()) {
      await loadTablesButton.trigger('click');
      await flushPromises();

      expect(dataCenterApi.dataCenterAPI.getMySQLTables).toHaveBeenCalled();
      expect(wrapper.vm.availableMysqlTables).toEqual(mockMysqlTables);
    }
  });

  it('应该正确处理MySQL导入提交', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 设置MySQL导入数据
    wrapper.vm.activeImportTab = 'mysql';
    wrapper.vm.mysqlFormData = {
      host: 'localhost',
      port: 3306,
      database: 'test_db',
      username: 'test_user',
      password: 'test_pass',
      environment: 'test',
      tables: ['stock_data', 'futures_data']
    };
    wrapper.vm.mysqlConnectionStatus = 'success';

    await nextTick();

    // 触发MySQL导入
    const importButton = wrapper.find('[data-testid="mysql-import-button"]');
    if (importButton.exists()) {
      await importButton.trigger('click');
      await flushPromises();

      expect(dataCenterApi.dataCenterAPI.importMySQLData).toHaveBeenCalledWith(
        expect.objectContaining({
          host: 'localhost',
          port: 3306,
          database: 'test_db',
          username: 'test_user',
          password: 'test_pass',
          environment: 'test',
          tables: ['stock_data', 'futures_data']
        })
      );
    }
  });

  it('应该正确处理导入历史加载', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 切换到历史Tab
    wrapper.vm.activeImportTab = 'history';
    await nextTick();

    // 等待历史数据加载
    await flushPromises();

    expect(dataCenterApi.dataCenterAPI.getImportHistory).toHaveBeenCalled();
    expect(wrapper.vm.importHistoryData).toEqual(mockImportHistory);
  });

  it('应该正确处理导入进度更新', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 模拟进度更新
    const progressData = {
      sessionId: 'session_123',
      progress: 50,
      currentTable: 'stock_data',
      totalTables: 2,
      completedTables: 1,
      recordsProcessed: 5000,
      totalRecords: 10000,
      speed: 167,
      eta: '30s'
    };

    wrapper.vm.updateImportProgress(progressData);

    expect(wrapper.vm.importProgress).toEqual(progressData);
    expect(wrapper.vm.overallProgress).toBe(50);
  });

  it('应该正确处理导入错误', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 模拟导入错误
    const errorData = {
      sessionId: 'session_123',
      error: 'Database connection failed',
      timestamp: Date.now()
    };

    wrapper.vm.handleImportError(errorData);

    expect(wrapper.vm.importInProgress).toBe(false);
    expect(wrapper.vm.importError).toBe('Database connection failed');
  });

  it('应该正确处理冲突解决', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 模拟冲突数据
    const conflictData = {
      sessionId: 'session_123',
      tableName: 'stock_data',
      conflictIndex: 0,
      conflictType: 'duplicate_key',
      conflictDetails: {
        existingRecord: { id: 1, symbol: 'AAPL' },
        newRecord: { id: 1, symbol: 'AAPL', updated: true }
      }
    };

    wrapper.vm.showConflictDialog = true;
    wrapper.vm.currentConflict = conflictData;
    await nextTick();

    // 触发冲突解决
    const resolutionData = {
      sessionId: 'session_123',
      conflictIndex: 0,
      resolution: 'replace'
    };

    wrapper.vm.resolveConflict(resolutionData);
    await flushPromises();

    expect(dataCenterApi.dataCenterAPI.resolveConflict).toHaveBeenCalledWith(resolutionData);
  });

  it('应该正确提供性能指标接口', () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    const metrics = wrapper.vm.getPerformanceMetrics();

    expect(metrics).toHaveProperty('importStatus');
    expect(metrics).toHaveProperty('activeImportType');
    expect(metrics).toHaveProperty('isImporting');
    expect(metrics).toHaveProperty('totalImports');
    expect(metrics).toHaveProperty('successRate');
  });

  it('应该正确处理Tab切换', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 测试切换到MySQL Tab
    wrapper.vm.activeImportTab = 'mysql';
    await nextTick();

    expect(wrapper.vm.activeImportTab).toBe('mysql');

    // 测试切换到历史Tab
    wrapper.vm.activeImportTab = 'history';
    await nextTick();

    expect(wrapper.vm.activeImportTab).toBe('history');
    // 历史Tab切换时应该自动加载历史数据
    expect(dataCenterApi.dataCenterAPI.getImportHistory).toHaveBeenCalled();
  });

  it('应该正确处理导入会话控制', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 设置导入中状态
    wrapper.vm.importInProgress = true;
    wrapper.vm.currentSessionId = 'session_123';

    await nextTick();

    // 测试暂停导入
    if (wrapper.vm.canPauseImport) {
      wrapper.vm.pauseImport();
      await flushPromises();

      expect(dataCenterApi.dataCenterAPI.pauseImport).toHaveBeenCalledWith('session_123');
    }

    // 测试恢复导入
    wrapper.vm.importPaused = true;
    if (wrapper.vm.canResumeImport) {
      wrapper.vm.resumeImport();
      await flushPromises();

      expect(dataCenterApi.dataCenterAPI.resumeImport).toHaveBeenCalledWith('session_123');
    }

    // 测试取消导入
    if (wrapper.vm.canCancelImport) {
      wrapper.vm.cancelImport();
      await flushPromises();

      expect(dataCenterApi.dataCenterAPI.cancelImport).toHaveBeenCalledWith('session_123');
    }
  });

  it('应该正确验证FromC2C文件格式', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 创建FromC2C格式的测试文件
    const csvContent =
      'index,open,close,high,low,volume,money,open_interest,contract_code,date\n1,100,101,102,99,1000,100000,500,RB2501.XSHE,2024-01-01';
    const file = new File([csvContent], 'test.csv', { type: 'text/csv' });

    // 测试FromC2C文件验证
    const validation = await wrapper.vm.validateFromC2CFile(file);

    expect(validation.valid).toBe(true);
    expect(validation.message).toContain('FromC2C格式验证通过');
    expect(validation.metadata).toHaveProperty('columns');
    expect(validation.metadata).toHaveProperty('estimatedRows');
  });

  it('应该正确处理无效的FromC2C文件', async () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 创建无效格式的测试文件
    const csvContent = 'invalid,header\n1,value';
    const file = new File([csvContent], 'invalid.csv', { type: 'text/csv' });

    // 测试无效文件验证
    const validation = await wrapper.vm.validateFromC2CFile(file);

    expect(validation.valid).toBe(false);
    expect(validation.error).toContain('缺少必要列');
    expect(validation.suggestions).toBeDefined();
    expect(validation.suggestions.length).toBeGreaterThan(0);
  });

  it('应该正确格式化显示数据', () => {
    wrapper = mount(UnifiedDataImport, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    // 测试文件大小格式化
    expect(wrapper.vm.formatFileSize(1024)).toBe('1.00 KB');
    expect(wrapper.vm.formatFileSize(1048576)).toBe('1.00 MB');
    expect(wrapper.vm.formatFileSize(1073741824)).toBe('1.00 GB');

    // 测试持续时间格式化
    expect(wrapper.vm.formatDuration(30000)).toBe('30s');
    expect(wrapper.vm.formatDuration(90000)).toBe('1m 30s');
    expect(wrapper.vm.formatDuration(3661000)).toBe('1h 1m 1s');

    // 测试进度百分比
    expect(wrapper.vm.formatProgress(0.5)).toBe('50%');
    expect(wrapper.vm.formatProgress(0.856)).toBe('86%');
  });
});
