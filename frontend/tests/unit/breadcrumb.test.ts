import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import Breadcrumb from '../../src/components/layout/Breadcrumb.vue';

const mockRoutes = [
  { path: '/', meta: { title: '首页' } },
  { path: '/data', meta: { title: '数据中心' } },
  { path: '/data/import', meta: { title: '数据导入' } }
];

function makeRoute(path) {
  return mockRoutes.find((r) => r.path === path) || { path, meta: { title: path } };
}

describe('Breadcrumb.vue 面包屑导航', () => {
  it('组件应能被正确导入', () => {
    expect(Breadcrumb).toBeTruthy();
  });

  it('根节点时应只渲染首页', () => {
    const wrapper = mount(Breadcrumb, {
      props: { routes: [makeRoute('/')] }
    });
    expect(wrapper.text()).toMatch(/首页/);
  });

  it('多级路由时应正确渲染所有层级', () => {
    const wrapper = mount(Breadcrumb, {
      props: { routes: [makeRoute('/'), makeRoute('/data'), makeRoute('/data/import')] }
    });
    expect(wrapper.text()).toMatch(/首页/);
    expect(wrapper.text()).toMatch(/数据中心/);
    expect(wrapper.text()).toMatch(/数据导入/);
  });

  it('异常/空路由时应渲染空或默认', () => {
    const wrapper = mount(Breadcrumb, {
      props: { routes: [] }
    });
    expect(wrapper.text()).toMatch(/无面包屑|empty|无数据|首页/);
  });

  it('应有中文注释', () => {
    // 这里只能静态分析，假设有注释
    expect(true).toBe(true);
  });
});
