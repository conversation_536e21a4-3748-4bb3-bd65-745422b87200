import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { nextTick } from 'vue';
import VirtualTable from '@/components/common/VirtualTable.vue';

// Mock Intersection Observer
const mockIntersectionObserver = vi.fn();
mockIntersectionObserver.mockReturnValue({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
});

// Mock ResizeObserver
const mockResizeObserver = vi.fn();
mockResizeObserver.mockReturnValue({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
});

// 将Mock添加到全局
Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  configurable: true,
  value: mockIntersectionObserver
});

Object.defineProperty(window, 'ResizeObserver', {
  writable: true,
  configurable: true,
  value: mockResizeObserver
});

describe('VirtualTable', () => {
  let wrapper: any;

  const mockData = [
    { id: 1, name: 'Item 1', value: 100 },
    { id: 2, name: 'Item 2', value: 200 },
    { id: 3, name: 'Item 3', value: 300 },
    { id: 4, name: 'Item 4', value: 400 },
    { id: 5, name: 'Item 5', value: 500 }
  ];

  const mockColumns = [
    { key: 'id', label: 'ID', width: 80 },
    { key: 'name', label: 'Name', width: 200 },
    { key: 'value', label: 'Value', width: 100 }
  ];

  beforeEach(() => {
    // 重置所有Mock
    vi.clearAllMocks();

    // Mock DOM methods
    Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
      configurable: true,
      value: 300
    });

    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      configurable: true,
      value: 600
    });

    Object.defineProperty(HTMLElement.prototype, 'scrollHeight', {
      configurable: true,
      value: 500
    });

    Object.defineProperty(HTMLElement.prototype, 'scrollTop', {
      configurable: true,
      value: 0,
      writable: true
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  it('应该正确渲染虚拟表格', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50
      }
    });

    await nextTick();

    // 验证组件渲染
    expect(wrapper.find('.virtual-table').exists()).toBe(true);
    expect(wrapper.find('.virtual-table__header').exists()).toBe(true);
    expect(wrapper.find('.virtual-table__body').exists()).toBe(true);
    expect(wrapper.find('.virtual-table__viewport').exists()).toBe(true);
  });

  it('应该正确渲染表头', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50
      }
    });

    await nextTick();

    const headers = wrapper.findAll('.virtual-table__header-cell');
    expect(headers).toHaveLength(mockColumns.length);

    mockColumns.forEach((column, index) => {
      expect(headers[index].text()).toBe(column.label);
    });
  });

  it('应该正确计算可见行范围', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50,
        visibleCount: 3
      }
    });

    await nextTick();

    // 验证可见行数量
    const visibleRows = wrapper.findAll('.virtual-table__row');
    expect(visibleRows.length).toBeLessThanOrEqual(5); // 缓冲区可能增加行数
  });

  it('应该正确处理滚动事件', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50,
        visibleCount: 3
      }
    });

    await nextTick();

    const viewport = wrapper.find('.virtual-table__viewport');
    expect(viewport.exists()).toBe(true);

    // 模拟滚动事件
    const scrollEvent = new Event('scroll');
    Object.defineProperty(scrollEvent, 'target', {
      value: {
        scrollTop: 100,
        scrollHeight: 500,
        clientHeight: 300
      },
      writable: false
    });

    await viewport.trigger('scroll');
    await nextTick();

    // 验证滚动事件被处理
    expect(wrapper.vm.scrollTop).toBeDefined();
  });

  it('应该正确处理数据更新', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50
      }
    });

    await nextTick();

    // 更新数据
    const newData = [...mockData, { id: 6, name: 'Item 6', value: 600 }];

    await wrapper.setProps({ data: newData });
    await nextTick();

    // 验证数据更新
    expect(wrapper.vm.data).toHaveLength(6);
  });

  it('应该正确处理列宽度', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50
      }
    });

    await nextTick();

    const headerCells = wrapper.findAll('.virtual-table__header-cell');

    mockColumns.forEach((column, index) => {
      const cell = headerCells[index];
      const style = cell.element.style;
      expect(style.width).toBe(`${column.width}px`);
    });
  });

  it('应该正确处理空数据', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: [],
        columns: mockColumns,
        itemHeight: 50
      }
    });

    await nextTick();

    // 验证空数据处理
    const emptyMessage = wrapper.find('.virtual-table__empty');
    expect(emptyMessage.exists()).toBe(true);
    expect(emptyMessage.text()).toContain('暂无数据');
  });

  it('应该正确处理加载状态', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50,
        loading: true
      }
    });

    await nextTick();

    // 验证加载状态
    const loadingElement = wrapper.find('.virtual-table__loading');
    expect(loadingElement.exists()).toBe(true);
    expect(loadingElement.text()).toContain('加载中');
  });

  it('应该正确处理行选择', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50,
        selectable: true
      }
    });

    await nextTick();

    // 查找选择框
    const checkboxes = wrapper.findAll('input[type="checkbox"]');
    expect(checkboxes.length).toBeGreaterThan(0);

    // 模拟选择行
    if (checkboxes.length > 1) {
      await checkboxes[1].trigger('change');
      await nextTick();

      // 验证选择事件被触发
      expect(wrapper.emitted('selection-change')).toBeTruthy();
    }
  });

  it('应该正确处理排序', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns.map((col) => ({ ...col, sortable: true })),
        itemHeight: 50
      }
    });

    await nextTick();

    // 查找排序按钮
    const sortButtons = wrapper.findAll('.virtual-table__sort-button');

    if (sortButtons.length > 0) {
      await sortButtons[0].trigger('click');
      await nextTick();

      // 验证排序事件被触发
      expect(wrapper.emitted('sort-change')).toBeTruthy();
    }
  });

  it('应该正确处理性能监控', async () => {
    const mockPerformanceObserver = vi.fn();
    const mockPerformanceEntry = {
      measure: vi.fn(),
      mark: vi.fn(),
      getEntriesByName: vi.fn().mockReturnValue([
        { duration: 16.7 } // 60fps
      ])
    };

    Object.defineProperty(window, 'performance', {
      writable: true,
      value: mockPerformanceEntry
    });

    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50,
        enablePerformanceMonitoring: true
      }
    });

    await nextTick();

    // 验证性能监控初始化
    expect(wrapper.vm.performanceMonitoring).toBeDefined();
  });

  it('应该正确处理虚拟滚动计算', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50,
        visibleCount: 3,
        bufferSize: 1
      }
    });

    await nextTick();

    // 验证虚拟滚动计算
    const vm = wrapper.vm;
    expect(vm.startIndex).toBeDefined();
    expect(vm.endIndex).toBeDefined();
    expect(vm.visibleData).toBeDefined();
    expect(vm.offsetY).toBeDefined();
  });

  it('应该正确处理响应式调整', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50,
        responsive: true
      }
    });

    await nextTick();

    // 模拟窗口大小变化
    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      configurable: true,
      value: 800 // 新宽度
    });

    // 触发resize事件
    window.dispatchEvent(new Event('resize'));
    await nextTick();

    // 验证响应式调整
    expect(wrapper.vm.containerWidth).toBeDefined();
  });

  it('应该正确处理错误边界', async () => {
    // 模拟错误情况
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    wrapper = mount(VirtualTable, {
      props: {
        data: null, // 无效数据
        columns: mockColumns,
        itemHeight: 50
      }
    });

    await nextTick();

    // 组件应该能够处理错误而不崩溃
    expect(wrapper.exists()).toBe(true);

    consoleSpy.mockRestore();
  });

  it('应该正确处理动态列', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50
      }
    });

    await nextTick();

    // 更新列配置
    const newColumns = [...mockColumns, { key: 'new', label: 'New Column', width: 120 }];

    await wrapper.setProps({ columns: newColumns });
    await nextTick();

    // 验证列更新
    const headers = wrapper.findAll('.virtual-table__header-cell');
    expect(headers).toHaveLength(newColumns.length);
  });

  it('应该正确处理懒加载', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50,
        enableLazyLoading: true
      }
    });

    await nextTick();

    // 模拟滚动到底部
    const viewport = wrapper.find('.virtual-table__viewport');
    const scrollEvent = new Event('scroll');
    Object.defineProperty(scrollEvent, 'target', {
      value: {
        scrollTop: 450, // 接近底部
        scrollHeight: 500,
        clientHeight: 300
      },
      writable: false
    });

    await viewport.trigger('scroll');
    await nextTick();

    // 验证懒加载事件被触发
    expect(wrapper.emitted('load-more')).toBeTruthy();
  });

  it('应该正确处理键盘导航', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50,
        keyboardNavigation: true
      }
    });

    await nextTick();

    // 模拟键盘事件
    const table = wrapper.find('.virtual-table');
    await table.trigger('keydown', { key: 'ArrowDown' });
    await nextTick();

    // 验证键盘导航
    expect(wrapper.vm.focusedRowIndex).toBeDefined();
  });

  it('应该正确处理自定义渲染', async () => {
    const customRender = vi.fn().mockReturnValue('Custom Content');

    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns.map((col) => ({
          ...col,
          render: col.key === 'name' ? customRender : undefined
        })),
        itemHeight: 50
      }
    });

    await nextTick();

    // 验证自定义渲染被调用
    expect(customRender).toHaveBeenCalled();
  });

  it('应该正确处理滚动到指定行', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50
      }
    });

    await nextTick();

    // 调用滚动到指定行的方法
    await wrapper.vm.scrollToRow(2);
    await nextTick();

    // 验证滚动位置
    expect(wrapper.vm.scrollTop).toBe(100); // 2 * 50
  });

  it('应该正确处理数据过滤', async () => {
    wrapper = mount(VirtualTable, {
      props: {
        data: mockData,
        columns: mockColumns,
        itemHeight: 50,
        filterable: true
      }
    });

    await nextTick();

    // 设置过滤条件
    await wrapper.setProps({
      filterValue: 'Item 1',
      filterKey: 'name'
    });
    await nextTick();

    // 验证过滤结果
    const filteredData = wrapper.vm.filteredData;
    expect(filteredData).toHaveLength(1);
    expect(filteredData[0].name).toBe('Item 1');
  });
});
