import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { nextTick } from 'vue';
import { createPinia, setActivePinia } from 'pinia';
import DataCenterPage from '@/modules/data-center/DataCenterPage.vue';
import { useDataCenterStore } from '@/stores/data_center_store';
import * as dataCenterApi from '@/api/data_center_api';

// Mock API
vi.mock('@/api/data_center_api', () => ({
  getTables: vi.fn(),
  getTableData: vi.fn(),
  getTableMeta: vi.fn(),
  uploadCSVFile: vi.fn()
}));

// Mock Vue Router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn()
};

const mockRoute = {
  params: {},
  query: {},
  path: '/data-center'
};

describe('DataCenterPage', () => {
  let wrapper: any;
  let store: any;
  let pinia: any;

  const mockTables = [
    {
      name: 'stock_data',
      record_count: 1000,
      columns: [
        { column_name: 'id', column_type: 'INTEGER', null: 'NO' },
        { column_name: 'symbol', column_type: 'VARCHAR', null: 'NO' },
        { column_name: 'name', column_type: 'VARCHAR', null: 'YES' }
      ]
    },
    {
      name: 'futures_data',
      record_count: 500,
      columns: [
        { column_name: 'id', column_type: 'INTEGER', null: 'NO' },
        { column_name: 'symbol', column_type: 'VARCHAR', null: 'NO' },
        { column_name: 'price', column_type: 'DOUBLE', null: 'YES' }
      ]
    }
  ];

  const mockTableData = {
    data: [
      { id: 1, symbol: '000001', name: '平安银行', price: 10.5 },
      { id: 2, symbol: '000002', name: '万科A', price: 15.2 },
      { id: 3, symbol: '600000', name: '浦发银行', price: 8.8 }
    ],
    pagination: {
      total: 1000,
      limit: 10,
      offset: 0,
      has_next: true
    }
  };

  beforeEach(() => {
    // 创建新的Pinia实例
    pinia = createPinia();
    setActivePinia(pinia);

    // 重置Mock
    vi.clearAllMocks();

    // 设置API Mock返回值
    vi.mocked(dataCenterApi.getTables).mockResolvedValue({
      success: true,
      data: mockTables,
      total_count: mockTables.length
    });

    vi.mocked(dataCenterApi.getTableData).mockResolvedValue({
      success: true,
      ...mockTableData
    });

    vi.mocked(dataCenterApi.getTableMeta).mockResolvedValue({
      success: true,
      data: {
        table_name: 'stock_data',
        record_count: 1000,
        column_count: 3,
        columns: mockTables[0].columns
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  it('应该正确渲染数据中心页面', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 验证主要组件渲染
    expect(wrapper.find('.data-center-page').exists()).toBe(true);
    expect(wrapper.find('.data-center-header').exists()).toBe(true);
    expect(wrapper.find('.data-center-content').exists()).toBe(true);
  });

  it('应该正确加载表格列表', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 验证API调用
    expect(dataCenterApi.getTables).toHaveBeenCalledWith('dev');

    // 验证表格列表渲染
    const tableCards = wrapper.findAll('.table-card');
    expect(tableCards).toHaveLength(mockTables.length);

    // 验证表格信息显示
    const firstCard = tableCards[0];
    expect(firstCard.text()).toContain('stock_data');
    expect(firstCard.text()).toContain('1000');
  });

  it('应该正确处理表格选择', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 查找第一个表格卡片
    const tableCards = wrapper.findAll('.table-card');
    if (tableCards.length > 0) {
      await tableCards[0].trigger('click');
      await nextTick();

      // 验证表格数据加载
      expect(dataCenterApi.getTableData).toHaveBeenCalledWith(
        'stock_data',
        'dev',
        expect.any(Object)
      );
    }
  });

  it('应该正确显示表格数据', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 选择表格
    const tableCards = wrapper.findAll('.table-card');
    if (tableCards.length > 0) {
      await tableCards[0].trigger('click');
      await flushPromises();

      // 验证数据表格显示
      const dataTable = wrapper.find('.data-table');
      expect(dataTable.exists()).toBe(true);

      // 验证表格数据
      const dataRows = wrapper.findAll('.data-row');
      expect(dataRows.length).toBeGreaterThan(0);
    }
  });

  it('应该正确处理分页', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 选择表格
    const tableCards = wrapper.findAll('.table-card');
    if (tableCards.length > 0) {
      await tableCards[0].trigger('click');
      await flushPromises();

      // 查找分页组件
      const pagination = wrapper.find('.pagination');
      if (pagination.exists()) {
        // 模拟点击下一页
        const nextButton = pagination.find('.pagination-next');
        if (nextButton.exists()) {
          await nextButton.trigger('click');
          await flushPromises();

          // 验证API调用包含正确的分页参数
          expect(dataCenterApi.getTableData).toHaveBeenCalledWith(
            'stock_data',
            'dev',
            expect.objectContaining({
              offset: 10
            })
          );
        }
      }
    }
  });

  it('应该正确处理搜索功能', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 查找搜索框
    const searchInput = wrapper.find('.search-input');
    if (searchInput.exists()) {
      // 输入搜索内容
      await searchInput.setValue('stock');
      await searchInput.trigger('input');
      await nextTick();

      // 验证搜索过滤
      const filteredCards = wrapper.findAll('.table-card:not(.hidden)');
      expect(filteredCards.length).toBeLessThanOrEqual(mockTables.length);
    }
  });

  it('应该正确处理CSV文件上传', async () => {
    vi.mocked(dataCenterApi.uploadCSVFile).mockResolvedValue({
      success: true,
      data: {
        table_name: 'new_table',
        record_count: 100,
        message: '上传成功'
      }
    });

    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 查找上传按钮
    const uploadButton = wrapper.find('.upload-button');
    if (uploadButton.exists()) {
      await uploadButton.trigger('click');
      await nextTick();

      // 验证上传对话框显示
      const uploadDialog = wrapper.find('.upload-dialog');
      expect(uploadDialog.exists()).toBe(true);

      // 模拟文件选择
      const fileInput = wrapper.find('input[type="file"]');
      if (fileInput.exists()) {
        const file = new File(['test,data'], 'test.csv', { type: 'text/csv' });
        Object.defineProperty(fileInput.element, 'files', {
          value: [file]
        });

        await fileInput.trigger('change');
        await nextTick();

        // 查找确认上传按钮
        const confirmButton = wrapper.find('.confirm-upload');
        if (confirmButton.exists()) {
          await confirmButton.trigger('click');
          await flushPromises();

          // 验证上传API调用
          expect(dataCenterApi.uploadCSVFile).toHaveBeenCalledWith(file, 'dev');
        }
      }
    }
  });

  it('应该正确处理加载状态', async () => {
    // 设置延迟的API响应
    vi.mocked(dataCenterApi.getTables).mockImplementation(
      () =>
        new Promise((resolve) => {
          setTimeout(
            () =>
              resolve({
                success: true,
                data: mockTables,
                total_count: mockTables.length
              }),
            100
          );
        })
    );

    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    // 验证加载状态
    expect(wrapper.find('.loading').exists()).toBe(true);

    await flushPromises();

    // 验证加载完成
    expect(wrapper.find('.loading').exists()).toBe(false);
  });

  it('应该正确处理错误状态', async () => {
    // 设置API错误响应
    vi.mocked(dataCenterApi.getTables).mockRejectedValue(new Error('网络错误'));

    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 验证错误状态显示
    const errorMessage = wrapper.find('.error-message');
    expect(errorMessage.exists()).toBe(true);
    expect(errorMessage.text()).toContain('加载失败');
  });

  it('应该正确处理空数据状态', async () => {
    vi.mocked(dataCenterApi.getTables).mockResolvedValue({
      success: true,
      data: [],
      total_count: 0
    });

    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 验证空数据状态
    const emptyState = wrapper.find('.empty-state');
    expect(emptyState.exists()).toBe(true);
    expect(emptyState.text()).toContain('暂无数据');
  });

  it('应该正确处理环境切换', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 查找环境选择器
    const envSelector = wrapper.find('.env-selector');
    if (envSelector.exists()) {
      await envSelector.setValue('test');
      await envSelector.trigger('change');
      await nextTick();

      // 验证重新加载数据
      expect(dataCenterApi.getTables).toHaveBeenCalledWith('test');
    }
  });

  it('应该正确处理表格排序', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 选择表格
    const tableCards = wrapper.findAll('.table-card');
    if (tableCards.length > 0) {
      await tableCards[0].trigger('click');
      await flushPromises();

      // 查找排序按钮
      const sortButton = wrapper.find('.sort-button');
      if (sortButton.exists()) {
        await sortButton.trigger('click');
        await nextTick();

        // 验证排序功能
        const sortedData = wrapper.vm.sortedData;
        expect(sortedData).toBeDefined();
      }
    }
  });

  it('应该正确处理表格过滤', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 选择表格
    const tableCards = wrapper.findAll('.table-card');
    if (tableCards.length > 0) {
      await tableCards[0].trigger('click');
      await flushPromises();

      // 查找过滤输入框
      const filterInput = wrapper.find('.filter-input');
      if (filterInput.exists()) {
        await filterInput.setValue('平安');
        await filterInput.trigger('input');
        await nextTick();

        // 验证过滤功能
        const filteredRows = wrapper.findAll('.data-row:not(.hidden)');
        expect(filteredRows.length).toBeLessThanOrEqual(mockTableData.data.length);
      }
    }
  });

  it('应该正确处理表格导出', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 选择表格
    const tableCards = wrapper.findAll('.table-card');
    if (tableCards.length > 0) {
      await tableCards[0].trigger('click');
      await flushPromises();

      // 查找导出按钮
      const exportButton = wrapper.find('.export-button');
      if (exportButton.exists()) {
        await exportButton.trigger('click');
        await nextTick();

        // 验证导出功能
        expect(wrapper.emitted('export')).toBeTruthy();
      }
    }
  });

  it('应该正确处理响应式布局', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 模拟移动端屏幕尺寸
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768
    });

    window.dispatchEvent(new Event('resize'));
    await nextTick();

    // 验证移动端布局
    const mobileLayout = wrapper.find('.mobile-layout');
    expect(mobileLayout.exists()).toBe(true);
  });

  it('应该正确处理键盘快捷键', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 模拟键盘快捷键
    await wrapper.trigger('keydown', { key: 'r', ctrlKey: true });
    await nextTick();

    // 验证刷新功能
    expect(dataCenterApi.getTables).toHaveBeenCalledTimes(2); // 初始加载 + 刷新
  });

  it('应该正确处理无限滚动', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 选择表格
    const tableCards = wrapper.findAll('.table-card');
    if (tableCards.length > 0) {
      await tableCards[0].trigger('click');
      await flushPromises();

      // 模拟滚动到底部
      const scrollContainer = wrapper.find('.scroll-container');
      if (scrollContainer.exists()) {
        await scrollContainer.trigger('scroll');
        await nextTick();

        // 验证是否触发加载更多
        expect(wrapper.vm.loadMore).toBeDefined();
      }
    }
  });

  it('应该正确处理数据刷新', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 查找刷新按钮
    const refreshButton = wrapper.find('.refresh-button');
    if (refreshButton.exists()) {
      await refreshButton.trigger('click');
      await nextTick();

      // 验证数据重新加载
      expect(dataCenterApi.getTables).toHaveBeenCalledTimes(2);
    }
  });

  it('应该正确处理Store集成', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 获取Store实例
    const store = useDataCenterStore();

    // 验证Store状态
    expect(store.tables).toEqual(mockTables);
    expect(store.loading).toBe(false);
    expect(store.error).toBe(null);
  });
});
