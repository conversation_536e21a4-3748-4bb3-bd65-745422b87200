import { describe, it, expect, beforeEach, vi } from 'vitest';
import { createP<PERSON>, setActivePinia } from 'pinia';
import { useDataCenterStore } from '../../src/stores/data_center_store';
import { dataCenterAPI } from '../../src/api/data_center_api';

// Mock API
vi.mock('../../src/api/data_center_api', () => ({
  dataCenterAPI: {
    getTables: vi.fn(),
    getTableData: vi.fn(),
    getTableMeta: vi.fn(),
    importCSV: vi.fn(),
    healthCheck: vi.fn()
  }
}));

describe('数据中心Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    vi.clearAllMocks();
  });

  describe('基础状态', () => {
    it('应该有正确的初始状态', () => {
      const store = useDataCenterStore();

      expect(store.tables).toEqual([]);
      expect(store.selectedTable).toBe(null);
      expect(store.tableData).toEqual([]);
      expect(store.tableColumns).toEqual([]);
      expect(store.totalRecords).toBe(0);
      expect(store.loading).toBe(false);
      expect(store.error).toBe(null);
    });

    it('应该有正确的计算属性', () => {
      const store = useDataCenterStore();

      expect(store.tableCount).toBe(0);
      expect(store.selectedTableInfo).toBe(null);
      expect(store.hasTableData).toBe(false);
      expect(store.isLoading).toBe(false);
    });
  });

  describe('获取表列表', () => {
    it('应该成功获取表列表', async () => {
      const store = useDataCenterStore();
      const mockResponse = {
        data: {
          success: true,
          data: [
            {
              name: 'test_table',
              record_count: 100,
              columns: [
                { name: 'id', type: 'INTEGER', nullable: 'NO', key: 'PRI', default: null },
                { name: 'name', type: 'VARCHAR', nullable: 'YES', key: '', default: null }
              ]
            }
          ]
        }
      };

      vi.mocked(dataCenterAPI.getTables).mockResolvedValue(mockResponse);

      await store.fetchTables('test');

      expect(dataCenterAPI.getTables).toHaveBeenCalledWith('test');
      expect(store.tables).toHaveLength(1);
      expect(store.tables[0].name).toBe('test_table');
      expect(store.tables[0].record_count).toBe(100);
      expect(store.error).toBe(null);
    });

    it('应该处理获取表列表失败', async () => {
      const store = useDataCenterStore();
      const mockError = new Error('API错误');

      vi.mocked(dataCenterAPI.getTables).mockRejectedValue(mockError);

      await expect(store.fetchTables('test')).rejects.toThrow('API错误');
      expect(store.error).toBe('API错误');
      expect(store.tables).toEqual([]);
    });
  });

  describe('获取表数据', () => {
    it('应该成功获取表数据', async () => {
      const store = useDataCenterStore();
      const mockTableDataResponse = {
        data: {
          success: true,
          data: [
            { id: 1, name: 'test1' },
            { id: 2, name: 'test2' }
          ],
          pagination: { total: 2, limit: 50, offset: 0 }
        }
      };

      const mockTableMetaResponse = {
        data: {
          success: true,
          data: {
            columns: [
              { column_name: 'id', column_type: 'INTEGER', null: 'NO', key: 'PRI', default: null },
              { column_name: 'name', column_type: 'VARCHAR', null: 'YES', key: '', default: null }
            ]
          }
        }
      };

      vi.mocked(dataCenterAPI.getTableData).mockResolvedValue(mockTableDataResponse);
      vi.mocked(dataCenterAPI.getTableMeta).mockResolvedValue(mockTableMetaResponse);

      await store.fetchTableData('test_table', { limit: 50, offset: 0 });

      expect(dataCenterAPI.getTableData).toHaveBeenCalledWith('test_table', {
        limit: 50,
        offset: 0,
        environment: 'test'
      });
      expect(store.selectedTable).toBe('test_table');
      expect(store.tableData).toHaveLength(2);
      expect(store.totalRecords).toBe(2);
      expect(store.tableColumns).toHaveLength(2);
      expect(store.error).toBe(null);
    });

    it('应该处理获取表数据失败', async () => {
      const store = useDataCenterStore();
      const mockError = new Error('表不存在');

      vi.mocked(dataCenterAPI.getTableData).mockRejectedValue(mockError);

      await expect(store.fetchTableData('non_existent_table')).rejects.toThrow('表不存在');
      expect(store.error).toBe('表不存在');
      expect(store.tableData).toEqual([]);
    });
  });

  describe('数据导入', () => {
    it('应该成功导入CSV数据', async () => {
      const store = useDataCenterStore();
      const mockFile = new File(['id,name\n1,test'], 'test.csv', { type: 'text/csv' });
      const mockImportResponse = {
        data: {
          success: true,
          data: {
            table_name: 'test_import',
            record_count: 1
          },
          message: '导入成功'
        }
      };

      const mockTablesResponse = {
        data: {
          success: true,
          data: []
        }
      };

      vi.mocked(dataCenterAPI.importCSV).mockResolvedValue(mockImportResponse);
      vi.mocked(dataCenterAPI.getTables).mockResolvedValue(mockTablesResponse);

      await store.importData(mockFile, { environment: 'test' });

      expect(dataCenterAPI.importCSV).toHaveBeenCalled();
      expect(store.importResult).toBeDefined();
      expect(store.importResult?.success).toBe(true);
      expect(store.importProgress).toBe(100);
      expect(store.error).toBe(null);
    });

    it('应该处理导入失败', async () => {
      const store = useDataCenterStore();
      const mockFile = new File(['invalid data'], 'test.csv', { type: 'text/csv' });
      const mockError = new Error('文件格式错误');

      vi.mocked(dataCenterAPI.importCSV).mockRejectedValue(mockError);

      await expect(store.importData(mockFile)).rejects.toThrow('文件格式错误');
      expect(store.error).toBe('文件格式错误');
      expect(store.importResult).toBe(null);
    });
  });

  describe('工具方法', () => {
    it('应该正确转换CSV格式', () => {
      const store = useDataCenterStore();
      const testData = [
        { id: 1, name: 'test1', value: 100.5 },
        { id: 2, name: 'test2', value: 200.7 }
      ];

      const csvContent = store.convertToCSV(testData);

      expect(csvContent).toContain('id,name,value');
      expect(csvContent).toContain('1,test1,100.5');
      expect(csvContent).toContain('2,test2,200.7');
    });

    it('应该处理空数据', () => {
      const store = useDataCenterStore();
      const csvContent = store.convertToCSV([]);

      expect(csvContent).toBe('');
    });

    it('应该处理包含逗号的数据', () => {
      const store = useDataCenterStore();
      const testData = [{ id: 1, name: 'test, with comma', value: 100.5 }];

      const csvContent = store.convertToCSV(testData);

      expect(csvContent).toContain('"test, with comma"');
    });
  });

  describe('状态管理', () => {
    it('应该正确重置状态', () => {
      const store = useDataCenterStore();

      // 设置一些状态
      store.selectedTable = 'test_table';
      store.tableData = [{ id: 1, name: 'test' }];
      store.error = '测试错误';

      // 重置状态
      store.reset();

      expect(store.selectedTable).toBe(null);
      expect(store.tableData).toEqual([]);
      expect(store.error).toBe(null);
    });

    it('应该正确清除错误', () => {
      const store = useDataCenterStore();

      store.error = '测试错误';
      store.clearError();

      expect(store.error).toBe(null);
    });
  });
});

describe('数据中心API', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该有正确的API方法', () => {
    expect(dataCenterAPI.getTables).toBeDefined();
    expect(dataCenterAPI.getTableData).toBeDefined();
    expect(dataCenterAPI.getTableMeta).toBeDefined();
    expect(dataCenterAPI.importCSV).toBeDefined();
    expect(dataCenterAPI.healthCheck).toBeDefined();
  });
});
