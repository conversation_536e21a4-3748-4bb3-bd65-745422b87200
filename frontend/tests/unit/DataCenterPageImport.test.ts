import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { nextTick } from 'vue';
import { createPinia, setActivePinia } from 'pinia';
import DataCenterPage from '@/modules/data-center/DataCenterPage.vue';
import { useDataCenterStore } from '@/stores/data_center_store';
import * as dataCenterApi from '@/api/data_center_api';

// Mock API
vi.mock('@/api/data_center_api', () => ({
  dataCenterAPI: {
    getStockData: vi.fn(),
    getFuturesData: vi.fn(),
    getTables: vi.fn(),
    getTableData: vi.fn(),
    getTableMeta: vi.fn(),
    getImportHistory: vi.fn(),
    getCacheStats: vi.fn(),
    clearCache: vi.fn()
  }
}));

// Mock UnifiedDataImport组件
vi.mock('@/modules/data-center/components/UnifiedDataImport.vue', () => ({
  default: {
    name: 'UnifiedDataImport',
    template: '<div class="unified-data-import-mock">Mock UnifiedDataImport Component</div>',
    methods: {
      getPerformanceMetrics: vi.fn(() => ({
        importStatus: 'idle',
        activeImportType: 'csv',
        isImporting: false,
        totalImports: 10,
        successRate: 85
      })),
      refreshImportStatus: vi.fn()
    }
  }
}));

// Mock Vue Router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn()
};

const mockRoute = {
  params: {},
  query: {},
  path: '/data-center'
};

describe('DataCenterPage 数据导入功能测试', () => {
  let wrapper: any;
  let store: any;
  let pinia: any;

  const mockStockData = [
    { symbol: 'AAPL', name: '苹果公司', close: 150.25, trade_date: '2025-07-22' },
    { symbol: 'GOOGL', name: '谷歌', close: 2800.5, trade_date: '2025-07-22' }
  ];

  const mockFuturesData = [
    { contract_code: 'RB2501.XSHE', close: 3500.0, trade_datetime: '2025-07-22T15:00:00Z' },
    { contract_code: 'HC2501.XSHE', close: 3200.0, trade_datetime: '2025-07-22T15:00:00Z' }
  ];

  beforeEach(() => {
    // 创建新的Pinia实例
    pinia = createPinia();
    setActivePinia(pinia);

    // 重置Mock
    vi.clearAllMocks();

    // 设置API Mock返回值
    vi.mocked(dataCenterApi.dataCenterAPI.getStockData).mockResolvedValue({
      data: {
        success: true,
        data: mockStockData,
        pagination: {
          total: 1000,
          limit: 50,
          offset: 0,
          has_next: true
        }
      }
    });

    vi.mocked(dataCenterApi.dataCenterAPI.getFuturesData).mockResolvedValue({
      data: {
        success: true,
        data: mockFuturesData,
        pagination: {
          total: 500,
          limit: 50,
          offset: 0,
          has_next: true
        }
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  it('应该正确渲染包含数据导入Tab的数据中心页面', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 验证基本页面结构
    expect(wrapper.find('.data-center-page').exists()).toBe(true);
    expect(wrapper.find('.page-header').exists()).toBe(true);
    expect(wrapper.find('.data-content').exists()).toBe(true);

    // 验证所有Tab存在
    expect(wrapper.text()).toContain('股票数据');
    expect(wrapper.text()).toContain('期货数据');
    expect(wrapper.text()).toContain('数据导入');
  });

  it('应该正确切换到数据导入Tab', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 切换到导入Tab
    const importTabButton = wrapper.find('[data-name="import"]');
    if (importTabButton.exists()) {
      await importTabButton.trigger('click');
      await nextTick();

      expect(wrapper.vm.activeTab).toBe('import');
    }
  });

  it('应该正确渲染UnifiedDataImport组件', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 切换到导入Tab
    wrapper.vm.activeTab = 'import';
    await nextTick();

    // 验证UnifiedDataImport组件渲染
    const importComponent = wrapper.findComponent({ name: 'UnifiedDataImport' });
    expect(importComponent.exists()).toBe(true);
  });

  it('应该在导入Tab下正确执行刷新功能', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 切换到导入Tab
    wrapper.vm.activeTab = 'import';
    await nextTick();

    // 点击刷新按钮
    const refreshButton = wrapper.find('[data-testid="refresh-button"]');
    if (refreshButton.exists()) {
      await refreshButton.trigger('click');
      await flushPromises();

      // 验证刷新导入状态被调用
      expect(wrapper.vm.refreshing).toBe(false);
    }
  });

  it('应该正确处理导入Tab的刷新逻辑', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 设置为导入Tab
    wrapper.vm.activeTab = 'import';

    // 调用刷新方法
    await wrapper.vm.refreshData();

    // 验证没有调用股票或期货数据API
    expect(dataCenterApi.dataCenterAPI.getStockData).not.toHaveBeenCalled();
    expect(dataCenterApi.dataCenterAPI.getFuturesData).not.toHaveBeenCalled();
  });

  it('应该正确处理性能统计功能对导入Tab的支持', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 启用性能监控
    wrapper.vm.enablePerformanceMonitoring = true;
    wrapper.vm.activeTab = 'import';
    await nextTick();

    // 点击性能统计按钮
    const performanceButton = wrapper.find('[data-testid="performance-stats-button"]');
    if (performanceButton.exists()) {
      await performanceButton.trigger('click');
      await nextTick();

      // 验证性能统计模态框显示
      expect(wrapper.vm.showPerformanceModal).toBe(true);
      expect(wrapper.vm.performanceStats).toBeDefined();
    }
  });

  it('应该在不同Tab间正确维护状态', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 初始状态应该是股票Tab
    expect(wrapper.vm.activeTab).toBe('stock');

    // 切换到期货Tab
    wrapper.vm.activeTab = 'futures';
    await nextTick();
    expect(wrapper.vm.activeTab).toBe('futures');

    // 切换到导入Tab
    wrapper.vm.activeTab = 'import';
    await nextTick();
    expect(wrapper.vm.activeTab).toBe('import');

    // 切换回股票Tab
    wrapper.vm.activeTab = 'stock';
    await nextTick();
    expect(wrapper.vm.activeTab).toBe('stock');
  });

  it('应该正确处理Tab切换时的数据加载', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 验证初始股票数据加载
    expect(dataCenterApi.dataCenterAPI.getStockData).toHaveBeenCalled();

    // 切换到期货Tab并刷新
    wrapper.vm.activeTab = 'futures';
    await wrapper.vm.refreshData();

    expect(dataCenterApi.dataCenterAPI.getFuturesData).toHaveBeenCalled();

    // 切换到导入Tab并刷新
    wrapper.vm.activeTab = 'import';
    await wrapper.vm.refreshData();

    // 验证导入相关的刷新逻辑
    // 这里主要验证没有错误发生
    expect(wrapper.vm.activeTab).toBe('import');
  });

  it('应该正确响应键盘快捷键在导入Tab下的行为', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 切换到导入Tab
    wrapper.vm.activeTab = 'import';
    await nextTick();

    // 模拟Ctrl+R快捷键
    await wrapper.trigger('keydown', { key: 'r', ctrlKey: true });
    await nextTick();

    // 验证刷新被触发（通过检查refreshing状态变化）
    // 这里主要验证快捷键在导入Tab下不会报错
    expect(wrapper.vm.activeTab).toBe('import');
  });

  it('应该正确处理导入Tab的错误状态', async () => {
    // Mock刷新导入状态失败
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 设置为导入Tab
    wrapper.vm.activeTab = 'import';

    // 模拟刷新导入状态失败
    const originalRefreshImportStatus = wrapper.vm.refreshImportStatus;
    wrapper.vm.refreshImportStatus = vi.fn().mockRejectedValue(new Error('刷新失败'));

    try {
      await wrapper.vm.refreshData();
    } catch (error) {
      expect(error.message).toBe('刷新失败');
    }

    consoleSpy.mockRestore();
  });

  it('应该正确处理导入组件的性能指标', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 切换到导入Tab
    wrapper.vm.activeTab = 'import';
    await nextTick();

    // 获取导入组件引用
    const importComponent = wrapper.findComponent({ name: 'UnifiedDataImport' });
    if (importComponent.exists()) {
      // 验证性能指标可以正确获取
      const metrics = importComponent.vm.getPerformanceMetrics();
      expect(metrics).toBeDefined();
      expect(metrics).toHaveProperty('importStatus');
      expect(metrics).toHaveProperty('activeImportType');
      expect(metrics).toHaveProperty('isImporting');
    }
  });

  it('应该正确维护导入Tab的响应式设计', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 切换到导入Tab
    wrapper.vm.activeTab = 'import';
    await nextTick();

    // 模拟屏幕尺寸变化
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768 // 移动端尺寸
    });

    window.dispatchEvent(new Event('resize'));
    await nextTick();

    // 验证导入Tab在移动端的布局
    const dataCenterPage = wrapper.find('.data-center-page');
    expect(dataCenterPage.exists()).toBe(true);
  });

  it('应该正确处理导入Tab的清理缓存功能', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 切换到导入Tab
    wrapper.vm.activeTab = 'import';
    await nextTick();

    // 调用清理缓存
    wrapper.vm.clearTableCache();

    // 验证清理缓存不会对导入Tab产生负面影响
    expect(wrapper.vm.activeTab).toBe('import');
  });

  it('应该正确集成导入组件的刷新状态方法', async () => {
    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 调用refreshImportStatus方法
    await wrapper.vm.refreshImportStatus();

    // 验证方法执行没有错误
    expect(wrapper.vm.refreshImportStatus).toBeDefined();
  });

  it('应该正确处理导入Tab在生产环境下的行为', async () => {
    // 模拟生产环境
    const originalNodeEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    wrapper = mount(DataCenterPage, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter,
          $route: mockRoute
        }
      }
    });

    await flushPromises();

    // 验证性能监控在生产环境下被禁用
    expect(wrapper.vm.enablePerformanceMonitoring).toBe(false);

    // 切换到导入Tab
    wrapper.vm.activeTab = 'import';
    await nextTick();

    // 验证性能统计按钮不显示
    const performanceButton = wrapper.find('[data-testid="performance-stats-button"]');
    expect(performanceButton.exists()).toBe(false);

    // 恢复环境变量
    process.env.NODE_ENV = originalNodeEnv;
  });
});
