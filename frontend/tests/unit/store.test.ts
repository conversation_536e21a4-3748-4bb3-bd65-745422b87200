import { describe, it, expect, beforeEach } from 'vitest';
import { createPinia, setActive<PERSON><PERSON>, defineStore } from 'pinia';
import { mount } from '@vue/test-utils';
import { createApp, defineComponent } from 'vue';

// 测试Pinia全局注册

describe('Pinia全局注册与状态变更', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it('store目录应存在且可定义store', () => {
    // 模拟定义一个简单store
    const useTestStore = defineStore('test', {
      state: () => ({ count: 0 }),
      actions: {
        increment() {
          this.count++;
        }
      }
    });
    const store = useTestStore();
    expect(store.count).toBe(0);
    store.increment();
    expect(store.count).toBe(1);
  });

  it('main.ts应全局注册Pinia', () => {
    // 修正版：注册后应能正常创建store并响应式读写
    const app = createApp(defineComponent({ template: '<div />' }));
    const pinia = createPinia();
    app.use(pinia);
    const useTestStore = defineStore('main_test', {
      state: () => ({ value: 1 }),
      actions: {
        inc() {
          this.value++;
        }
      }
    });
    setActivePinia(pinia);
    const store = useTestStore();
    expect(store.value).toBe(1);
    store.inc();
    expect(store.value).toBe(2);
  });

  it('组件中可读写store状态', async () => {
    // 定义store
    const useTestStore = defineStore('test2', {
      state: () => ({ value: 'AQUA' }),
      actions: {
        setValue(v: string) {
          this.value = v;
        }
      }
    });
    // 定义组件
    const Comp = defineComponent({
      template: `<div>{{ value }}</div>`,
      setup() {
        const store = useTestStore();
        return { value: store.value };
      }
    });
    const wrapper = mount(Comp, {
      global: { plugins: [createPinia()] }
    });
    expect(wrapper.text()).toBe('AQUA');
  });
});
