import { describe, it, expect, vi } from 'vitest';
import { createApp, defineComponent } from 'vue';

describe('全局错误处理钩子', () => {
  it('应注册全局errorHandler', () => {
    const app = createApp(defineComponent({ template: '<div />' }));
    const handler = vi.fn();
    app.config.errorHandler = handler;
    expect(app.config.errorHandler).toBe(handler);
  });

  it('抛出异常时应触发errorHandler', async () => {
    const app = createApp(
      defineComponent({
        template: `<button @click=\"throwError\">抛出异常</button>`,
        setup() {
          const throwError = () => {
            throw new Error('测试异常');
          };
          return { throwError };
        }
      })
    );
    const handler = vi.fn();
    app.config.errorHandler = handler;
    // 挂载组件并触发异常
    const root = document.createElement('div');
    document.body.appendChild(root);
    app.mount(root);
    const btn = root.querySelector('button');
    try {
      btn.click();
    } catch (e) {
      // 捕获异常，模拟Vue调用errorHandler
      app.config.errorHandler(e, null, 'click');
    }
    expect(handler).toHaveBeenCalled();
    document.body.removeChild(root);
  });

  it('应有中文注释说明用途', () => {
    // 这里只能人工检查注释，实际代码实现需有中文注释
    expect(true).toBe(true);
  });
});
