import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { dataCenterAPI } from '@/api/data_center_api';
import { apiClient } from '@/api/index';

// Mock axios客户端
vi.mock('@/api/index', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn()
  }
}));

describe('数据中心API测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('基础数据API', () => {
    it('应该正确调用获取表列表API', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: [
            { name: 'stock_data', record_count: 1000 },
            { name: 'futures_data', record_count: 500 }
          ]
        }
      };

      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const result = await dataCenterAPI.getTables('test');

      expect(apiClient.get).toHaveBeenCalledWith('/api/data/tables', {
        params: { environment: 'test' }
      });
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用获取表数据API', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: [
            { id: 1, symbol: 'AAPL', name: '苹果公司' },
            { id: 2, symbol: 'GOOGL', name: '谷歌' }
          ],
          pagination: {
            total: 100,
            limit: 10,
            offset: 0,
            has_next: true
          }
        }
      };

      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const result = await dataCenterAPI.getTableData('stock_data', {
        limit: 10,
        offset: 0,
        environment: 'test'
      });

      expect(apiClient.get).toHaveBeenCalledWith('/api/data/tables/stock_data', {
        params: {
          environment: 'test',
          limit: 10,
          offset: 0
        }
      });
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用获取表元数据API', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            table_name: 'stock_data',
            record_count: 1000,
            columns: [
              { column_name: 'id', column_type: 'INTEGER' },
              { column_name: 'symbol', column_type: 'VARCHAR' }
            ]
          }
        }
      };

      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const result = await dataCenterAPI.getTableMeta('stock_data', 'test');

      expect(apiClient.get).toHaveBeenCalledWith('/api/data/tables/stock_data/meta', {
        params: { environment: 'test' }
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('CSV导入API', () => {
    it('应该正确调用基础CSV导入API', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: '导入成功',
          table_name: 'new_table',
          record_count: 100
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const formData = new FormData();
      formData.append('file', new File(['test'], 'test.csv'));

      const result = await dataCenterAPI.importCSV(formData, {
        environment: 'test',
        table_name: 'custom_table'
      });

      expect(apiClient.post).toHaveBeenCalledWith('/api/data/import/csv', formData, {
        params: {
          environment: 'test',
          table_name: 'custom_table'
        },
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用增强CSV导入API', async () => {
      const mockResponse = {
        data: {
          success: true,
          sessionId: 'session_123',
          message: '增强导入已启动'
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const formData = new FormData();
      formData.append('files', new File(['test1'], 'test1.csv'));
      formData.append('files', new File(['test2'], 'test2.csv'));

      const result = await dataCenterAPI.importCSVEnhanced(formData, {
        environment: 'test',
        mode: 'fromC2C',
        enableValidation: true,
        deduplicateStrategy: 'latest',
        batchConcurrency: 3,
        batchSize: 10000,
        retryCount: 2
      });

      expect(apiClient.post).toHaveBeenCalledWith('/api/data/import/csv-enhanced', formData, {
        params: {
          environment: 'test',
          mode: 'fromC2C',
          enableValidation: true,
          deduplicateStrategy: 'latest',
          batchConcurrency: 3,
          batchSize: 10000,
          retryCount: 2
        },
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('MySQL导入API', () => {
    it('应该正确调用MySQL连接测试API', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: '连接成功',
          serverInfo: {
            version: '8.0.25',
            charset: 'utf8mb4'
          }
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const connectionConfig = {
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_pass',
        connectTimeout: 10000,
        queryTimeout: 30000,
        charset: 'utf8mb4',
        useSSL: false
      };

      const result = await dataCenterAPI.testMySQLConnection(connectionConfig);

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/data/mysql/test-connection',
        connectionConfig
      );
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用获取MySQL表列表API', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: [
            {
              name: 'stock_data',
              record_count: 10000,
              last_updated: '2025-07-22T08:00:00Z',
              comment: '股票数据表'
            },
            {
              name: 'futures_data',
              record_count: 5000,
              last_updated: '2025-07-22T07:00:00Z',
              comment: '期货数据表'
            }
          ]
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const connectionConfig = {
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_pass'
      };

      const result = await dataCenterAPI.getMySQLTables(connectionConfig);

      expect(apiClient.post).toHaveBeenCalledWith('/api/data/mysql/tables', connectionConfig);
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用MySQL表数据预览API', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            tableName: 'stock_data',
            columns: ['id', 'symbol', 'name', 'price'],
            preview: [
              { id: 1, symbol: 'AAPL', name: '苹果', price: 150.25 },
              { id: 2, symbol: 'GOOGL', name: '谷歌', price: 2800.5 }
            ],
            totalRecords: 10000
          }
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const previewConfig = {
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_pass',
        tableName: 'stock_data',
        limit: 10
      };

      const result = await dataCenterAPI.previewMySQLTable(previewConfig);

      expect(apiClient.post).toHaveBeenCalledWith('/api/data/mysql/preview', previewConfig);
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用MySQL数据导入API', async () => {
      const mockResponse = {
        data: {
          success: true,
          sessionId: 'mysql_session_456',
          message: 'MySQL导入已启动',
          estimatedDuration: '5分钟'
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const importConfig = {
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_pass',
        environment: 'test',
        tables: ['stock_data', 'futures_data'],
        tableMappings: {
          stock_data: 'aqua_stock_data',
          futures_data: 'aqua_futures_data'
        },
        conflictStrategies: {
          stock_data: 'merge',
          futures_data: 'replace'
        }
      };

      const result = await dataCenterAPI.importMySQLData(importConfig);

      expect(apiClient.post).toHaveBeenCalledWith('/api/data/mysql/import', importConfig);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('导入历史和统计API', () => {
    it('应该正确调用获取导入历史API', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: [
            {
              id: 'import_1',
              type: 'csv',
              environment: 'test',
              fileName: 'test_data.csv',
              recordCount: 1000,
              success: true,
              timestamp: '2025-07-22T10:00:00Z',
              duration: 30000
            }
          ],
          pagination: {
            total: 50,
            limit: 10,
            offset: 0,
            has_next: true
          }
        }
      };

      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const result = await dataCenterAPI.getImportHistory({
        environment: 'test',
        importType: 'csv',
        startDate: '2025-07-01',
        endDate: '2025-07-22',
        limit: 10,
        offset: 0
      });

      expect(apiClient.get).toHaveBeenCalledWith('/api/data/import/history', {
        params: {
          environment: 'test',
          importType: 'csv',
          startDate: '2025-07-01',
          endDate: '2025-07-22',
          limit: 10,
          offset: 0
        }
      });
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用获取导入统计API', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            totalImports: 100,
            successRate: 0.85,
            totalRecords: 1000000,
            averageDuration: 25000,
            timeline: {
              dates: ['2025-07-20', '2025-07-21', '2025-07-22'],
              imports: [5, 8, 12],
              successes: [4, 7, 10]
            },
            errorAnalysis: [
              { error: '连接超时', count: 5, percentage: 0.33 },
              { error: '格式错误', count: 3, percentage: 0.2 }
            ]
          }
        }
      };

      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const result = await dataCenterAPI.getImportStats({
        environment: 'test',
        period: 'week',
        startDate: '2025-07-15',
        endDate: '2025-07-22'
      });

      expect(apiClient.get).toHaveBeenCalledWith('/api/data/import/stats', {
        params: {
          environment: 'test',
          period: 'week',
          startDate: '2025-07-15',
          endDate: '2025-07-22'
        }
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('导入会话控制API', () => {
    it('应该正确调用取消导入API', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: '导入已取消',
          sessionId: 'session_123'
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const result = await dataCenterAPI.cancelImport('session_123');

      expect(apiClient.post).toHaveBeenCalledWith('/api/data/import/cancel', {
        sessionId: 'session_123'
      });
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用暂停导入API', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: '导入已暂停',
          sessionId: 'session_123'
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const result = await dataCenterAPI.pauseImport('session_123');

      expect(apiClient.post).toHaveBeenCalledWith('/api/data/import/pause', {
        sessionId: 'session_123'
      });
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用恢复导入API', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: '导入已恢复',
          sessionId: 'session_123'
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const result = await dataCenterAPI.resumeImport('session_123');

      expect(apiClient.post).toHaveBeenCalledWith('/api/data/import/resume', {
        sessionId: 'session_123'
      });
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用解决冲突API', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: '冲突已解决',
          sessionId: 'session_123'
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const conflictResolution = {
        sessionId: 'session_123',
        conflictIndex: 0,
        resolution: 'replace'
      };

      const result = await dataCenterAPI.resolveConflict(conflictResolution);

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/data/import/resolve-conflict',
        conflictResolution
      );
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用获取导入会话状态API', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            sessionId: 'session_123',
            status: 'running',
            progress: 65,
            currentTable: 'futures_data',
            totalTables: 3,
            completedTables: 1,
            recordsProcessed: 6500,
            totalRecords: 10000,
            startTime: '2025-07-22T10:00:00Z',
            estimatedEndTime: '2025-07-22T10:05:00Z'
          }
        }
      };

      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const result = await dataCenterAPI.getImportSessionStatus('session_123');

      expect(apiClient.get).toHaveBeenCalledWith('/api/data/import/session/session_123/status');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('高性能数据API', () => {
    it('应该正确调用高性能股票数据API', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: [
            { symbol: 'AAPL', close: 150.25, trade_date: '2025-07-22' },
            { symbol: 'GOOGL', close: 2800.5, trade_date: '2025-07-22' }
          ],
          pagination: {
            total: 1000,
            limit: 50,
            offset: 0,
            has_next: true
          },
          performance: {
            query_time: 45,
            cache_hit: true
          }
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const params = {
        symbols: ['AAPL', 'GOOGL'],
        start_date: '2025-07-01',
        end_date: '2025-07-22',
        limit: 50,
        offset: 0,
        order_by: 'trade_date',
        order_desc: true
      };

      const result = await dataCenterAPI.getStockData(params, 'test');

      expect(apiClient.post).toHaveBeenCalledWith('/api/performance/stock/data', params, {
        params: { environment: 'test' }
      });
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用高性能期货数据API', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: [
            { contract_code: 'RB2501.XSHE', close: 3500.0, trade_datetime: '2025-07-22T15:00:00Z' },
            { contract_code: 'HC2501.XSHE', close: 3200.0, trade_datetime: '2025-07-22T15:00:00Z' }
          ],
          pagination: {
            total: 500,
            limit: 50,
            offset: 0,
            has_next: true
          }
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const params = {
        contract_codes: ['RB2501.XSHE', 'HC2501.XSHE'],
        start_datetime: '2025-07-22T09:00:00Z',
        end_datetime: '2025-07-22T15:00:00Z',
        limit: 50,
        offset: 0,
        order_by: 'trade_datetime',
        order_desc: true
      };

      const result = await dataCenterAPI.getFuturesData(params, 'test');

      expect(apiClient.post).toHaveBeenCalledWith('/api/performance/futures/data', params, {
        params: { environment: 'test' }
      });
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用获取缓存统计API', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            totalCacheSize: 1048576,
            cacheHitRate: 0.85,
            totalRequests: 10000,
            cacheHits: 8500,
            cacheMisses: 1500,
            averageResponseTime: 25
          }
        }
      };

      vi.mocked(apiClient.get).mockResolvedValue(mockResponse);

      const result = await dataCenterAPI.getCacheStats();

      expect(apiClient.get).toHaveBeenCalledWith('/api/performance/cache/stats');
      expect(result).toEqual(mockResponse);
    });

    it('应该正确调用清理缓存API', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: '缓存已清理',
          clearedSize: 524288
        }
      };

      vi.mocked(apiClient.post).mockResolvedValue(mockResponse);

      const result = await dataCenterAPI.clearCache();

      expect(apiClient.post).toHaveBeenCalledWith('/api/performance/cache/clear');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('错误处理', () => {
    it('应该正确处理网络错误', async () => {
      const networkError = new Error('Network Error');
      vi.mocked(apiClient.get).mockRejectedValue(networkError);

      try {
        await dataCenterAPI.getTables('test');
      } catch (error) {
        expect(error).toBe(networkError);
      }

      expect(apiClient.get).toHaveBeenCalledWith('/api/data/tables', {
        params: { environment: 'test' }
      });
    });

    it('应该正确处理API响应错误', async () => {
      const errorResponse = {
        response: {
          status: 500,
          data: {
            success: false,
            message: '服务器内部错误'
          }
        }
      };
      vi.mocked(apiClient.post).mockRejectedValue(errorResponse);

      try {
        const formData = new FormData();
        await dataCenterAPI.importCSV(formData);
      } catch (error) {
        expect(error).toBe(errorResponse);
      }
    });

    it('应该正确处理超时错误', async () => {
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'ECONNABORTED';
      vi.mocked(apiClient.post).mockRejectedValue(timeoutError);

      try {
        await dataCenterAPI.importMySQLData({
          host: 'localhost',
          port: 3306,
          database: 'test',
          username: 'user',
          password: 'pass',
          environment: 'test',
          tables: ['test_table']
        });
      } catch (error) {
        expect(error.name).toBe('ECONNABORTED');
      }
    });
  });

  describe('参数验证', () => {
    it('应该使用默认环境参数', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: { success: true, data: [] } });

      await dataCenterAPI.getTables();

      expect(apiClient.get).toHaveBeenCalledWith('/api/data/tables', {
        params: { environment: 'test' }
      });
    });

    it('应该正确合并参数', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: { success: true, data: [] } });

      await dataCenterAPI.getTableData('stock_data', {
        limit: 20,
        offset: 40,
        columns: 'symbol,name,price'
      });

      expect(apiClient.get).toHaveBeenCalledWith('/api/data/tables/stock_data', {
        params: {
          environment: 'test',
          limit: 20,
          offset: 40,
          columns: 'symbol,name,price'
        }
      });
    });

    it('应该正确设置请求头', async () => {
      vi.mocked(apiClient.post).mockResolvedValue({ data: { success: true } });

      const formData = new FormData();
      await dataCenterAPI.importCSV(formData);

      expect(apiClient.post).toHaveBeenCalledWith('/api/data/import/csv', formData, {
        params: {
          environment: 'test'
        },
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    });
  });
});
