import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import Menu from '../../src/components/layout/Menu.vue';
import menuMock from '../mock/menu.mock.json';

// 菜单mock数据（多级）
const menuData = menuMock;

describe('Menu.vue', () => {
  it('组件应能被正确导入', () => {
    expect(Menu).toBeTruthy();
  });

  it('应能正常渲染多级菜单', () => {
    const wrapper = mount(Menu, { props: { menu: menuData } });
    // 一级菜单数量
    expect(wrapper.findAll('.menu-item-root').length).toBeGreaterThan(0);
    // 多级菜单递归渲染
    expect(wrapper.html()).toContain('数据仓库');
    expect(wrapper.html()).toContain('数据导入');
    expect(wrapper.html()).toContain('中国A股');
  });

  it('应渲染菜单项名称、icon、route', () => {
    const wrapper = mount(Menu, { props: { menu: menuData } });
    expect(wrapper.text()).toMatch(/数据仓库/);
    expect(wrapper.text()).toMatch(/策略抽屉/);
    // icon渲染（可用快照或class）
    expect(wrapper.html()).toMatch(/icon/);
    // route属性存在但不渲染为链接（后续联动）
  });

  it('空children或无children应正常渲染', () => {
    const testMenu = [
      { id: 'x', name: '无子菜单', icon: 'test' },
      { id: 'y', name: '空子菜单', children: [] }
    ];
    const wrapper = mount(Menu, { props: { menu: testMenu } });
    expect(wrapper.text()).toMatch(/无子菜单/);
    expect(wrapper.text()).toMatch(/空子菜单/);
  });

  it('权限字段预留不影响渲染', () => {
    const testMenu = [{ id: 'z', name: '权限菜单', permission: 'admin' }];
    const wrapper = mount(Menu, { props: { menu: testMenu } });
    expect(wrapper.text()).toMatch(/权限菜单/);
  });
});
