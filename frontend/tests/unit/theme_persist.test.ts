import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia } from 'pinia';
import App from '@/App.vue';

// 模拟localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    clear: () => {
      store = {};
    },
    removeItem: (key: string) => {
      delete store[key];
    }
  };
})();

Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// mock vue-router useRoute，返回带matched的对象
vi.mock('vue-router', async () => {
  const actual = await vi.importActual<any>('vue-router');
  return {
    ...actual,
    useRoute: () => ({ matched: [{ path: '/', meta: {} }] })
  };
});

describe('主题切换与持久化', () => {
  beforeEach(() => {
    window.localStorage.clear();
  });

  function mountWithPinia() {
    return mount(App, {
      global: {
        plugins: [createPinia()]
      }
    });
  }

  it('点击主题切换按钮可切换明暗模式', async () => {
    const wrapper = mountWithPinia();
    const btn = wrapper.find('button[aria-label="切换主题"]');
    const oldTheme = wrapper.vm.theme;
    await btn.trigger('click');
    // theme应发生变化
    expect(wrapper.vm.theme).not.toBe(oldTheme);
  });

  it('切换主题后状态应持久化到localStorage', async () => {
    const wrapper = mountWithPinia();
    const btn = wrapper.find('button[aria-label="切换主题"]');
    await btn.trigger('click');
    // localStorage应有theme字段
    expect(window.localStorage.getItem('theme')).toMatch(/dark|light/);
  });

  it('刷新页面后应自动恢复上次主题', async () => {
    window.localStorage.setItem('theme', 'dark');
    const wrapper = mountWithPinia();
    expect(wrapper.vm.theme).toBe('dark');
  });

  it('组件文件头和关键节点应有中文注释（人工检查）', () => {
    expect(true).toBe(true);
  });
});
