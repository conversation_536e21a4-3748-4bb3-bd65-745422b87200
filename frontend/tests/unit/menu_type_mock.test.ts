import { describe, it, expect } from 'vitest';
import menuMock from '../mock/menu.mock.json';
import type { MenuItem } from '../../src/types/menu';

function validateMenuTree(tree: any[]): boolean {
  return tree.every((item) => {
    // 必选字段
    if (typeof item.id !== 'string' || typeof item.name !== 'string') return false;
    // 可选字段
    if (item.icon && typeof item.icon !== 'string') return false;
    if (item.route && typeof item.route !== 'string') return false;
    if (item.permission && typeof item.permission !== 'string') return false;
    if (item.description && typeof item.description !== 'string') return false;
    // children递归
    if (item.children) {
      if (!Array.isArray(item.children)) return false;
      if (!validateMenuTree(item.children)) return false;
    }
    return true;
  });
}

describe('菜单mock与类型一致性', () => {
  it('mock数据结构应与MenuItem类型完全一致', () => {
    expect(validateMenuTree(menuMock)).toBe(true);
  });
});
