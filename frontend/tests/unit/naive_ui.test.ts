import { mount } from '@vue/test-utils';
import { NButton, NInput, NMessageProvider } from 'naive-ui';
import { describe, it, expect } from 'vitest';

// 基础渲染测试：NButton
// 业务场景：全局注册后，任何页面/组件均可直接使用Naive UI组件

describe('Naive UI 组件全局引入与基础渲染', () => {
  it('NButton 可以正常渲染', () => {
    const wrapper = mount(NButton, { slots: { default: '测试按钮' } });
    expect(wrapper.text()).toBe('测试按钮');
  });

  it('NInput 可以正常渲染', () => {
    const wrapper = mount(NInput, { props: { value: 'AQUA' } });
    // NInput渲染后应包含input元素，且值为AQUA
    const input = wrapper.find('input');
    expect(input.exists()).toBe(true);
    expect(input.element.value).toBe('AQUA');
  });

  it('NMessageProvider 可用，支持消息弹窗', async () => {
    const wrapper = mount(NMessageProvider, {
      slots: {
        default: '<div>消息测试</div>'
      }
    });
    expect(wrapper.text()).toContain('消息测试');
  });
});

// 说明：
// 1. 本测试覆盖Naive UI全局引入后的基础组件渲染能力，适配MVP主流程。
// 2. 推荐后续在业务组件中按需补充NForm、NDataTable等常用组件的集成测试。
// 3. 如需测试主题切换、动态表单、消息等高级用法，可参考Naive UI官方最佳实践文档。
