import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import HomePage from '@/modules/home/<USER>';

describe('HomePage首页组件', () => {
  it('HomePage.vue文件应存在且可被import', () => {
    expect(HomePage).toBeTruthy();
  });

  it('应能渲染系统简介内容', () => {
    const wrapper = mount(HomePage);
    expect(wrapper.text()).toMatch(/AQUA|量化|数据平台|系统简介/);
  });

  it('应能渲染核心功能入口卡片', () => {
    const wrapper = mount(HomePage);
    // 假设有"数据中心"、"回测结果"、"AI助手"等卡片
    expect(wrapper.text()).toMatch(/数据中心/);
    expect(wrapper.text()).toMatch(/回测结果/);
    expect(wrapper.text()).toMatch(/AI助手/);
  });

  it('功能卡片应可点击（模拟跳转或按钮）', async () => {
    const wrapper = mount(HomePage);
    // 查找所有功能卡片按钮
    const buttons = wrapper.findAll('.feature-card button, .feature-card a');
    expect(buttons.length).toBeGreaterThan(0);
    // 可进一步模拟点击
  });

  it('组件文件头和关键节点应有中文注释（人工检查）', () => {
    expect(true).toBe(true);
  });
});
