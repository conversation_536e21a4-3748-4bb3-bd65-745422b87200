import { describe, it, expect } from 'vitest';
import { createRouter, createWebHistory } from 'vue-router';
import routes from '@/router/index';

// mock组件
const Dummy = { template: '<div>Dummy</div>' };

describe('路由配置分离与多级嵌套', () => {
  it('router/index.ts文件应存在且结构清晰', () => {
    expect(routes).toBeDefined();
    expect(Array.isArray(routes)).toBe(true);
  });

  it('应包含首页（/）和404路由', () => {
    const names = routes.map((r) => r.path);
    expect(names).toContain('/');
    expect(names).toContain('/:pathMatch(.*)*');
  });

  it('首页路由跳转应渲染HomePage组件', async () => {
    const router = createRouter({ history: createWebHistory(), routes });
    router.addRoute({ path: '/', component: Dummy });
    await router.push('/');
    expect(router.currentRoute.value.path).toBe('/');
  });

  it('404路由跳转应渲染NotFound组件', async () => {
    const router = createRouter({ history: createWebHistory(), routes });
    router.addRoute({ path: '/:pathMatch(.*)*', component: Dummy });
    await router.push('/not-exist');
    expect(router.currentRoute.value.matched[0].path).toMatch(/:pathMatch/);
  });

  it('应支持多级嵌套路由', () => {
    // 检查是否有children字段
    const hasNested = routes.some((r) => Array.isArray(r.children) && r.children.length > 0);
    expect(hasNested).toBe(true);
  });

  it('路由配置文件应有中文注释（人工检查）', () => {
    expect(true).toBe(true);
  });
});
