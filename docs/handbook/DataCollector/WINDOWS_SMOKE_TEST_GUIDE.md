# AQUA Windows Smoke Test Guide v2.0

> **Target**: Quick validation of AQUA data collection CLI on Windows  
> **Duration**: 2 minutes (Layer 1 Quick Validation)  
> **Use Cases**: Dev environment validation, CI/CD pipeline, quick troubleshooting  
> **Update**: Support for layered testing architecture, optimized performance and business logic validation

---

## Test Overview

### Layered Testing Architecture
- **Layer 1**: Quick Validation (2 minutes) - This document scope
- **Layer 2**: Standard Function Testing (10 minutes)
- **Layer 3**: Production Load Testing (45 minutes)

### Layer 1 Test Scope
- Basic environment check
- Core module import validation
- Data source connection status check
- Small data volume validation (10-50 records per data source)

### Prerequisites
- Windows 10/11 Operating System
- Python 3.11+ installed
- Project dependencies installed (`pip install -r requirements.txt`)
- Complete configuration files (`config/settings.toml`, `config/.env`)

---

## Quick Execution Steps

### Step 1: Environment Validation (30 seconds)
```bash
# One-click environment check
python -c "
import sys, platform, os
print(f'✅ Python: {sys.version.split()[0]}')
print(f'✅ Platform: {platform.system()} {platform.release()}')
print(f'✅ Virtual Env: {\"Yes\" if sys.prefix != sys.base_prefix else \"No\"}')
print(f'✅ Working Dir: {os.getcwd()}')
"

# Quick structure check
python -c "
from pathlib import Path
dirs = ['src', 'config', 'logs', 'tests']
for d in dirs:
    status = '✅' if Path(d).exists() else '❌'
    print(f'{status} {d}/')
"
```

### Step 2: Core Module Validation (30 seconds)
```bash
# Core module import test
python -c "
try:
    from src.cli.main import app
    from src.utils.config_loader_v2 import ConfigLoaderV2
    import duckdb, pandas, tushare, pymysql
    print('✅ All core modules imported successfully')
except ImportError as e:
    print(f'❌ Module import failed: {e}')
"
```

### Step 3: Configuration Validation (30 seconds)
```bash
# Configuration loading test
python -c "
import sys; sys.path.insert(0, 'src')
from utils.config_loader_v2 import ConfigLoaderV2
import os
try:
    config = ConfigLoaderV2()
    dev_config = config.get_config('dev')
    token = os.getenv('TUSHARE_TOKEN') or 'Not Set'
    print(f'✅ Configuration loaded successfully')
    print(f'✅ TUSHARE Token: {token[:20]}...' if len(token) > 20 else f'⚠️ TUSHARE Token: {token}')
    print(f'✅ Data source config: {list(dev_config.get(\"datasources\", {}).keys())}')
except Exception as e:
    print(f'❌ Configuration loading failed: {e}')
"
```

### Step 4: Data Source Connection Validation (30 seconds)
```bash
# Quick connection test (parallel execution)
python -c "
import sys, subprocess, concurrent.futures
sys.path.insert(0, 'src')

def test_source(source):
    try:
        result = subprocess.run(
            ['python', '-m', 'src.cli.main', 'collect', '--check-capabilities', '--source', source],
            capture_output=True, timeout=10, text=True
        )
        return f'✅ {source.upper()}: Available' if result.returncode == 0 else f'❌ {source.upper()}: Error'
    except:
        return f'⚠️ {source.upper()}: Timeout'

sources = ['tushare', 'csv', 'mysql']
with concurrent.futures.ThreadPoolExecutor() as executor:
    results = list(executor.map(test_source, sources))
    for result in results:
        print(result)
"
```

---

## Acceptance Criteria

### Must Pass (Blocking)
- [ ] **Python 3.11+** version check passed
- [ ] **Virtual environment** activation confirmed
- [ ] **Project structure** complete (src/, config/, logs/, tests/)
- [ ] **Core modules** imported successfully (CLI, config loader, core dependencies)
- [ ] **Configuration loading** working properly
- [ ] **At least 2 data sources** connection status Available

### Recommended Pass (Non-blocking)
- [ ] **TUSHARE Token** configured
- [ ] **MySQL connection** successful
- [ ] **All data sources** status Available

---

## Quick Troubleshooting

### One-click Diagnostic Script
```bash
# Run complete diagnosis
python -c "
import sys, os, subprocess
from pathlib import Path

print('🔍 AQUA Environment Diagnostic Report')
print('='*50)

# 1. Python environment
print(f'Python Version: {sys.version.split()[0]}')
print(f'Virtual Env: {\"✅\" if sys.prefix != sys.base_prefix else \"❌\"}')

# 2. Project structure
missing_dirs = [d for d in ['src', 'config', 'logs', 'tests'] if not Path(d).exists()]
print(f'Project Structure: {\"✅\" if not missing_dirs else \"❌ Missing: \" + str(missing_dirs)}')

# 3. Configuration files
config_files = ['config/settings.toml', 'config/.env']
missing_configs = [f for f in config_files if not Path(f).exists()]
print(f'Config Files: {\"✅\" if not missing_configs else \"❌ Missing: \" + str(missing_configs)}')

# 4. Core dependencies
try:
    import duckdb, pandas, tushare, pymysql
    print('Core Dependencies: ✅')
except ImportError as e:
    print(f'Core Dependencies: ❌ {e}')

# 5. TUSHARE Token
token = os.getenv('TUSHARE_TOKEN')
print(f'TUSHARE Token: {\"✅\" if token else \"❌ Not Set\"}')

print('='*50)
"
```

### Common Issues Quick Fix
```bash
# Issue 1: Missing dependencies -> Reinstall
pip install -r requirements.txt

# Issue 2: Environment variables -> Reload
set TUSHARE_TOKEN=your_token_here

# Issue 3: Virtual environment -> Reactivate
.venv\Scripts\activate

# Issue 4: Configuration issues -> Check syntax
python -c "import toml; toml.load('config/settings.toml'); print('Configuration file syntax correct')"
```

---

## Automated Test Report

### One-click Test Report Generation
```bash
python -c "
import sys, os, platform, datetime
from pathlib import Path
sys.path.insert(0, 'src')

print('# AQUA Smoke Test Report')
print(f'**Execution Time**: {datetime.datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")}')
print(f'**Environment**: {platform.system()} {platform.release()}')
print(f'**Python Version**: {sys.version.split()[0]}')
print()

# Environment check
venv_status = '✅' if sys.prefix != sys.base_prefix else '❌'
print(f'- Virtual Environment: {venv_status}')

# Project structure
dirs = ['src', 'config', 'logs', 'tests']
for d in dirs:
    status = '✅' if Path(d).exists() else '❌'
    print(f'- {d}/: {status}')

# Configuration check
try:
    from utils.config_loader_v2 import ConfigLoaderV2
    ConfigLoaderV2().get_config('dev')
    print('- Configuration Loading: ✅')
except:
    print('- Configuration Loading: ❌')

# Token check
token = os.getenv('TUSHARE_TOKEN')
print(f'- TUSHARE Token: {\"✅\" if token else \"❌\"}')

print()
print('**Conclusion**: Smoke Test', '✅ PASSED' if venv_status == '✅' else '❌ FAILED')
"
```

---

## Next Steps

### ✅ Smoke Test Passed
```bash
# Execute standard function test (Layer 2)
python -m tests.run_standard_tests

# Or execute full E2E test
python -m tests.run_full_e2e_tests
```

### ❌ Smoke Test Failed
```bash
# Run diagnostic script
python -c "exec(open('scripts/diagnose_environment.py').read())"

# View detailed logs
type logs\smoke_test_error.log
```

---

## Related Documentation

- **[E2E Test Tasks](WINDOWS_E2E_TEST_TASKS.md)** - Detailed test task definitions
- **[E2E Checklist](WINDOWS_E2E_CHECKLIST.md)** - Layered test execution checklist
- **[Configuration Guide](../Configuration/CONFIG_GUIDE.md)** - Detailed configuration file instructions
- **[Troubleshooting](../Troubleshooting/COMMON_ISSUES.md)** - Common issue solutions

---

## Version History

- **v2.0** (2025-08-02): Support for layered testing architecture, optimized execution efficiency
- **v1.0** (2025-08-01): Initial version, basic smoke test functionality
