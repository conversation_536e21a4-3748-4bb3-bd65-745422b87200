# AQUA数据采集CLI工具

> 个人量化开发者的专业数据采集工具，采用"Multi-Source Real Data Only"设计理念，支持三种真实数据源

## ✨ 核心特性

- 🔗 **Multi-Source Real Data Only** - 100%使用真实数据源，支持TUSHARE(Web API)、CSV(本地文件)、MySQL(数据库)
- 🖥️ **跨平台支持** - macOS完全验证 ✅ / Windows架构就绪 🔄  
- ⚡ **个人环境优化** - 启动<2秒，内存增长<20MB
- 🎯 **CLI友好交互** - 专为个人开发者设计的命令行体验
- 🛡️ **数据质量保证** - 任何环境下都确保数据来源真实可靠
- ✅ **专业标准** - 符合量化交易对数据质量的严格要求

## 🚀 5分钟快速开始

### 第一步：环境准备
```bash
# 确保Python 3.11+
python --version

# 克隆项目
git clone https://github.com/your-org/AQUA.git
cd AQUA

# 安装依赖
pip install -r requirements.txt
```

### 第二步：配置数据源
```bash
# TUSHARE数据源配置（Web API）
export TUSHARE_TOKEN="your_token_here"

# CSV数据源配置（本地文件，可选）  
export CSV_DATA_PATH="data/csv/"

# MySQL数据源配置（数据库，可选）
export MYSQL_HOST="localhost"
export MYSQL_USER="your_username" 
export MYSQL_PASSWORD="your_password"
export MYSQL_DATABASE="your_database"

# 验证所有数据源连接
python -m src.cli.main collect --check-capabilities --source tushare
python -m src.cli.main collect --check-capabilities --source csv
python -m src.cli.main collect --check-capabilities --source mysql

# ⚠️ 重要：系统支持三种真实数据源，根据需要配置相应的环境变量
```

### 第三步：多数据源采集示例
```bash
# TUSHARE数据源采集（Web API）
python -m src.cli.main collect 000001.SZ --source tushare --preview

# CSV数据源采集（本地文件）
python -m src.cli.main collect 000001.SZ --source csv --preview

# MySQL数据源采集（数据库）
python -m src.cli.main collect 000001.SZ --source mysql --preview

# 成功输出示例：
# ✅ 数据预览成功！
# 📈 预计采集 250 行股票数据  
# 💾 数据源: TUSHARE/CSV/MYSQL (100%真实数据)
# 🔒 数据源类型: real_webapi/real_localfile/real_database
# ⏱️  预计用时: <1秒
```

### 第四步：实际数据采集
```bash
# 去掉--preview参数，选择数据源进行实际采集
python -m src.cli.main collect 000001.SZ --source tushare
python -m src.cli.main collect futures_data --source csv
python -m src.cli.main collect stock_table --source mysql

# 成功后会看到：
# ✅ 数据采集成功！
# 📈 采集到 XXX 行数据
# 💾 数据已保存到: data/aqua_real_data.duckdb
# 🔒 数据源: TUSHARE/CSV/MYSQL (真实数据)
# ⏱️  用时: <2秒
```

## 🎯 适用人群

### ✅ 适合您，如果您是：
- 个人量化交易研究者
- Python数据分析爱好者  
- 需要股票/期货数据的个人开发者
- 寻找简单可靠数据采集工具的用户

### ❌ 可能不适合：
- 需要企业级高并发的机构用户
- 需要毫秒级实时数据的高频交易
- 不熟悉命令行操作的用户

## 📚 完整文档

| 文档 | 用途 | 适合人群 |
|------|------|----------|
| [**使用指南**](USER_GUIDE.md) | 详细操作说明，从安装到高级功能 | 所有用户必读 |
| [**常见问题**](FAQ.md) | 问题排查和解决方案 | 遇到问题时查看 |

## 🆘 获取帮助

- **使用问题**: 查看 [FAQ.md](FAQ.md)
- **功能建议**: 提交 GitHub Issue
- **技术交流**: 查看项目主页联系方式

## 🏷️ 版本信息

- **当前版本**: v1.0.0 (EPIC2-V3完整版)
- **发布日期**: 2025-07-30
- **支持平台**: macOS 10.15+ / Windows 10+
- **Python要求**: 3.11+

---

**开始使用** → [使用指南](USER_GUIDE.md) | **遇到问题** → [常见问题](FAQ.md)