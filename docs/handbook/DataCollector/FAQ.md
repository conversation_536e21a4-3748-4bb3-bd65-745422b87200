# AQUA数据采集常见问题

## 📋 问题分类
- [多数据源问题](#多数据源问题)
- [安装配置问题](#安装配置问题)
- [数据采集问题](#数据采集问题)  
- [数据源问题](#数据源问题)
- [性能问题](#性能问题)
- [数据查看问题](#数据查看问题)
- [高级功能问题](#高级功能问题)

---

## 多数据源问题

### ❓ 如何选择合适的数据源？

**回答**: AQUA支持三种真实数据源，根据需求选择：

| 数据源 | 适用场景 | 配置要求 | 数据类型 |
|--------|----------|----------|----------|
| **TUSHARE** | 实时股票、期货数据 | TUSHARE_TOKEN | real_webapi |
| **CSV** | 历史数据导入、本地数据 | CSV文件路径 | real_localfile |
| **MySQL** | 现有数据库迁移 | 数据库连接参数 | real_database |

```bash
# 检查各数据源能力
python -m src.cli.main collect --check-capabilities --source tushare
python -m src.cli.main collect --check-capabilities --source csv
python -m src.cli.main collect --check-capabilities --source mysql
```

### ❓ CSV数据源支持什么格式？

**回答**: CSV数据源主要支持：
- **FROMC2C格式**: 期货主力合约数据
- **标准股票格式**: 包含OHLCV字段
- **编码支持**: UTF-8, UTF-8-BOM, GBK

```bash
# 设置CSV数据路径
export CSV_DATA_PATH="/path/to/your/csv/files/"

# 预览CSV数据
python -m src.cli.main collect your_data --source csv --preview
```

### ❓ MySQL数据源如何配置？

**回答**: 需要配置完整的数据库连接参数：

```bash
# 必需配置
export MYSQL_HOST="localhost"        # 数据库地址
export MYSQL_USER="your_username"    # 用户名
export MYSQL_PASSWORD="your_password"# 密码
export MYSQL_DATABASE="your_database" # 数据库名

# 可选配置
export MYSQL_PORT="3306"             # 端口（默认3306）

# 验证连接
python -m src.cli.main collect --check-capabilities --source mysql
```

### ❓ 多数据源的数据会混合吗？

**回答**: 不会。系统严格隔离不同数据源：
- 每个数据源有独立的标识（real_webapi/real_localfile/real_database）
- 数据保存时包含数据源信息
- 可以通过数据源类型进行筛选和查询

---

## 安装配置问题

### ❓ pip install失败怎么办？

**症状**: 运行 `pip install -r requirements.txt` 时出现错误

**解决方案**:
```bash
# 1. 确保Python版本正确
python --version  # 应该是3.11+

# 2. 升级pip
pip install --upgrade pip

# 3. 重新安装，显示详细信息
pip install -r requirements.txt --verbose

# 4. 如果还是失败，尝试用户安装
pip install --user -r requirements.txt
```

### ❓ macOS上出现权限错误

**症状**: Permission denied 或权限相关错误

**解决方案**:
```bash
# 使用用户级安装
pip install --user -r requirements.txt

# 或者使用虚拟环境（推荐）
python -m venv aqua_env
source aqua_env/bin/activate  # macOS/Linux
pip install -r requirements.txt
```

### ❓ Windows上找不到Python

**症状**: 'python' 不是内部或外部命令

**解决方案**:
```bash
# 尝试使用py命令
py --version
py -m pip install -r requirements.txt

# 或者使用完整路径
python3 --version
python3 -m pip install -r requirements.txt
```

---

## 数据采集问题

### ❓ 没有TUSHARE Token能使用吗？

**答案**: 不能。AQUA数据采集工具采用"Real Data Only"设计理念，仅使用真实数据源。

**说明**: 
- 系统100%使用真实的TUSHARE数据源
- 无Token时系统会报错并提供详细配置指导
- 确保所有数据都是真实市场数据，适合量化策略开发和实际投资决策
- 任何环境（开发/测试/生产）都不使用模拟数据

**获取Token**:
1. 访问 [TUSHARE官网](https://tushare.pro/) 注册账户
2. 完成身份验证（通常需要手机号验证）
3. 在个人中心获取免费Token
4. 免费Token每天有2000积分，足够个人开发使用

### ❓ 采集时显示"Connection failed"

**症状**: 网络连接失败相关错误

**解决方案**: 
这表示TUSHARE数据源连接失败，需要排查问题：

```bash
# 1. 检查网络连接
ping tushare.pro

# 2. 验证Token设置
echo $TUSHARE_TOKEN  # macOS/Linux
echo %TUSHARE_TOKEN%  # Windows

# 3. 测试数据源连接
python -m src.cli.main --check-capabilities
```

**常见原因**:
- 网络连接问题
- TUSHARE Token未设置或错误
- TUSHARE服务暂时不可用
- 账户积分不足

### ❓ 股票代码格式错误

**症状**: "Symbol not found" 或代码格式错误

**正确格式**:
```bash
# 深交所股票：6位数字 + .SZ
python -m src.cli.main collect 000001.SZ  # 平安银行
python -m src.cli.main collect 000002.SZ  # 万科A

# 上交所股票：6位数字 + .SH  
python -m src.cli.main collect 600036.SH  # 招商银行
python -m src.cli.main collect 600000.SH  # 浦发银行

# 期货：合约代码 + .CFX
python -m src.cli.main collect IF00.CFX --type futures
```

### ❓ 采集速度很慢

**症状**: 数据采集用时过长

**解决方案**:
```bash
# 1. 减少批量大小
python -m src.cli.main collect 000001.SZ,600036.SH --batch-size 10

# 2. 减少并发数（网络不好时）
python -m src.cli.main collect 000001.SZ --concurrent 1

# 3. 先用预览模式测试
python -m src.cli.main collect 000001.SZ --preview
```

---

## 数据源问题

### ❓ 如何知道当前使用的是什么数据源？

**检查方法**:
```bash
# 查看数据源状态
python -m src.cli.main --check-capabilities

# 输出示例：
# TUSHARE: ✅ 可用 (真实数据)
# 数据源类型: Real Data Only
```

### ❓ 系统是否提供模拟数据用于测试？

**答案**: 不提供。AQUA采用"Real Data Only"设计理念。

**设计原因**:
- **数据一致性**: 确保开发和生产环境数据特征完全一致
- **质量保证**: 避免因模拟数据特征差异导致的策略问题
- **专业标准**: 符合专业金融工具的严格要求
- **开发质量**: 从第一天就适应真实数据源的特性

**替代方案**:
```bash
# 使用预览模式进行测试（不实际保存数据）
python -m src.cli.main collect 000001.SZ --preview

# 使用小数据量进行测试
python -m src.cli.main collect 000001.SZ --last-days 7
```

### ❓ TUSHARE Token设置后仍然连接失败

**可能原因**:
1. Token格式错误或包含额外字符
2. Token积分不足或已过期
3. 网络连接问题或防火墙阻拦
4. TUSHARE服务暂时不可用

**排查步骤**:
```bash
# 1. 检查环境变量是否正确设置
echo $TUSHARE_TOKEN  # macOS/Linux
echo %TUSHARE_TOKEN%  # Windows

# 2. 重新设置Token（注意去除多余空格）
export TUSHARE_TOKEN="your_correct_token"

# 3. 测试数据源连接
python -m src.cli.main --check-capabilities

# 4. 如果仍然失败，检查网络
ping tushare.pro
```

**注意**: 系统不会降级到其他数据源，确保数据源问题得到及时发现和解决。

---

## 性能问题

### ❓ 程序启动很慢

**症状**: 运行命令后等待时间长

**解决方案**:
```bash
# 1. 首次运行会慢一些，这是正常的
# 2. 后续运行应该在2秒内启动
# 3. 如果持续慢，检查网络连接

# 性能测试
time python -m src.cli.main collect 000001.SZ --preview
```

### ❓ 内存使用过高

**症状**: 系统内存不足或程序占用大量内存

**解决方案**:
```bash
# 1. 减少批量处理大小
python -m src.cli.main collect symbols --batch-size 50

# 2. 分批采集大量数据
python -m src.cli.main collect 000001.SZ --last-days 30
python -m src.cli.main collect 000001.SZ --last-days 30 --start-date 2024-01-01
```

### ❓ 个人电脑配置推荐

**推荐配置** (已针对个人环境优化):
```bash
# 默认配置就是个人环境优化的：
# - 批量大小: 100个标的
# - 并发数: 2线程  
# - 启动时间: <2秒
# - 内存增长: <20MB

# 如果电脑配置较低，可以进一步调整：
python -m src.cli.main collect symbols --batch-size 20 --concurrent 1
```

---

## 数据查看问题

### ❓ 数据保存在哪里？

**默认位置**: `data/aqua_dev.duckdb`

**查看方法**:
```bash
# 检查文件是否存在
ls -la data/aqua_dev.duckdb

# 查看文件大小
du -h data/aqua_dev.duckdb
```

### ❓ 如何用Python查看采集的数据？

**简单查看**:
```python
import duckdb

# 连接数据库
conn = duckdb.connect('data/aqua_dev.duckdb')

# 查看有哪些表
print("数据表:", conn.execute("SHOW TABLES").fetchall())

# 查看股票数据
result = conn.execute("""
    SELECT ts_code, trade_date, close 
    FROM stock_daily 
    LIMIT 5
""").fetchall()

for row in result:
    print(f"股票:{row[0]}, 日期:{row[1]}, 收盘价:{row[2]}")
```

### ❓ 如何导出数据到Excel？

**方法一: 使用pandas**
```python
import duckdb
import pandas as pd

conn = duckdb.connect('data/aqua_dev.duckdb')
df = conn.execute("SELECT * FROM stock_daily").df()
df.to_excel('股票数据.xlsx', index=False)
```

**方法二: 导出CSV再转换**
```python
# 导出CSV
df.to_csv('股票数据.csv', index=False)
# 然后用Excel打开CSV文件
```

---

## 高级功能问题

### ❓ 如何自定义采集模板？

**创建自定义模板** `my_template.yaml`:
```yaml
name: "我的股票组合"
description: "个人关注的股票列表"

symbols:
  - "000001.SZ"  # 平安银行
  - "600036.SH"  # 招商银行
  - "000858.SZ"  # 五粮液

source: "tushare"
data_type: "stock" 
frequency: "daily"
start_date: "2024-01-01"
```

**使用模板**:
```bash
python -m src.cli.main collect --config my_template.yaml
```

### ❓ 如何定时自动采集数据？

**macOS/Linux使用cron**:
```bash
# 编辑定时任务
crontab -e

# 添加任务：每个工作日早上9点采集
0 9 * * 1-5 cd /path/to/AQUA && python -m src.cli.main collect --template my_stocks

# 查看已设置的任务
crontab -l
```

**Windows使用任务计划程序**:
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器：工作日早上9点
4. 设置操作：启动程序 `python`
5. 添加参数：`-m src.cli.main collect --template my_stocks`
6. 设置起始于：AQUA项目目录

### ❓ 如何在其他Python项目中使用？

**作为模块导入**:
```python
import sys
sys.path.append('/path/to/AQUA')

from src.cli.services.collect_service import CollectService

# 创建服务实例
service = CollectService()

# 采集数据
result = service.collect_data(
    symbols=['000001.SZ'], 
    source='tushare',
    data_type='stock',
    freq='daily',
    preview=False
)

print(f"采集结果: {result}")
```

---

## 💡 还有其他问题？

### 排查步骤
1. **查看错误信息** - 仔细阅读报错内容
2. **检查基础环境** - Python版本、网络连接
3. **尝试预览模式** - 用 `--preview` 测试
4. **查看系统状态** - 用 `--check-capabilities` 检查

### 获取帮助
- **GitHub Issues**: 提交详细问题描述
- **查看日志**: 程序会在 `logs/` 目录生成日志文件
- **联系开发者**: 查看项目主页联系方式

### 问题反馈模板
```
**环境信息**:
- 操作系统: macOS 13.0
- Python版本: 3.11.2  
- 错误命令: python -m src.cli.main collect 000001.SZ

**错误现象**:
[描述具体的错误信息和现象]

**期望结果**:
[描述期望的正确结果]

**其他信息**:
[其他可能相关的信息]
```

---

*最后更新: 2025-07-30 | 如有问题遗漏，欢迎反馈补充*