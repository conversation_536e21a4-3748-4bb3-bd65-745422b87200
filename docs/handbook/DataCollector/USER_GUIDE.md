# AQUA数据采集完整使用指南

## 📋 目录
- [安装配置](#安装配置)
- [基础使用](#基础使用)  
- [高级功能](#高级功能)
- [配置文件](#配置文件)
- [数据查看](#数据查看)
- [最佳实践](#最佳实践)

---

## 安装配置

### 系统要求
- **Python**: 3.11+
- **操作系统**: macOS 10.15+ 或 Windows 10+
- **内存**: 建议4GB+
- **磁盘**: 至少500MB可用空间

### 安装步骤

#### 方式一：Git克隆（推荐）
```bash
git clone https://github.com/your-org/AQUA.git
cd AQUA
pip install -r requirements.txt
```

#### 方式二：直接下载
1. 下载项目压缩包并解压
2. 进入项目目录
3. 运行 `pip install -r requirements.txt`

### 多数据源配置

AQUA数据采集工具采用"Multi-Source Real Data Only"设计理念，支持三种真实数据源，100%使用真实数据，不提供任何模拟数据：

#### TUSHARE数据源配置（Web API）
```bash
# 临时设置
export TUSHARE_TOKEN="your_token_here"

# 永久设置（macOS/Linux）
echo 'export TUSHARE_TOKEN="your_token_here"' >> ~/.bashrc
source ~/.bashrc

# 永久设置（Windows）
setx TUSHARE_TOKEN "your_token_here"
```

**获取TUSHARE Token**：
1. 访问 [TUSHARE官网](https://tushare.pro/) 注册账户
2. 在个人中心获取Token
3. 按上述方法配置到环境变量

#### CSV数据源配置（本地文件）
```bash
# 设置CSV数据路径（可选，默认为data/csv/）
export CSV_DATA_PATH="/path/to/your/csv/data/"
```

**CSV数据源特性**：
- 支持FROMC2C格式期货数据
- 支持股票历史数据导入
- 本地文件，无网络依赖

#### MySQL数据源配置（数据库）
```bash
# 配置MySQL连接参数
export MYSQL_HOST="localhost"
export MYSQL_PORT="3306"  # 可选，默认3306
export MYSQL_USER="your_username"
export MYSQL_PASSWORD="your_password"
export MYSQL_DATABASE="your_database"
```

**MySQL数据源特性**：
- 支持现有MySQL数据库迁移
- 数据库到DuckDB的无缝转换
- 支持多种金融数据表结构

### 验证安装
```bash
python -m src.cli.main --help

# 看到帮助信息说明安装成功
```

---

## 基础使用

### 命令格式
```bash
python -m src.cli.main collect <标的代码> --source <数据源> [选项]
```

### 多数据源采集

#### 数据源能力检查（推荐先检查）
```bash
# 检查TUSHARE数据源能力
python -m src.cli.main collect --check-capabilities --source tushare

# 检查CSV数据源能力
python -m src.cli.main collect --check-capabilities --source csv

# 检查MySQL数据源能力
python -m src.cli.main collect --check-capabilities --source mysql
```

#### 预览模式（推荐先试试）
```bash
# TUSHARE数据源预览
python -m src.cli.main collect 000001.SZ --source tushare --preview

# CSV数据源预览
python -m src.cli.main collect futures_data --source csv --preview

# MySQL数据源预览
python -m src.cli.main collect stock_table --source mysql --preview

# 输出示例：
# 📊 数据预览 - Multi-Source Real Data Only
# 标的代码: 000001.SZ
# 数据源: TUSHARE/CSV/MYSQL (100%真实数据)
# 数据源类型: real_webapi/real_localfile/real_database
# 预计数据量: 250行 (约0.1MB)
# 时间范围: 最近30天
```

#### 实际采集
```bash
# TUSHARE数据源采集
python -m src.cli.main collect 000001.SZ --source tushare

# CSV数据源采集
python -m src.cli.main collect futures_data --source csv

# MySQL数据源采集
python -m src.cli.main collect stock_table --source mysql

# 成功输出：
# ✅ 数据采集成功！
# 📈 采集到 XXX 行数据
# 💾 保存位置: data/aqua_real_data.duckdb
# 🔒 数据源: TUSHARE/CSV/MYSQL (真实数据)
# ⏱️  用时: <2秒
```

### 多只股票采集
```bash
# 逗号分隔多个代码
python -m src.cli.main collect 000001.SZ,600036.SH,000002.SZ

# 采集银行股
python -m src.cli.main collect 000001.SZ,600036.SH,600000.SH,002142.SZ
```

### 指定时间范围
```bash
# 采集指定日期范围的数据
python -m src.cli.main collect 000001.SZ --start-date 2024-01-01 --end-date 2024-06-30

# 采集最近N天的数据
python -m src.cli.main collect 000001.SZ --last-days 30

# 采集最近N周的数据  
python -m src.cli.main collect 000001.SZ --last-weeks 4
```

### 期货数据采集
```bash
# 采集期货数据
python -m src.cli.main collect IF00.CFX --type futures

# 采集多个期货合约
python -m src.cli.main collect IF00.CFX,IC00.CFX,IH00.CFX --type futures
```

---

## 高级功能

### 使用预设模板
```bash
# 查看可用模板
python -m src.cli.main collect --list-templates

# 使用银行股模板
python -m src.cli.main collect --template bank_stocks_daily

# 使用期货主力合约模板
python -m src.cli.main collect --template futures_main_contracts
```

### 数据源管理
```bash
# 查看数据源状态
python -m src.cli.main --check-capabilities

# 指定数据源（仅支持tushare）
python -m src.cli.main collect 000001.SZ --source tushare

# ⚠️ 注意：系统仅支持tushare数据源
# 使用其他数据源会报错并提供说明
```

### 批量处理优化
```bash
# 调整批量大小（默认100）
python -m src.cli.main collect 000001.SZ,600036.SH --batch-size 50

# 调整并发数（默认2，个人环境推荐）
python -m src.cli.main collect 000001.SZ,600036.SH --concurrent 1
```

---

## 配置文件

### 创建配置文件
创建 `my_stocks.yaml`:
```yaml
# 基础配置
symbols:
  - "000001.SZ"  # 平安银行
  - "600036.SH"  # 招商银行
  - "600000.SH"  # 浦发银行

# 数据源配置
source: "tushare"
data_type: "stock"
frequency: "daily"

# 时间范围
start_date: "2024-01-01"
end_date: "2024-06-30"

# 输出配置
preview_only: false
save_format: "duckdb"
```

### 使用配置文件
```bash
python -m src.cli.main collect --config my_stocks.yaml
```

### 高级配置示例
创建 `advanced_config.yaml`:
```yaml
# 多类型数据采集
collections:
  - name: "银行股票"
    symbols: ["000001.SZ", "600036.SH"]
    type: "stock"
    frequency: "daily"
    
  - name: "股指期货"
    symbols: ["IF00.CFX", "IC00.CFX"]
    type: "futures"
    frequency: "daily"

# 性能配置
performance:
  batch_size: 100
  max_concurrent: 2
  timeout: 30

# 输出配置
output:
  database_path: "data/my_data.duckdb"
  backup_enabled: true
  log_level: "INFO"
```

---

## 数据查看

### 使用Python查看数据
```python
import duckdb

# 连接数据库
conn = duckdb.connect('data/aqua_dev.duckdb')

# 查看所有表
tables = conn.execute("SHOW TABLES").fetchall()
print("可用数据表:", tables)

# 查看股票数据
result = conn.execute("""
    SELECT ts_code, trade_date, close, vol 
    FROM stock_daily 
    WHERE ts_code = '000001.SZ' 
    ORDER BY trade_date DESC 
    LIMIT 10
""").fetchall()

for row in result:
    print(f"代码:{row[0]}, 日期:{row[1]}, 收盘:{row[2]}, 成交量:{row[3]}")
```

### 使用pandas分析数据
```python
import pandas as pd
import duckdb

conn = duckdb.connect('data/aqua_dev.duckdb')

# 读取为DataFrame
df = conn.execute("""
    SELECT * FROM stock_daily 
    WHERE ts_code = '000001.SZ' 
    ORDER BY trade_date
""").df()

# 基础分析
print(f"数据量: {len(df)}")
print(f"时间范围: {df['trade_date'].min()} 到 {df['trade_date'].max()}")
print(f"价格统计: 最高{df['high'].max()}, 最低{df['low'].min()}")

# 绘制K线图（需要安装matplotlib）
# df.set_index('trade_date')['close'].plot(title='股价走势')
```

### 导出数据
```python
import duckdb
import pandas as pd

conn = duckdb.connect('data/aqua_dev.duckdb')

# 导出为CSV
df = conn.execute("SELECT * FROM stock_daily").df()
df.to_csv('exported_data.csv', index=False)

# 导出特定股票数据
df_stock = conn.execute("""
    SELECT * FROM stock_daily 
    WHERE ts_code = '000001.SZ'
""").df()
df_stock.to_csv('ping_an_bank.csv', index=False)
```

---

## 最佳实践

### 👍 推荐做法

1. **先预览再采集**
   ```bash
   # 总是先用--preview检查
   python -m src.cli.main collect 000001.SZ --preview
   # 确认无误后再实际采集
   python -m src.cli.main collect 000001.SZ
   ```

2. **合理的批量大小**
   ```bash
   # 个人环境推荐10-20只股票一批
   python -m src.cli.main collect 000001.SZ,600036.SH,600000.SH
   ```

3. **使用配置文件管理复杂任务**
   ```bash
   # 复杂采集任务用配置文件
   python -m src.cli.main collect --config my_config.yaml
   ```

4. **定期备份数据**
   ```bash
   # 定期备份数据库文件
   cp data/aqua_dev.duckdb backup/aqua_backup_$(date +%Y%m%d).duckdb
   ```

### ❌ 避免的做法

1. **不要一次采集太多数据**
   ```bash
   # 避免：一次采集几百只股票
   # 推荐：分批采集，每批10-20只
   ```

2. **不要忽略错误信息**
   ```bash
   # 如果看到错误，及时查看FAQ或调整参数
   ```

3. **网络不稳定时的处理策略**
   ```bash
   # 网络不好时先使用预览模式测试连接
   python -m src.cli.main collect 000001.SZ --preview
   
   # 或者减少批量大小和并发数
   python -m src.cli.main collect 000001.SZ --batch-size 10 --concurrent 1
   
   # ⚠️ 注意：系统不会自动降级到Mock数据，确保数据来源真实可靠
   ```

### 🔧 性能优化建议

1. **个人环境推荐配置**
   ```bash
   # 适合个人电脑的配置
   python -m src.cli.main collect symbols --batch-size 50 --concurrent 1
   ```

2. **大量数据采集策略**
   ```bash
   # 分时段采集大量历史数据
   python -m src.cli.main collect 000001.SZ --start-date 2024-01-01 --end-date 2024-03-31
   python -m src.cli.main collect 000001.SZ --start-date 2024-04-01 --end-date 2024-06-30
   ```

3. **定时采集设置**
   ```bash
   # 可以结合cron实现定时采集（macOS/Linux）
   # 在crontab中添加：
   # 0 9 * * 1-5 cd /path/to/AQUA && python -m src.cli.main collect --template daily_stocks
   ```

---

## 🆘 下一步

- **遇到问题**: 查看 [FAQ.md](FAQ.md)
- **了解更多**: 查看项目主目录的技术文档
- **功能建议**: 提交GitHub Issue

---

*更新日期: 2025-07-30 | 版本: v1.0.0*