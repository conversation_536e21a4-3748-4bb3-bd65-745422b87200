#!/usr/bin/env python3
"""
测试AQUA模块导入 - Windows调试脚本
"""
import sys
import os
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).resolve().parent
src_dir = project_root / "src"
sys.path.insert(0, str(src_dir))

print(f"项目根目录: {project_root}")
print(f"src目录: {src_dir}")
print(f"src目录存在: {src_dir.exists()}")

# 测试各个模块导入
try:
    print("\n=== 测试导入 ===")
    
    print("1. 测试utils.config_loader...")
    from utils.config_loader import ConfigLoader
    print("✅ utils.config_loader导入成功")
    
    print("2. 测试aqua.cli.init_helpers...")
    from aqua.cli import init_helpers
    print("✅ aqua.cli.init_helpers导入成功")
    
    print("3. 测试aqua.cli.setup_wizard...")
    from aqua.cli.setup_wizard import setup_command
    print("✅ aqua.cli.setup_wizard导入成功")
    
    print("4. 测试aqua.main...")
    from aqua.main import app
    print("✅ aqua.main导入成功")
    
    print("\n🎉 所有模块导入成功！")
    
except ImportError as e:
    print(f"\n❌ 导入失败: {e}")
    print(f"Python路径: {sys.path[:3]}...")
    
    # 检查具体文件是否存在
    files_to_check = [
        src_dir / "utils" / "config_loader.py",
        src_dir / "aqua" / "__init__.py",
        src_dir / "aqua" / "main.py",
        src_dir / "aqua" / "cli" / "__init__.py",
        src_dir / "aqua" / "cli" / "setup_wizard.py"
    ]
    
    print("\n文件存在性检查:")
    for file_path in files_to_check:
        exists = file_path.exists()
        print(f"{'✅' if exists else '❌'} {file_path}")
        
except Exception as e:
    print(f"\n💥 其他错误: {e}")
    import traceback
    traceback.print_exc()