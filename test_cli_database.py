#!/usr/bin/env python3
"""
测试CLI使用的数据库路径
"""

import sys
import os
from pathlib import Path
sys.path.insert(0, 'src')

def test_cli_database():
    print("🔍 CLI数据库路径测试")
    print("=" * 40)
    
    try:
        # 测试配置加载器
        from utils.config_loader_v2 import ConfigLoaderV2
        config = ConfigLoaderV2()
        dev_config = config.get_config('dev')
        
        if 'database' in dev_config:
            db_path = dev_config['database'].get('path', '未设置')
            print(f"配置的数据库路径: {db_path}")
            
            # 检查路径是否正确
            expected_path = "D:/Data/duckdb/AQUA/DataCenter/aqua_dev.duckdb"
            if db_path == expected_path:
                print("✅ 数据库路径配置正确")
            else:
                print("❌ 数据库路径配置错误")
                print(f"期望: {expected_path}")
                print(f"实际: {db_path}")
            
            # 检查目录结构
            db_dir = Path(db_path).parent
            print(f"数据库目录: {db_dir}")
            print(f"目录存在: {db_dir.exists()}")
            
            return db_path == expected_path
        else:
            print("❌ 数据库配置不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_cli_database()
    if success:
        print("\n🎉 数据库路径配置修复成功！")
        print("正确的数据库位置: D:/Data/duckdb/AQUA/DataCenter/aqua_dev.duckdb")
    else:
        print("\n❌ 数据库路径配置仍有问题")
